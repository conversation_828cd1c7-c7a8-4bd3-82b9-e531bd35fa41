# Swarm SDK releases

## v3.5.19 <Badge text="beta" type="success" />
Released on February 2, 2021.

- Flow Router Refinitiv hardcoded batch configuration - [#156](https://github.com/swarmhub/swarm-sdk/pull/156)

## v3.5.18 <Badge text="beta" type="success" />
Released on February 1, 2021.
### :muscle: Enhancements

- central Settings object to centralize configurations - [#142](https://github.com/swarmhub/swarm-sdk/pull/142)

### :bug: Fixes

- [DE-442] Parse tenant configuration fetched with addict.Dict - [#148](https://github.com/swarmhub/swarm-sdk/pull/148)

### :pray: Contributors

- [<PERSON><PERSON><PERSON>](https://github.com/fsramalho)
- [<PERSON>](https://github.com/microft)

## v3.5.16 <Badge text="beta" type="success" />
Released on January 13, 2021.
### :muscle: Enhancements

- [DE-433] Handle prefect signals in transformBaseTask │ fix bug in task_audit.py [#136](https://github.com/swarmhub/swarm-sdk/pull/136)

### :pray: Contributors

- [Diogo-B-Lima](https://github.com/Diogo-B-Lima)

## v3.5.14 <Badge text="beta" type="success" />
Released on January 7, 2021.
### :muscle: Enhancements

- Improvement on schema publisher

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)

## v3.5.13 <Badge text="beta" type="success" />
Released on January 5, 2021.
### :muscle: Enhancements

- Moved FlowMeta to reusable file avoiding circular dependencies - [#125](https://github.com/swarmhub/swarm-sdk/pull/125)

### :pray: Contributors

- [Luis Braga](https://github.com/microft)

## 3.5.12 <Badge text="beta" type="success" />
Released on December 29, 2020.
### :muscle: Enhancements

- [DE-405] Add memory usage and aws env vars logging to all flows - [#119](https://github.com/swarmhub/swarm-sdk/pull/119)
- refactors the SDK's FlowRouter to use SeBatchJobSubmitter - [#122](https://github.com/swarmhub/swarm-sdk/pull/122)

### :bug: Fixes

- More fixes to schema publisher - [#121](https://github.com/swarmhub/swarm-sdk/pull/121)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)
- [Luis Braga](https://github.com/microft)
- [Luis Ferro](https://github.com/luis-ferro-se)

## v3.5.11 <Badge text="beta" type="success" />
Released on December 7, 2020.
### :muscle: Enhancements

- [DE-356] Secrets Store abstraction class - [#111](https://github.com/swarmhub/swarm-sdk/pull/111)

### :pray: Contributors

- [Luis Braga](https://github.com/microft)

## v3.5.10 <Badge text="beta" type="success" />
Released on November 24, 2020.
### :muscle: Enhancements

- Add SE Batch Job Submitter class - [#105](https://github.com/swarmhub/swarm-sdk/pull/105)
- [DE-264] Store duplicate record details on SinkFileAudit - [#107](https://github.com/swarmhub/swarm-sdk/pull/107)

### :pray: Contributors

- [David Haines](https://github.com/davidfhaines)
- [Luis Braga](https://github.com/microft)

## v3.5.9 <Badge text="beta" type="success" />
Released on November 4, 2020.
### :boom: Features

- [DE-297] Flow Workflow: handle flatten upstream tasks - [#99](https://github.com/swarmhub/swarm-sdk/pull/99)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)
## v3.5.7 <Badge text="beta" type="success" />
Released on November 2, 2020.
### :muscle: Enhancements

- User Policy: added support for "legacy" policy data structure- [#95](https://github.com/swarmhub/swarm-sdk/pull/95)

### :pray: Contributors

- [Stephen Salamida](https://github.com/sdsalamida)

## v3.5.6 <Badge text="beta" type="success" />
Released on October 23, 2020.
### :muscle: Enhancements

- [DE-280] Flow Workflow: flatten flow source schema- [#92](https://github.com/swarmhub/swarm-sdk/pull/92)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)

## v3.5.5 <Badge text="beta" type="success" />
Released on October 21, 2020.
### :bug: Fixes

- [DE-278] RecordHandler: fix `id` issues - [#89](https://github.com/swarmhub/swarm-sdk/pull/89)

### :pray: Contributors

- [Suresh Babu Angadi](https://github.com/sureshab)

## v3.5.4 <Badge text="beta" type="success" />
Released on October 16, 2020.


## v3.5.2 <Badge text="beta" type="success" />
Released on October 13, 2020.

### :bug: Fixes

- [SC-79] Fixed roles on session object for Api v1 - [#83](https://github.com/swarmhub/swarm-sdk/pull/83)
- [SC-79] Fixed uniqueProps validation on Bearer plugin - [#83](https://github.com/swarmhub/swarm-sdk/pull/83)


## v3.5.2 <Badge text="beta" type="success" />
Released on October 12, 2020.

### :muscle: Enhancements

- [SC-79] Update Schema for fixing encoded traits - [#80](https://github.com/swarmhub/swarm-sdk/pull/80)

### :bug: Fixes

- [SC-79] Fixed Bearer plugin `&ttlExpiry` - [#80](https://github.com/swarmhub/swarm-sdk/pull/80)
- [SC-79] Fixed Cognito user attributes and permissions - [#80](https://github.com/swarmhub/swarm-sdk/pull/80)

## v3.5.1 <Badge text="beta" type="success" />
Released on October 9, 2020.
### :muscle: Enhancements

- [SC-178] Swap pycryptodome instead of buggy pycrypto - [#77](https://github.com/swarmhub/swarm-sdk/pull/77)
- [SC-178] Revert .abaci for session storage - [#77](https://github.com/swarmhub/swarm-sdk/pull/77)

### :bug: Fixes

- Fix for Password hash to be cross compatible with se-data-server - [#77](https://github.com/swarmhub/swarm-sdk/pull/77)

### :pray: Contributors

- [Shankar Vasdevan](https://github.com/vshank77)

## v3.5.0 <Badge text="beta" type="success" />
Released on October 6, 2020.
### :boom: Features

- [SC-178] Implement SSO and Authentication Features - [#71](https://github.com/swarmhub/swarm-sdk/pull/71)
- [SC-83] Implement Schema Publisher - [#71](https://github.com/swarmhub/swarm-sdk/pull/71)

### :pray: Contributors

- [Shankar Vasdevan](https://github.com/vshank77)

## v3.4.46 <Badge text="beta" type="success" />
Released on October 5, 2020.
### :muscle: Enhancements

- [DE-263] Update Prefect to 0.13.9 - [#72](https://github.com/swarmhub/swarm-sdk/pull/72)

### :pray: Contributors

- [David Haines](https://github.com/davidfhaines)

## v3.4.45 <Badge text="beta" type="success" />
Released on August 26, 2020.
### :muscle: Enhancements

- Update Schema Range - [#66](https://github.com/swarmhub/swarm-sdk/pull/66)

### :pray: Contributors

- [David Haines](https://github.com/davidfhaines)

## v3.4.44 <Badge text="beta" type="success" />
Released on August 21, 2020.
### :boom: Features

- [DE-87] Fix indict hash for map fields - [#63](https://github.com/swarmhub/swarm-tasks/pull/63)

### :muscle: Enhancements

- [DE-198] Handle critical errors in FlowRouter and FlowRunner - [#61](https://github.com/swarmhub/swarm-sdk/pull/61)

### :bug: Fixes

- Fix for TransformBaseTask - [#62](https://github.com/swarmhub/swarm-sdk/pull/62)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)
- [Luis Braga](https://github.com/microft)
- [Suresh Babu Angadi](https://github.com/sureshab)

## v3.4.43 <Badge text="beta" type="success" />
Released on August 13, 2020.
### :muscle: Enhancements

- [DE-195] Update schema to v3.0.0b147 - [#58](https://github.com/swarmhub/swarm-sdk/pull/58)

### :pray: Contributors

- [David Haines](https://github.com/davidfhaines)

## v3.4.42 <Badge text="beta" type="success" />
Released on August 11, 2020.
### :muscle: Enhancements

- DE-146 Refactor to use infra resource configs from prefect context - [#45](https://github.com/swarmhub/swarm-sdk/pull/45)
- [DE-167] Update se-schema version to latest-3.0.0-beta.143 - [#52](https://github.com/swarmhub/swarm-sdk/pull/52)
- [DE-173] Update se-schema version to latest-3.0.0-beta.144 - [#53](https://github.com/swarmhub/swarm-sdk/pull/53)
- [DE-186] Indict: increase double precision - [#55](https://github.com/swarmhub/swarm-sdk/pull/55)

### :bug: Fixes

- [DE-146] Little refactoring of Workflow and FlowRunner - [#54](https://github.com/swarmhub/swarm-sdk/pull/54)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)
- [Luis Braga](https://github.com/microft)
- [Puneeth R](https://github.com/puneeth-r)

## v3.4.41 <Badge text="beta" type="success" />
Released on August 3, 2020.
### :muscle: Enhancements

- Updated se-schema beta 139 [DE-137] - [#38](https://github.com/swarmhub/swarm-sdk/pull/38)
- [DE-153] Updated se-schema beta 142 - [#48](https://github.com/swarmhub/swarm-sdk/pull/48)
- DE-154 Add retry logging to SFTP Client - [#49](https://github.com/swarmhub/swarm-sdk/pull/49)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)
- [Luis Braga](https://github.com/microft)

## v3.4.40 <Badge text="beta" type="success" />
Released on July 28, 2020.
### :muscle: Enhancements

- Updated se-schema beta 139 [DE-137] - [#38](https://github.com/swarmhub/swarm-sdk/pull/38)

### :pray: Contributors

- [Fabio Ramalho](https://github.com/fsramalho)

## v3.4.39 <Badge text="beta" type="success" />
Released on July 26, 2020.
### :muscle: Enhancements

- Support task parameter overrides - [#23](https://github.com/swarmhub/swarm-sdk/pull/23)
- Implement simple retry policy for SSHException on the SftpClient - [#34](https://github.com/swarmhub/swarm-sdk/pull/34)
- Enrich File Audit with more details - [#36](https://github.com/swarmhub/swarm-sdk/pull/36)

### :pray: Contributors

- [David Haines](https://github.com/davidfhaines)
- [Luis Braga](https://github.com/microft)
- [Suresh Babu Angadi](https://github.com/sureshab)
