from integration_wrapper.integration_aries_task_input import IntegrationAriesTaskInput
from pydantic import Field


class OrderIressBellPotterFixAriesTaskInput(IntegrationAriesTaskInput):
    """This feed does not require any other parameter than the default ones."""

    skip_tenant_level_instrument_search: bool = Field(
        default=True,
        description=(
            "If set to true, tenant level search will be skipped for instruments not found in SRP"
        ),
    )
    batch: int = Field(
        default=...,
        description=("Batch number of current batch."),
    )
    batch_total: int = Field(
        default=...,
        description=("Total number of batches."),
    )
    cache_parquet_uri: str = Field(
        default=...,
        description=("Cloud URI of the cache parquet file."),
    )
