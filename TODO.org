* STUFF TODO

** Global

*** TODO Add docstrings

** RecordHandler

*** TODO Derive more granular error types for record handler

*** TODO Add error handling where failures can occur (e.g. get()ing a non-existent id)

*** TODO Don't return addict from swarm search

*** TODO Add an interactive mode

*** TODO delete() params are inconsistent with update() and create()

*** TODO Instantiate Elasticsearch in swarm directly (to decouple other stuff from ES altogether)
