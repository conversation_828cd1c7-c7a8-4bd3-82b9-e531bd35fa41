import logging
import os

import pandas as pd
from se_core_tasks.currency.convert_minor_to_major import CastTo
from se_trades_tasks.order.static import add_prefix
from se_trades_tasks.order.static import ModelPrefix
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order_and_tr.static import PartyPrefix

from swarm_tasks.generic.currency.convert_minor_to_major import ConvertMinorToMajor
from swarm_tasks.generic.currency.convert_minor_to_major import (
    Params as ParamsConvertMinorToMajor,
)
from swarm_tasks.order.transformations.metatrader.mt5.mt5_deals_transformations import (
    MT5DealsTransformations,
)
from swarm_tasks.order.transformations.metatrader.mt5.static import calculate_qty
from swarm_tasks.order.transformations.metatrader.mt5.static import SourceDealsColumns
from swarm_tasks.order.transformations.metatrader.mt5.static import (
    SourceInstrumentColumns,
)
from swarm_tasks.order.transformations.metatrader.mt5.static import TempPartyIDs
from swarm_tasks.transform.concat.concat_attributes import ConcatAttributes
from swarm_tasks.transform.concat.concat_attributes import (
    Params as ParamsConcatAttributes,
)
from swarm_tasks.utilities.static import Delimiters

logger_ = logging.getLogger(__name__)


class MT5OandaDealsTransformations(MT5DealsTransformations):
    def get_temp_qty(self) -> pd.Series:
        """
        Auxiliary method; so it can be overwritten on Oanda overrides
        Divides SourceDealsColumns.VOLUME by 10000
        """
        ten_thousand = "__ten_thousand__"
        self.source_frame.loc[:, ten_thousand] = 10000
        return calculate_qty(
            df=self.source_frame,
            qty_attribute=ten_thousand,
            volume_attribute=SourceDealsColumns.VOLUME,
        )

    def populate_party_identifiers(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        :param df: pd.Dataframe
        :return: pd.Dataframe
        Overrides populate_party_identifiers() method from
        MT5OrderStateTransformations to add EXECUTING_ENTITY
        """
        df.loc[:, TempPartyIDs.CLIENT] = (
            PartyPrefix.ID + df.loc[:, SourceDealsColumns.OANDA_USER_ID]
        )
        exec_entity = self.executing_entity()
        df.loc[:, TempPartyIDs.EXECUTING_ENTITY] = f"{PartyPrefix.ID}{exec_entity}"
        return df

    @staticmethod
    def executing_entity() -> str:
        """
        :return: str
        Take the first subfield of the input file name delimieted by '-'
        (i.e., mt5-ogm-deals-2022-02-14.csv will be “ogm”)
        """
        source_file_name = os.getenv("SWARM_FILE_URL").split("/")[-1]
        first_name_sub_field = source_file_name.split("-")
        if len(first_name_sub_field) > 2:
            return first_name_sub_field[1]
        else:
            logger_.warning(
                f"Unable to fetch executing_entity from file_name. "
                f"Filename:{source_file_name}"
            )
        return ""

    def _execution_details_outgoing_order_addl_info(self) -> None:
        """
        :return: Populates EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO
        """
        gateway = "Gateway - " + self.source_frame.loc[
            :, SourceDealsColumns.GATEWAY
        ].astype("string")

        price = "Price Gateway - " + self.source_frame.loc[
            :, SourceDealsColumns.PRICE_GATEWAY
        ].astype("string")

        login = "Login - " + self.source_frame.loc[:, SourceDealsColumns.LOGIN].astype(
            "string"
        )

        mt5_symbol = "mt5_symbol - " + self.source_frame.loc[
            :, SourceDealsColumns.SYMBOL
        ].astype("string")

        fv_adj = "FV_adj - " + self.source_frame.loc[
            :, SourceDealsColumns.FAIR_VALUE_ADJUSTMENT
        ].astype("string")

        client_id = "Internal Client ID - " + self.source_frame.loc[
            :, SourceDealsColumns.OANDA_USER_ID
        ].astype("string")

        source_frame = pd.concat(
            [gateway, price, login, client_id, mt5_symbol, fv_adj], axis=1
        )

        return ConcatAttributes.process(
            source_frame=source_frame,
            params=ParamsConcatAttributes(
                target_attribute=OrderColumns.EXECUTION_DETAILS_OUTGOING_ORDER_ADDL_INFO,
                source_attributes=[
                    SourceDealsColumns.GATEWAY,
                    SourceDealsColumns.PRICE_GATEWAY,
                    SourceDealsColumns.LOGIN,
                    SourceDealsColumns.SYMBOL,
                    SourceDealsColumns.FAIR_VALUE_ADJUSTMENT,
                ],
                delimiter=Delimiters.COMMA_SPACE,
            ),
        )

    def _price_forming_data_price(self) -> None:
        """
        Populates PRICE_FORMING_DATA_PRICE
        [ON-3101] Override this method as per Oanda requirements to use the cost difference instead of sum
        """
        prefix_target_att = add_prefix(
            prefix=ModelPrefix.ORDER_STATE,
            attribute=OrderColumns.PRICE_FORMING_DATA_PRICE,
        )

        self.target_df.loc[:, prefix_target_att] = ConvertMinorToMajor.process(
            source_frame=pd.concat(
                [
                    pd.DataFrame(
                        data=self.source_frame.loc[:, SourceDealsColumns.PRICE].astype(
                            float
                        )
                        - self.source_frame.loc[
                            :, SourceDealsColumns.FAIR_VALUE_ADJUSTMENT
                        ]
                        .fillna(0.0)
                        .astype(float),
                        columns=[SourceDealsColumns.PRICE],
                    ),
                    self.source_frame.loc[:, SourceInstrumentColumns.C_BASE_CCY],
                ],
                axis=1,
            ),
            params=ParamsConvertMinorToMajor(
                source_price_attribute=SourceDealsColumns.PRICE,
                source_ccy_attribute=SourceInstrumentColumns.C_BASE_CCY,
                target_price_attribute=prefix_target_att,
                cast_to=CastTo.FLOAT,
            ),
        )[prefix_target_att]
