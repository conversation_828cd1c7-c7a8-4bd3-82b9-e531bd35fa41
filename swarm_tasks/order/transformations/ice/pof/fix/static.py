class SourceColumns:
    AVG_PX = "AvgPx"
    CLIENT_ID = "ClientID"
    CL_ORD_ID = "ClOrdID"
    EXEC_ID = "ExecID"
    LAST_PX = "LastPx"
    LAST_QTY = "LastQty"
    LEAVES_QTY = "LeavesQty"
    MULTI_LEG_REPORTING_TYPE = "MultiLegReportingType"
    ON_BEHALF_OF_LOCATION_ID = "OnBehalfOfLocationID"
    ON_BEHALF_OF_SUB_ID = "OnBehalfOfSubID"
    ORDER_ID = "OrderID"
    ORDER_QTY = "OrderQty"
    ORD_TYPE = "OrdType"
    POSITION_EFFECT = "PositionEffect"
    PRICE = "Price"
    SECONDARY_EXEC_ID = "SecondaryExecID"
    SENDER_COMP_ID = "SenderCompID"
    SIDE = "Side"
    STOP_PX = "StopPx"
    SYMBOL = "Symbol"
    TARGET_SUB_ID = "TargetSubID"
    TEXT = "Text"
    TIME_IN_FORCE = "TimeInForce"
    TRANSACT_TIME = "TransactTime"
    TRD_TYPE = "TrdType"
    FF_439 = "ff_439"
    FF_440 = "ff_440"
    FF_9018 = "ff_9018"
    FF_9066 = "ff_9066"
    FF_9092 = "ff_9092"
    FF_9121 = "ff_9121"
    FF_9139 = "ff_9139"
    FF_9520 = "ff_9520"
    FF_9521 = "ff_9521"
    FF_9522 = "ff_9522"
    FF_9523 = "ff_9523"
    FF_9524 = "ff_9524"
    FF_9525 = "ff_9525"
    FF_9701 = "ff_9701"
    FF_9704 = "ff_9704"
    FF_9705 = "ff_9705"


class TempColumns:
    BUY_SELL = "__buy_sell__"
    BUYER = "__buyer__"
    CLIENT = "__client__"
    COUNTERPARTY = "__counterparty__"
    EXECUTION_WITHIN_FIRM = "__execution_within_firm__"
    EXECUTING_ENTITY = "__executing_entity__"
    INVESTMENT_DECISION = "__investment_decision__"
    LAST_PX = "__last_px__"
    ORDER_ID = "__order_id__"
    ORD_TYPE = "__order_type__"
    PRICE = "__price__"
    SELLER = "__seller__"
    STOP_PX = "__stop_px__"
    SYMBOL = "__symbol__"
    TRADER = "__trader__"
    TRD_TYPE = "__trd_type__"


MARKET_SIDE = "Market Side"
DATA_SOURCE = "ICE POF Exchange"
