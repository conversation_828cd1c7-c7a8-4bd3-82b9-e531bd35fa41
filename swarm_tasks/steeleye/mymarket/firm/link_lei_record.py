import logging
import os
from typing import List

import pandas as pd
from prefect import context
from prefect.engine import signals
from pydantic import Field
from schema_sdk.steeleye_model.base.schema import SchemaExtra
from swarm.conf import Settings
from swarm.task.auditor import Auditor
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask

LEI_RECORD = "LeiRecord"


class Params(BaseParams):
    lei_column: str = Field(
        default="firmIdentifiers.lei",
        description="Column containing the LEI",
    )
    source_name_column: str = Field(
        ...,
        description="Column containing the name. If null, this will be replaced"
        "with the name from LeiRecord. If non-null, the value in the"
        "source will be retained",
    )
    target_name_column: str = Field(
        default="name",
        description="Target name column according to the MarketCounterparty" "schema",
    )
    drop_source_name_column: bool = Field(
        default=True,
        description="Drops the source name column at the end of the task if True",
    )


class LEIRecordCols:
    ENTITY_ENTITY_STATUS = "entity.entityStatus"
    ENTITY_HQ_ADDRESS_CITY = "entity.headquartersAddress.city"
    ENTITY_HQ_ADDRESS_COUNTRY = "entity.headquartersAddress.country"
    ENTITY_HQ_ADDRESS_LINE_1 = "entity.headquartersAddress.line1"
    ENTITY_HQ_ADDRESS_LINE_2 = "entity.headquartersAddress.line2"
    ENTITY_HQ_ADDRESS_POSTAL_CODE = "entity.headquartersAddress.postalCode"
    ENTITY_LEGAL_ADDRESS_CITY = "entity.legalAddress.city"
    ENTITY_LEGAL_ADDRESS_COUNTRY = "entity.legalAddress.country"
    ENTITY_LEGAL_ADDRESS_LINE_1 = "entity.legalAddress.line1"
    ENTITY_LEGAL_ADDRESS_LINE_2 = "entity.legalAddress.line2"
    ENTITY_LEGAL_ADDRESS_POSTAL_CODE = "entity.legalAddress.postalCode"
    ENTITY_LEGAL_NAME = "entity.legalName"
    LEI = "lei"
    REGISTRATION_REGISTRATION_STATUS = "registration.registrationStatus"

    @classmethod
    def required_columns(cls):
        return [
            cls.ENTITY_ENTITY_STATUS,
            cls.ENTITY_HQ_ADDRESS_CITY,
            cls.ENTITY_HQ_ADDRESS_COUNTRY,
            cls.ENTITY_HQ_ADDRESS_LINE_1,
            cls.ENTITY_HQ_ADDRESS_LINE_2,
            cls.ENTITY_HQ_ADDRESS_POSTAL_CODE,
            cls.ENTITY_LEGAL_ADDRESS_CITY,
            cls.ENTITY_LEGAL_ADDRESS_COUNTRY,
            cls.ENTITY_LEGAL_ADDRESS_LINE_1,
            cls.ENTITY_LEGAL_ADDRESS_LINE_2,
            cls.ENTITY_LEGAL_ADDRESS_POSTAL_CODE,
            cls.ENTITY_LEGAL_NAME,
            cls.LEI,
            cls.REGISTRATION_REGISTRATION_STATUS,
        ]

    @classmethod
    def columns_to_drop(cls):
        """Returns a list of LeiRecord columns which have to be dropped before exiting
        from the task. These columns include all the LeiRecord columns which have not
        been renamed"""
        return [
            cls.ENTITY_HQ_ADDRESS_LINE_1,
            cls.ENTITY_HQ_ADDRESS_LINE_2,
            cls.ENTITY_LEGAL_ADDRESS_LINE_1,
            cls.ENTITY_LEGAL_ADDRESS_LINE_2,
            cls.ENTITY_LEGAL_NAME,
            cls.LEI,
        ]


class MarketCounterpartyCols:
    DETAILS_FIRM_STATUS = "details.firmStatus"
    DETAILS_LEI_REGISTRATION_STATUS = "details.leiRegistrationStatus"
    FIRM_LOCATION_REGISTERED_ADDRESS_CITY = "firmLocation.registeredAddress.city"
    FIRM_LOCATION_REGISTERED_ADDRESS_COUNTRY = "firmLocation.registeredAddress.country"
    FIRM_LOCATION_REGISTERED_ADDRESS_ADDRESS = "firmLocation.registeredAddress.address"
    FIRM_LOCATION_REGISTERED_ADDRESS_POSTAL_CODE = (
        "firmLocation.registeredAddress.postalCode"
    )
    FIRM_LOCATION_TRADING_ADDRESS_CITY = "firmLocation.tradingAddress.city"
    FIRM_LOCATION_TRADING_ADDRESS_COUNTRY = "firmLocation.tradingAddress.country"
    FIRM_LOCATION_TRADING_ADDRESS_ADDRESS = "firmLocation.tradingAddress.address"
    FIRM_LOCATION_TRADING_ADDRESS_POSTAL_CODE = "firmLocation.tradingAddress.postalCode"
    FIRM_IDENTIFIERS_LEI = "firmIdentifiers.lei"
    NAME = "name"

    @classmethod
    def target_columns(cls):
        return [
            cls.DETAILS_FIRM_STATUS,
            cls.DETAILS_LEI_REGISTRATION_STATUS,
            cls.FIRM_LOCATION_REGISTERED_ADDRESS_CITY,
            cls.FIRM_LOCATION_REGISTERED_ADDRESS_COUNTRY,
            cls.FIRM_LOCATION_REGISTERED_ADDRESS_ADDRESS,
            cls.FIRM_LOCATION_REGISTERED_ADDRESS_POSTAL_CODE,
            cls.FIRM_LOCATION_TRADING_ADDRESS_CITY,
            cls.FIRM_LOCATION_TRADING_ADDRESS_COUNTRY,
            cls.FIRM_LOCATION_TRADING_ADDRESS_ADDRESS,
            cls.FIRM_LOCATION_TRADING_ADDRESS_POSTAL_CODE,
            cls.NAME,
        ]


LEI_RECORD_MARKET_COUNTERPARTY_MAP = {
    LEIRecordCols.ENTITY_LEGAL_ADDRESS_CITY: MarketCounterpartyCols.FIRM_LOCATION_REGISTERED_ADDRESS_CITY,
    LEIRecordCols.ENTITY_LEGAL_ADDRESS_COUNTRY: MarketCounterpartyCols.FIRM_LOCATION_REGISTERED_ADDRESS_COUNTRY,
    LEIRecordCols.ENTITY_LEGAL_ADDRESS_POSTAL_CODE: MarketCounterpartyCols.FIRM_LOCATION_REGISTERED_ADDRESS_POSTAL_CODE,
    LEIRecordCols.ENTITY_HQ_ADDRESS_CITY: MarketCounterpartyCols.FIRM_LOCATION_TRADING_ADDRESS_CITY,
    LEIRecordCols.ENTITY_HQ_ADDRESS_COUNTRY: MarketCounterpartyCols.FIRM_LOCATION_TRADING_ADDRESS_COUNTRY,
    LEIRecordCols.ENTITY_HQ_ADDRESS_POSTAL_CODE: MarketCounterpartyCols.FIRM_LOCATION_TRADING_ADDRESS_POSTAL_CODE,
    LEIRecordCols.REGISTRATION_REGISTRATION_STATUS: MarketCounterpartyCols.DETAILS_LEI_REGISTRATION_STATUS,
    LEIRecordCols.ENTITY_ENTITY_STATUS: MarketCounterpartyCols.DETAILS_FIRM_STATUS,
}


class LinkLeiRecord(TransformBaseTask):
    """
    This task receives a dataframe containing MarketCounterparty data. It links
    these records with SRP LeiRecord data based on the LEI. Fields such as
    the trading and registered address are fetched from SRP so they can be
    populated in the MarketCounterparty records.
    The task also populates the 'name' field as follows:
    If the source name field (say __name__) is null, it gets the legal name from
    the matched LEI record. If it is non-null, the same value is passed to the
    target.
    It returns a data frame with the fields in the source frame as well as the
    new fields from LeiRecord.

    NOTE: If name is still null after the lookup, the record is dropped!
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        **kwargs,
    ) -> pd.DataFrame:

        return self.process(
            source_frame=source_frame,
            params=params,
            logger=self.logger,
            auditor=self.auditor,
        )

    @classmethod
    def process(
        cls,
        source_frame: pd.DataFrame = None,
        params: Params = None,
        logger=context.get("logger"),
        auditor: Auditor = None,
    ) -> pd.DataFrame:

        if source_frame.empty or params.lei_column not in source_frame.columns:
            raise signals.SKIP(
                "Source frame empty or required lei column not present, returning empty DataFrame"
            )
        es_reference = Settings.connections.get("reference-data")
        df = source_frame.copy()

        df = cls._link_counterparty_lei_record(
            df=df,
            es_reference=es_reference,
            lei_column=params.lei_column,
            logger=logger,
            auditor=auditor,
        )

        # Replace the name value with the legal name from LeiRecord if it's null
        df = cls.replace_name_if_null(df=df, params=params)

        # Drop all the columns from LeiRecord. They are no longer needed
        unnecessary_lei_record_cols = (
            LEIRecordCols.columns_to_drop() + [params.source_name_column]
            if params.drop_source_name_column
            else LEIRecordCols.columns_to_drop()
        )
        df = df.drop(unnecessary_lei_record_cols, axis=1)

        return df.fillna(pd.NA)

    @classmethod
    def replace_name_if_null(cls, df: pd.DataFrame, params: Params) -> pd.DataFrame:
        """
        Replaces the values in a the params.source_name_column field of df by the value
        in LEIRecordCols.ENTITY_LEGAL_NAME if params.source_name_column is null. If not
        null, it keeps the same value. The results are added to a column params.target_name_column

        :param: df: Source data frame containing params.source_name_column and LEIRecordCols.ENTITY_LEGAL_NAME
        :param: params: Params instance
        :returns: A DataFrame with a new column params.target_name_column containing either params.source_name_column
                  (if not null) or LEIRecordCols.ENTITY_LEGAL_NAME  (if params.source_name_column is null)
        """
        # If name is present in the source file, return it. Otherwise replace it with the legalName from LeiRecord
        name_not_null_mask = df.loc[:, params.source_name_column].notnull()
        df.loc[name_not_null_mask, params.target_name_column] = df.loc[
            name_not_null_mask, params.source_name_column
        ]
        df.loc[~name_not_null_mask, params.target_name_column] = df.loc[
            ~name_not_null_mask, LEIRecordCols.ENTITY_LEGAL_NAME
        ]
        return df

    @classmethod
    def _link_counterparty_lei_record(
        cls,
        df: pd.DataFrame,
        es_reference,
        lei_column: str,
        logger: logging.Logger,
        auditor: Auditor,
    ) -> pd.DataFrame:
        """Links counterparty records to Lei Records. It retrieves records from
        the SRP LeiRecord model which have the same LEIs as the ones in the
        source frame df.
        :param: df: source_df containing records for a client
        :param: es_reference: ElasticsearchClient instance
        :param: lei_column: name of the field containing the LEI, to be searched in
                LeiRecord
        :param: logger: Logger for logging messages
        :param: auditor: Auditor to write into SinkFileAudit
        :returns: DataFrame containing records where the records were not found
                  in Elasticsearch
        """
        leis_list = df.loc[:, lei_column].dropna().unique().tolist()
        logger.info(f"Fetching {len(leis_list)} LEIs from {LEI_RECORD}")
        fetched_lei_records = cls._get_lei_records(
            leis=leis_list,
            es_reference=es_reference,
        )
        if fetched_lei_records.empty:
            audit_message = (
                f"Firm address and other fields could not be populated for {leis_list}"
            )
            auditor.add(audit_message)
            logger.warning(audit_message)
            # Add required columns in target df with value NA
            for col in LEIRecordCols.required_columns():
                df.loc[:, col] = pd.NA
            df = cls.get_counterparty_fields_from_lei_record_fields(df)
            return df

        df.loc[:, "__index__"] = df.index

        # Merge the source df with the LeiRecord df (left join), drop duplicates is
        # needed because the right df might have multiple records with the same LEI
        target = df.merge(
            fetched_lei_records,
            how="left",
            left_on=lei_column,
            right_on=LEIRecordCols.LEI,
        )
        # duplicated() and drop_duplicates() treat NAs as duplicates. To override this,
        # create a bool duplicated series
        dupes = target.duplicated(subset=[lei_column, LEIRecordCols.LEI])
        # And then wherever the source LEI column is null, set this series to False (not
        # a duplicate). Now, these values won't be filtered out.
        dupes[target[lei_column].isnull()] = False

        # merge automatically resets index. To get back the original index, we'd saved the original
        # index in a column, and we're assigning it back as the index
        target = target[~dupes].set_index("__index__")

        # Rename columns from LeiRecord to those expected by MarketCounterparty, get concatenated
        # address fields
        target = cls.get_counterparty_fields_from_lei_record_fields(target)

        return target

    @classmethod
    def get_counterparty_fields_from_lei_record_fields(
        cls, df: pd.DataFrame
    ) -> pd.DataFrame:
        """This method renames the LeiRecord fields so they have the correct name according
        to the MarketCounterparty schema. It also gets 2 address fields by concatenating
        the 'line 1' and 'line 2' of the LeiRecord's HQ and legal addresses.

        :param: df: data frame containing source and LeiRecord fields
        :returns: data frame with required MarketCounterparty fields
        """
        df = df.rename(columns=LEI_RECORD_MARKET_COUNTERPARTY_MAP)
        # Combine line1 and line2 in LeiRecord to get the address
        df.loc[:, MarketCounterpartyCols.FIRM_LOCATION_TRADING_ADDRESS_ADDRESS] = (
            df.loc[:, LEIRecordCols.ENTITY_HQ_ADDRESS_LINE_1].fillna("")
            + " "
            + df.loc[:, LEIRecordCols.ENTITY_HQ_ADDRESS_LINE_2].fillna("")
        )
        df.loc[:, MarketCounterpartyCols.FIRM_LOCATION_REGISTERED_ADDRESS_ADDRESS] = (
            df.loc[:, LEIRecordCols.ENTITY_LEGAL_ADDRESS_LINE_1].fillna("")
            + " "
            + df.loc[:, LEIRecordCols.ENTITY_LEGAL_ADDRESS_LINE_2].fillna("")
        )
        for col in [
            MarketCounterpartyCols.FIRM_LOCATION_TRADING_ADDRESS_ADDRESS,
            MarketCounterpartyCols.FIRM_LOCATION_REGISTERED_ADDRESS_ADDRESS,
        ]:
            df.loc[:, col] = df.loc[:, col].str.strip().replace("", pd.NA)
        return df

    @staticmethod
    def _get_lei_records(leis: List[str], es_reference) -> pd.DataFrame:
        """Gets LeiRecord records from SRP based for the list of LEIs in the
        argument.
        :param: leis: List of LEIs for which records need to be fetched form LeiRecord
        :param: es_reference: Elasticsearch client
        :returns: DataFrame containing matching records from LeiRecord for the LEI list
        """
        required_columns = LEIRecordCols.required_columns()
        if bool(int(os.getenv("USE_EFDH", "0"))):
            from se_elastic_schema.models.efdh.lei_record import LeiRecord

            lei_record_index = LeiRecord.get_elastic_index_alias(tenant="efdh")
        else:
            from se_schema.all_models import (
                LeiRecord,
            )

            lei_record_index = LeiRecord.schema().get(SchemaExtra.ALIAS)

        query = {
            "size": es_reference.SIZE,
            "_source": {"include": required_columns},
            "query": {
                "bool": {"filter": [{"term": {es_reference.meta.model: LEI_RECORD}}]}
            },
        }

        if len(leis) > es_reference.MAX_TERMS_SIZE:
            lei_chunks = [
                leis[ix : ix + es_reference.MAX_TERMS_SIZE]
                for ix in range(0, len(leis), es_reference.MAX_TERMS_SIZE)
            ]
            should = [{"terms": {LEIRecordCols.LEI: chunk}} for chunk in lei_chunks]

            query["query"]["bool"]["should"] = should
            query["query"]["bool"]["minimum_should_match"] = 1
        else:
            query["query"]["bool"]["filter"].append(
                {"terms": {LEIRecordCols.LEI: leis}}
            )

        result = es_reference.scroll(index=lei_record_index, query=query)

        # If any of the columns aren't present in the result, add them (value=NA)
        for col in required_columns:
            if col not in result:
                result[col] = pd.NA
        return result
