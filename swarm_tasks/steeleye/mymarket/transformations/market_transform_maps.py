from se_core_tasks.abstractions.transformations.transform_map import TransformMap

from swarm_tasks.steeleye.market_counterparty.transformations.aladdin import (
    aladdin_broker_firm_transformations,
)
from swarm_tasks.steeleye.mymarket.transformations.bp.person import (
    bp_person_transformations,
)

aladdin_broker_transform_map = TransformMap(
    default=aladdin_broker_firm_transformations.AladdinBrokerFirmTransformations,
    map={},
)

bp_person_transform_map = TransformMap(
    default=bp_person_transformations.BpPersonTransformations,
    map={},
)
