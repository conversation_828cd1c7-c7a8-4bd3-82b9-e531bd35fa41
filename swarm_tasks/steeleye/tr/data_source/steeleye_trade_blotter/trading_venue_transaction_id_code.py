from enum import Enum
from typing import Optional

import pandas as pd
from pydantic import Field
from se_elastic_schema.validators.steeleye.venues.venue_validator import VenueValidator
from swarm.task.base import BaseParams
from swarm.task.transform.base import TransformBaseTask


TRX_DTL_VENUE = "transactionDetails.venue"
TRX_REF_NO = "reportDetails.transactionRefNo"
TARGET_ATTRIBUTE = "reportDetails.tradingVenueTransactionIdCode"


class VenueTypeValidator(Enum):

    EEA_VENUES = "is_eea_venue"
    NON_EEA_VENUES = "is_non_eea_venue"
    SI_VENUES = "is_systematic_internaliser_venue"
    UK_VENUES = "is_uk_venue"


class Params(BaseParams):

    venue_type: str = Field(
        ...,
        description="Type of venue to validate against. Must be one of the keys from VenueTypeValidator enum.",
        example="UK_VENUES",
    )

    custom_execution_entity: Optional[str] = Field(
        None,
        description="Execution entity identifier. If provided along with `custom_venue`, "
        "records matching both will have their transaction reference number populated.",
    )
    custom_venue: Optional[str] = Field(
        None,
        description="Venue identifier to be matched in `transactionDetails.ultimateVenue`. "
        "Used together with `custom_execution_entity` for conditional population.",
    )


class TradingVenueTransactionIdCode(TransformBaseTask):
    """
    This task populates the reportDetails.tradingVenueTransactionIdCode field.
    params.venue_type is mapped to a validator through the VenueTypeValidator enum.
    If it exists, the result is the validator from
    se_elastic_schema.validators.steeleye.venues.venue_validator that will be used
    If we want report transactions not in UK_venues or from a custom_venue/entity, we can use the Params to
    populate reportDetails.tradingVenueTransactionIdCode

    Logic:
        1. if "transactionDetails.venue" passes the venue_type validation (ex: venue is in UK_venues.txt list):
        Populate "reportDetails.tradingVenueTransactionIdCode" with respective "reportDetails.transactionRefNo" value
        <https://steeleye.atlassian.net/browse/SPI-1684>
        2. If both `execution_entity` and `venue` parameters are provided, records where:
            - `EXECUTINGENTITYID` contains the `execution_entity` value (case-insensitive, word-boundary match)
            - `transactionDetails.ultimateVenue` equals the `venue` value
           will also have `reportDetails.tradingVenueTransactionIdCode` populated from `reportDetails.transactionRefNo`.
    """

    params_class = Params

    def execute(
        self,
        source_frame: pd.DataFrame,
        params: Params,
        **kwargs,
    ) -> pd.DataFrame:

        target = pd.DataFrame(index=source_frame.index)
        target[TARGET_ATTRIBUTE] = pd.NA
        venue_type_param = params.venue_type
        custom_executing_entity_param = params.custom_execution_entity
        venue_param = params.custom_venue
        venue_validator = VenueTypeValidator[venue_type_param]

        not_null_venue_mask = source_frame.loc[:, TRX_DTL_VENUE].notnull()

        valid_venue_mask = not_null_venue_mask & source_frame.loc[
            not_null_venue_mask, TRX_DTL_VENUE
        ].apply(lambda x: getattr(VenueValidator, venue_validator.value)(x))

        # Mask for Custom Executition EntityID and Venue
        # <https://steeleye.atlassian.net/browse/SPI-1684>
        valid_exec_venue_mask = pd.Series(False, index=source_frame.index)

        if custom_executing_entity_param is not None and venue_param is not None:
            execution_entity_mask = source_frame["EXECUTINGENTITYID"].str.contains(
                rf"\b{custom_executing_entity_param}\b",
                case=False,
                regex=True,
                na=False,
            )
            ultimate_venue_mask = (
                source_frame["transactionDetails.ultimateVenue"] == venue_param
            )
            valid_exec_venue_mask = execution_entity_mask & ultimate_venue_mask

        if not (valid_venue_mask.any() or valid_exec_venue_mask.any()):
            return target

        target.loc[valid_venue_mask, TARGET_ATTRIBUTE] = source_frame.loc[
            valid_venue_mask, TRX_REF_NO
        ]
        target.loc[valid_exec_venue_mask, TARGET_ATTRIBUTE] = source_frame.loc[
            valid_exec_venue_mask, TRX_REF_NO
        ]

        target = target.fillna(pd.NA)

        return target
