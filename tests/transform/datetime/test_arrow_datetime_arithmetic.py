import pandas as pd
import pytest
from pydantic import ValidationError

from swarm_tasks.transform.datetime.arrow_datetime_arithmetic import (
    ArrowDatetimeArithmetic,
)
from swarm_tasks.transform.datetime.arrow_datetime_arithmetic import Params


@pytest.fixture()
def source_frame_df_with_manipulator_column_date() -> pd.DataFrame:
    """Creates a sample source_frame data frame with a manipulator column (col used to add/subtract required
    datetime units to the source date attribute)"""
    df = pd.DataFrame(
        {
            "maturity_date": [
                "2021-03-11",
                "2021-03-11",
                "2021-03-11",
                "2021-03-11",
                "2021-03-31",
            ],
            "months_to_subtract": [1, 2, 0, 4, 1],
        }
    )
    return df


@pytest.fixture()
def source_frame_df_for_static_manipulator_date() -> pd.DataFrame:
    """Creates a sample source_frame data frame without a manipulator column"""
    df = pd.DataFrame(
        {
            "maturity_date": [
                "2021-03-25",
                "2021-03-26",
                "2021-03-27",
                "2021-03-30",
                "2021-03-31",
            ],
        }
    )
    return df


@pytest.fixture()
def source_frame_df_with_manipulator_column_datetime() -> pd.DataFrame:
    """Creates a sample source_frame data frame with an incrementor column (col used to add required
    datetime units to the source date attribute)"""
    df = pd.DataFrame(
        {
            "maturity_date": [
                # Leap day!
                "2020-02-29 15:31:18",
                "2021-03-11 15:31:18",
                "2021-03-11 15:31:18",
            ],
            "months_to_add": [1, 2, 0],
        }
    )
    return df


@pytest.fixture()
def source_frame_df_for_static_manipulator_datetime() -> pd.DataFrame:
    """Creates a sample source_frame data frame without a manipulator column"""
    df = pd.DataFrame(
        {
            "maturity_date": [
                "2021-03-25 15:31:18",
                "2021-03-26 15:31:18",
                # Leap day!
                "2020-02-29 15:31:18",
            ],
        }
    )
    return df


class TestArrowDatetimeArithmetic:
    """Class to hold all the test cases for ArrowDatetimeArithmetic"""

    def test_decrement_months_with_source_incrementor(
        self, source_frame_df_with_manipulator_column_date
    ):
        """Tests the task when source_manipulator_attribute is passed in the params, along with
        the decrement param (source_manipulator_unit='months')"""

        params = Params(
            source_attribute="maturity_date",
            source_attribute_format="YYYY-MM-DD",
            source_manipulator_attribute="months_to_subtract",
            source_manipulator_unit="months",
            target_attribute="expiry_date",
            target_attribute_format="YYYY-MM-DD",
            decrement=True,
        )

        expected = pd.DataFrame(
            {
                "expiry_date": [
                    "2021-02-11",
                    "2021-01-11",
                    "2021-03-11",
                    "2020-11-11",
                    "2021-02-28",
                ]
            }
        )

        task = ArrowDatetimeArithmetic(name="ArrowDatetimeArithmetic", params=params)
        result = task.execute(
            source_frame=source_frame_df_with_manipulator_column_date, params=params
        )
        assert result.equals(expected)

    def test_decrement_months_with_static_manipulator(
        self, source_frame_df_for_static_manipulator_date
    ):
        """Tests the task when static_manipulator is passed in the params, along with
        the decrement param (source_manipulator_unit='months')"""
        params = Params(
            source_attribute="maturity_date",
            source_attribute_format="YYYY-MM-DD",
            static_manipulator=1,
            source_manipulator_unit="months",
            target_attribute="expiry_date",
            target_attribute_format="YYYY-MM-DD",
            decrement=True,
        )

        expected = pd.DataFrame(
            {
                "expiry_date": [
                    "2021-02-25",
                    "2021-02-26",
                    "2021-02-27",
                    "2021-02-28",
                    "2021-02-28",
                ],
            }
        )

        task = ArrowDatetimeArithmetic(name="ArrowDatetimeArithmetic", params=params)
        result = task.execute(
            source_frame=source_frame_df_for_static_manipulator_date, params=params
        )
        assert result.equals(expected)

    def test_increment_years_datetime_with_source_manipulator(
        self, source_frame_df_with_manipulator_column_datetime
    ):
        """Tests the task when source_manipulator_attribute is passed in the params, along with
        the decrement param (source_manipulator_unit='months')"""

        params = Params(
            source_attribute="maturity_date",
            source_attribute_format="YYYY-MM-DD HH:mm:ss",
            source_manipulator_attribute="months_to_add",
            source_manipulator_unit="years",
            target_attribute="expiry_date",
            target_attribute_format="YYYY/MM/DD HH:mm:ss",
        )

        expected = pd.DataFrame(
            {
                "expiry_date": [
                    "2021/02/28 15:31:18",
                    "2023/03/11 15:31:18",
                    "2021/03/11 15:31:18",
                ],
            }
        )

        task = ArrowDatetimeArithmetic(name="ArrowDatetimeArithmetic", params=params)
        result = task.execute(
            source_frame=source_frame_df_with_manipulator_column_datetime, params=params
        )
        assert result.equals(expected)

    def test_increment_years_datetime_with_static_manipulator(
        self, source_frame_df_for_static_manipulator_datetime
    ):
        """Tests the task when static_manipulator is passed in the params, along with
        the decrement param (source_manipulator_unit='months')"""
        params = Params(
            source_attribute="maturity_date",
            source_attribute_format="YYYY-MM-DD HH:mm:ss",
            static_manipulator=1,
            source_manipulator_unit="years",
            target_attribute="expiry_date",
            target_attribute_format="YYYY-MM-DD HH:mm:ss",
        )

        expected = pd.DataFrame(
            {
                "expiry_date": [
                    "2022-03-25 15:31:18",
                    "2022-03-26 15:31:18",
                    "2021-02-28 15:31:18",
                ],
            }
        )

        task = ArrowDatetimeArithmetic(name="ArrowDatetimeArithmetic", params=params)
        result = task.execute(
            source_frame=source_frame_df_for_static_manipulator_datetime, params=params
        )
        assert result.equals(expected)

    def test_convert_datetime_with_both_source_manipulator_attribute_and_static_manipulator_raises_value_error(
        self,
    ):
        with pytest.raises(ValueError) as e:
            Params(
                source_attribute="maturity_date",
                source_attribute_format="YYYY-MM-DD",
                static_manipulator=1,
                source_manipulator_attribute="months_to_subtract",
                source_manipulator_unit="years",
                target_attribute="expiry_date",
                target_attribute_format="YYYY-MM-DD",
            )

        assert (
            e.value.errors()[0]["msg"]
            == "Please specify either source_manipulator_attribute or static_manipulator (but not both)"
        )

    def test_convert_datetime_with_neither_source_manipulator_attribute_or_static_manipulator_raises_value_error(
        self,
    ):
        with pytest.raises(ValueError) as e:
            Params(
                source_attribute="maturity_date",
                source_attribute_format="YYYY-MM-DD",
                source_manipulator_unit="years",
                target_attribute="expiry_date",
                target_attribute_format="YYYY-MM-DD",
            )

        assert (
            e.value.errors()[0]["msg"]
            == "Please specify either source_manipulator_attribute or static_manipulator (but not both)"
        )

    def test_invalid_source_manipulator_unit_value_error(self):
        with pytest.raises(ValidationError) as e:
            Params(
                source_attribute="maturity_date",
                source_attribute_format="YYYY-MM-DD",
                static_manipulator=1,
                source_manipulator_unit="yearsmonthsdays",
                target_attribute="expiry_date",
                target_attribute_format="YYYY-MM-DD",
            )

        assert (
            e.value.errors()[0]["msg"]
            == "value is not a valid enumeration member; permitted: 'months', 'quarters', 'seconds', 'weeks', 'years'"
        )

    def test_datetime_adder_subtractor(self):
        """Tests datetime_adder_subtractor static method"""
        assert (
            ArrowDatetimeArithmetic.datetime_adder_subtractor(
                "20210325", "YYYYMMDD", 2, "YYYY-MM-DD", "months"
            )
            == "2021-05-25"
        )
        # Leap day!
        assert (
            ArrowDatetimeArithmetic.datetime_adder_subtractor(
                "20200229", "YYYYMMDD", -1, "YYYY-MM-DD", "years"
            )
            == "2019-02-28"
        )
        assert (
            ArrowDatetimeArithmetic.datetime_adder_subtractor(
                "20210331", "YYYYMMDD", -1, "YYYY-MM-DD", "months"
            )
            == "2021-02-28"
        )
        assert (
            ArrowDatetimeArithmetic.datetime_adder_subtractor(
                "20210330", "YYYYMMDD", -1, "YYYY-MM-DD", "months"
            )
            == "2021-02-28"
        )
        assert (
            ArrowDatetimeArithmetic.datetime_adder_subtractor(
                "20200331", "YYYYMMDD", -1, "YYYY-MM-DD", "months"
            )
            == "2020-02-29"
        )
        assert (
            ArrowDatetimeArithmetic.datetime_adder_subtractor(
                "20200229", "YYYYMMDD", -1, "YYYY-MM-DD", "quarters"
            )
            == "2019-11-29"
        )
        assert (
            ArrowDatetimeArithmetic.datetime_adder_subtractor(
                "20200229", "YYYYMMDD", 2, "YYYY-MM-DD", "weeks"
            )
            == "2020-03-14"
        )
