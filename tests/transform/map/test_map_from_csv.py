from pathlib import Path

import pandas as pd
import pytest
from se_core_tasks.core.core_dataclasses import ExtractPathResult

from swarm_tasks.transform.map import map_from_csv

SCRIPT_PATH = Path(__file__).parent
FILE_PATH = ExtractPathResult(path=SCRIPT_PATH.joinpath(r"data/map_from_csv/test.csv"))


@pytest.fixture()
def source_dataframe() -> pd.DataFrame:
    """Represents a source dataframe parsed from an input csv file."""
    df = pd.DataFrame({"foo": ["A", "B", "C", pd.NA]})
    return df


class TestMapFromCsv:
    def test_map(self, source_dataframe: pd.DataFrame):
        params = map_from_csv.Params(
            source_attribute="foo",
            key_attribute="key",
            value_attribute="value",
            target_attribute="bar",
        )
        mapping = map_from_csv.MapFromCsv(name="test-map", params=params)
        outcome = mapping.execute(
            source_dataframe, params=params, extractor_result=FILE_PATH
        )
        expected = pd.DataFrame({"bar": ["1", "2", "3", pd.NA]})
        assert outcome.equals(expected)
