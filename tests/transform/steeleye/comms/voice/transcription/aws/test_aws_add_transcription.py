import shutil
from pathlib import Path
from typing import Dict

import pandas as pd
import pytest
from addict import addict
from prefect.engine.signals import SKIP
from swarm.conf import SettingsCls

from .conftest import transcription_full_bodies_dict
from swarm_tasks.transform.steeleye.comms.voice.transcription.aws_add_transcription import (
    AwsAddTranscription,
)
from swarm_tasks.transform.steeleye.comms.voice.transcription.aws_add_transcription import (
    Params,
)
from swarm_tasks.transform.steeleye.comms.voice.transcription.aws_add_transcription import (
    Resources,
)

SCRIPT_PATH = Path(__file__).parent
TRANSCRIPTION_DIR = Path(__file__).parent.parent
TMP_DIR = TRANSCRIPTION_DIR.joinpath("tmp_dir")
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
TMP_DIR_PATH = SCRIPT_PATH.joinpath("tmp_dir")
ES_RECORDS = TEST_FILES_DIR.joinpath(r"es_records.pkl")
EXPECTED_FRAME = TEST_FILES_DIR.joinpath(r"expected_frame.pkl")


def get_transcription_full_bodies_dict(path_key, **kwargs) -> Dict:
    return transcription_full_bodies_dict.get(path_key)


def get_es_records(**kwargs):
    return pd.read_pickle(ES_RECORDS)


def raise_exception():
    raise Exception


class TestAWSAddTranscription:
    def test_no_transcripts(self, mocker):
        mocker.patch.object(
            AwsAddTranscription, "_get_s3_transcription_list", return_value=[]
        )
        params = Params()
        task, resources = self._init_task(mocker, params)
        with pytest.raises(SKIP):
            task.execute(params=params, resources=resources)

    def test_no_records(self, mocker, get_transcription_list):
        mocker.patch.object(
            AwsAddTranscription,
            "_get_s3_transcription_list",
            return_value=get_transcription_list,
        )
        mocker.patch.object(
            AwsAddTranscription,
            "open_trancription_file",
            side_effect=get_transcription_full_bodies_dict,
        )
        mocker.patch.object(AwsAddTranscription, "_move_s3_transcribe_file")
        mocker.patch.object(AwsAddTranscription, "_delete_aws_transcribe_job")
        params = Params()
        mocker.patch.object(
            AwsAddTranscription, "_get_records", return_value=pd.DataFrame(data={})
        )
        task, resources = self._init_task(mocker, params)
        with pytest.raises(SKIP):
            task.execute(params=params, resources=resources)

    def test_raise_exception(self, mocker, get_transcription_list):
        mocker.patch.object(
            AwsAddTranscription,
            "_get_s3_transcription_list",
            return_value=get_transcription_list,
        )
        mocker.patch.object(
            AwsAddTranscription,
            "open_trancription_file",
            side_effect=get_transcription_full_bodies_dict,
        )
        mocker.patch.object(
            AwsAddTranscription,
            "_move_s3_transcribe_file",
            return_value=raise_exception,
        )
        params = Params()
        mocker.patch.object(
            AwsAddTranscription, "_get_records", return_value=pd.DataFrame(data={})
        )
        task, resources = self._init_task(mocker, params)
        with pytest.raises(Exception):
            task.execute(params=params, resources=resources)

    def test_skip_transcribed(self, mocker, get_transcription_list):
        mocker.patch.object(
            AwsAddTranscription,
            "_get_s3_transcription_list",
            return_value=get_transcription_list,
        )
        mocker.patch.object(
            AwsAddTranscription,
            "open_trancription_file",
            side_effect=get_transcription_full_bodies_dict,
        )
        mocker.patch.object(AwsAddTranscription, "_move_s3_transcribe_file")
        mocker.patch.object(AwsAddTranscription, "_delete_aws_transcribe_job")
        params = Params()
        mocker.patch.object(
            AwsAddTranscription, "_get_records", side_effect=get_es_records
        )
        task, resources = self._init_task(mocker, params)

        with pytest.raises(SKIP):
            task.execute(params=params, resources=resources)

    def test_end_to_end(self, mocker, get_transcription_list):
        mocker.patch.object(
            AwsAddTranscription,
            "_get_s3_transcription_list",
            return_value=get_transcription_list,
        )
        mocker.patch.object(
            AwsAddTranscription,
            "open_trancription_file",
            side_effect=get_transcription_full_bodies_dict,
        )
        mocker.patch.object(AwsAddTranscription, "_move_s3_transcribe_file")
        mocker.patch.object(AwsAddTranscription, "_delete_aws_transcribe_job")
        params = Params(**{"skip_transcribed": False})
        mocker.patch.object(
            AwsAddTranscription, "_get_records", side_effect=get_es_records
        )
        task, resources = self._init_task(mocker, params)

        result = task.execute(params=params, resources=resources)
        result_frame = pd.read_csv(SCRIPT_PATH.parent.joinpath(result[0].path))
        expected_frame = pd.read_pickle(EXPECTED_FRAME)

        assert not pd.testing.assert_frame_equal(result_frame, expected_frame)

        shutil.rmtree(result[0].path.parent.parent.parent)

    def test_end_to_end_dynamo(self, mocker, get_transcription_list):
        mocker.patch.object(
            AwsAddTranscription,
            "_get_dynamo_transcription_list",
            return_value=get_transcription_list,
        )
        mocker.patch.object(
            AwsAddTranscription,
            "open_trancription_file",
            side_effect=get_transcription_full_bodies_dict,
        )
        mocker.patch.object(AwsAddTranscription, "_delete_dynamo_path_record")
        mocker.patch.object(AwsAddTranscription, "_delete_aws_transcribe_job")
        mocker.patch.object(
            AwsAddTranscription, "_get_records", side_effect=get_es_records
        )

        params = Params(**{"read_from": "dynamo", "skip_transcribed": False})
        task, resources = self._init_task(mocker, params)

        result = task.execute(params=params, resources=resources)
        result_frame = pd.read_csv(SCRIPT_PATH.parent.joinpath(result[0].path))
        expected_frame = pd.read_pickle(EXPECTED_FRAME)

        assert not pd.testing.assert_frame_equal(result_frame, expected_frame)

        shutil.rmtree(result[0].path.parent.parent.parent)

    @staticmethod
    def _init_task(mocker, params):
        mock_connections = mocker.patch.object(
            SettingsCls, "connections", new_callable=mocker.PropertyMock
        )
        mock_connections.return_value = addict.Dict(
            {
                "tenant-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                    },
                    "MAX_TERMS_SIZE": 1024,
                }
            }
        )

        resources = Resources(**{"es_client_key": "tenant-data"})
        task = AwsAddTranscription(
            name="AwsAddTranscription", params=params, resources=resources
        )
        return task, resources
