��"      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�workflow.status��/instrumentDetails.instrument.instrumentFullName��-instrumentDetails.instrument.instrumentIdCode��
parties.buyer��.instrumentDetails.instrument.bond.maturityDate��+instrumentDetails.instrument.ext.venueInEEA��+parties.executingEntity.firmIdentifiers.lei��;instrumentDetails.instrument.derivative.optionExerciseStyle��2instrumentDetails.instrument.derivative.optionType��workflow.eligibility.eligible��;instrumentDetails.instrument.derivative.strikePriceCurrency��;instrumentDetails.instrument.derivative.underlyingIndexTerm��=instrumentDetails.instrument.derivative.underlyingInstruments��6instrumentDetails.instrument.ext.underlyingInstruments��;instrumentDetails.instrument.derivative.underlyingIndexName��0instrumentDetails.instrument.ext.strikePriceType��(instrumentDetails.instrument.ext.onFIRDS��2instrumentDetails.instrument.derivative.expiryDate��7instrumentDetails.instrument.derivative.priceMultiplier��.instrumentDetails.instrument.notionalCurrency1��5instrumentDetails.instrument.instrumentClassification��3instrumentDetails.instrument.derivative.strikePrice��4instrumentDetails.instrument.derivative.deliveryType��@instrumentDetails.instrument.derivative.underlyingIndexTermValue��<instrumentDetails.instrument.fxDerivatives.notionalCurrency2��workflow.eligibility.totv��transactionDetails.venue��reportDetails.reportStatus�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(hBN�start�K �stop�K	�step�Ku��R�e]�(hhK ��h��R�(KKK	��h�f8�����R�(K�<�NNNJ����J����K t�b�B        �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �?      �?      �?      �?      �?      �?      �?      �?      �?      �      �      �      �      Y@     @�@     ��@      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      ��t�bhhK ��h��R�(KKK	��h�b1�����R�(Kh"NNNJ����J����K t�b�C                  �t�bhhK ��h��R�(KKK	��h!�]�(�
REPORTABLE�hihihihihihihihi�Instrument Full Name�hjhjhjhjhjhjhjhj�0[{'firmIdentifiers': {'lei': 'BUYER_TEST_LEI'}}]�hkhkhkhkhkhkhkhk�EXECUTING_ENTITY_LEI�hlhlhlhlhlhlhlhlG�      G�      G�      G�      G�      G�      G�      G�      �USD��h[{'instrumentIdCode': 'ISIN_ONE'}, {'instrumentIdCode': 'ISIN_TWO'}, {'instrumentIdCode': 'ISIN_THREE'}]�hn�"[{'instrumentIdCode': 'ISIN_ONE'}]�hnhnhnhohnhn�Underlying Index Name�G�      G�      G�      hpG�      G�      G�      G�      �MntryVal�hqhqhq�PNDG�hrhrhrhr�
2027-12-31�hshshshshshshshs�USD�hthththththththt�FFICSX��SEIPXC��SEBPXC�hwhuhvhwhwhw�CASH�hxhxhxhxhxhxhxhx�XXXX�hyhyhyhyhyhyhyhy�NEWT�hzhzhzhzhzhzhzhzet�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h'h)h*h,h-h0h1h7h:h<h=et�bhBNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h.h5h>et�bhBNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h(h+h/h2h3h4h6h8h9h;h?h@et�bhBNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hQ�mgr_locs�hhK ��h��R�(KK��h�i8�����R�(KhVNNNJ����J����K t�b�CX                                                                             �t�bu}�(h�h\h�hhK ��h��R�(KK��h��C	                     �t�bu}�(h�hfh�hhK ��h��R�(KK��h��Cp                             
       
                                                               �t�bueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.