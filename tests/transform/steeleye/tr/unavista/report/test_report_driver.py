from pathlib import Path
from unittest.mock import patch
from unittest.mock import PropertyMock

import pandas as pd
import pytest
from addict import addict
from swarm.conf import SettingsCls

from swarm_tasks.transform.steeleye.tr.unavista.report.report_driver import Params
from swarm_tasks.transform.steeleye.tr.unavista.report.report_driver import ReportDriver
from swarm_tasks.transform.steeleye.tr.unavista.report.report_driver import Resources

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
CANC_TRANSACTION_REPORT = TEST_FILES_DIR.joinpath("df.csv")


@pytest.fixture()
def params():
    return Params(
        **{
            "chunksize": 5000,
            "submit_flow_prefix": "flows/tr-workflow-unavista-submit-report",
        }
    )


@pytest.fixture()
def resources():
    return Resources(**{"es_client_key": "tenant-data"})


class TestReportDriver:
    @patch.object(
        SettingsCls,
        "realm",
        new_callable=PropertyMock,
        return_value="test.dev.steeleye.co",
    )
    def test_with_canc_transactions(self, mocker, params, resources):
        es_mock = mocker.Mock()
        es_mock.meta.key = "&key"
        es_mock.meta.id = "&id"
        es_mock.client.info.return_value = {
            "version": {"number": "5.1.2"},
            "cluster_name": "elasticsearch",
        }

        es_mock.search.side_effect = es_client_search
        es_mock.scroll.side_effect = es_client_scroll
        es_mock.client.create.return_value = {
            "result": "created",
            "_index": "0",
            "_version": "1",
        }

        task = ReportDriver(name="TestReportDriverWithCANCs", params=params)
        task.clients = {"tenant-data": es_mock}

        flow_args = (
            '{"action": "overdue", "fromDate": "1500128444000", "toDate": "1700156429000", "user": "system", '
            '"lockDocId": null}'
        )
        task.execute(params=params, resources=resources, flow_args=flow_args)

        # Checking if the function is getting called if all the transactions are CANCs
        assert es_mock.client.create.called


def es_client_search(index: str, **kwargs):
    if index == "test-rts22_transaction-alias":
        return addict.Dict(
            {
                "took": 1,
                "timed_out": False,
                "_shards": {"total": 1, "successful": 1, "skipped": 0, "failed": 0},
                "hits": {"total": 1, "max_score": 0.0, "hits": []},
                "aggregations": {
                    "EXEC-ENTITY-LEI": {
                        "doc_count_error_upper_bound": 0,
                        "sum_other_doc_count": 0,
                        "buckets": [{"key": "TESTING123", "doc_count": 1}],
                    }
                },
            }
        )

    elif index == "test-account_firm-alias":
        return addict.Dict(
            {
                "hits": {
                    "hits": [
                        {
                            "_index": "test_dev_steeleye_co_accountfirm",
                            "_type": "AccountFirm",
                            "_id": "$this",
                            "_version": 1,
                            "found": True,
                            "_source": {
                                "&uniqueProps": ["lei:testing123"],
                                "&id": "$this",
                                "&traitFqn": "reference/firm",
                                "&key": "AccountFirm:$this:*************",
                                "uniqueIds": ["lei:testing123"],
                                "&model": "AccountFirm",
                                "name": "SteelEye",
                                "client": {
                                    "metFaceToFace": False,
                                    "isAggregatedClientAccount": False,
                                },
                                "emirDetails": {
                                    "isClearingThreshold": False,
                                    "corporateSector": ["F"],
                                    "natureOfFirm": "F",
                                },
                                "details": {
                                    "firmStatus": "ACTIVE",
                                    "inEEA": True,
                                    "retailOrProfessional": "N/A",
                                    "leiRegistrationStatus": "ISSUED",
                                    "parentOfCollectiveInvestmentSchema": False,
                                    "isEmirDelegatedReporting": False,
                                    "mifidRegistered": True,
                                },
                                "firmIdentifiers": {
                                    "lei": "testing123",
                                    "isIsda": False,
                                    "branchCountry": "GB",
                                    "deaAccess": False,
                                    "kycApproved": False,
                                },
                                "sinkIdentifiers": {
                                    "tradeFileIdentifiers": [
                                        {"id": "testing123", "label": "lei"}
                                    ]
                                },
                                "&timestamp": "*************",
                                "&user": "AKIAIG2C2VNWA5ZTLKEA",
                            },
                        }
                    ]
                }
            }
        )

    return {}


def es_client_scroll(index: str, **kwargs):
    if index == "test-rts22_transaction-alias":
        return pd.read_csv(CANC_TRANSACTION_REPORT)
    return pd.DataFrame()


def test_update_override_query():
    # Case-->1 Where Query has executingEntity Populated
    query_1 = {
        "query": {
            "bool": {
                "must_not": [
                    {"exists": {"field": "&expiry"}},
                    {"term": {"workflow.isReported": True}},
                ],
                "must": [
                    {"term": {"&model": "RTS22Transaction"}},
                    {"term": {"reportDetails.reportStatus": "CANC"}},
                    {
                        "terms": {
                            "workflow.status": [
                                "REPORTABLE",
                                "REPORTABLE_USER_OVERRIDE",
                            ]
                        }
                    },
                    {
                        "term": {
                            "parties.executingEntity.firmIdentifiers.lei": "123459875"
                        }
                    },
                ],
            }
        }
    }
    report_id_1 = {
        "report_id": "SteelEye_TRMiFIR_123459875.20241016065714",
        "count": 5,
        "filters": [
            {"term": {"parties.executingEntity.firmIdentifiers.lei": "123459875"}}
        ],
    }
    expected_result_1 = {
        "query": {
            "bool": {
                "must_not": [
                    {"exists": {"field": "&expiry"}},
                    {"term": {"workflow.isReported": True}},
                ],
                "must": [
                    {"term": {"&model": "RTS22Transaction"}},
                    {"term": {"reportDetails.reportStatus": "CANC"}},
                    {
                        "terms": {
                            "workflow.status": [
                                "REPORTABLE",
                                "REPORTABLE_USER_OVERRIDE",
                            ]
                        }
                    },
                    {
                        "term": {
                            "parties.executingEntity.firmIdentifiers.lei": "123459875"
                        }
                    },
                ],
            }
        }
    }

    # Case-->2 Where Query has executingEntity Not Populated
    query_2 = {
        "query": {
            "bool": {
                "must_not": [
                    {"exists": {"field": "&expiry"}},
                    {"term": {"workflow.isReported": True}},
                ],
                "must": [
                    {"term": {"&model": "RTS22Transaction"}},
                    {"term": {"reportDetails.reportStatus": "CANC"}},
                    {
                        "terms": {
                            "workflow.status": [
                                "REPORTABLE",
                                "REPORTABLE_USER_OVERRIDE",
                            ]
                        }
                    },
                ],
            }
        }
    }
    report_id_2 = {
        "report_id": "SteelEye_TRMiFIR_11223344.20241016065714",
        "count": 5,
        "filters": [
            {"term": {"parties.executingEntity.firmIdentifiers.lei": "11223344"}}
        ],
    }
    expected_result_2 = {
        "query": {
            "bool": {
                "must_not": [
                    {"exists": {"field": "&expiry"}},
                    {"term": {"workflow.isReported": True}},
                ],
                "must": [
                    {"term": {"&model": "RTS22Transaction"}},
                    {"term": {"reportDetails.reportStatus": "CANC"}},
                    {
                        "terms": {
                            "workflow.status": [
                                "REPORTABLE",
                                "REPORTABLE_USER_OVERRIDE",
                            ]
                        }
                    },
                    {
                        "term": {
                            "parties.executingEntity.firmIdentifiers.lei": "11223344"
                        }
                    },
                ],
            }
        }
    }

    result_1 = ReportDriver.update_override_query(query_1, report_id_1)
    result_2 = ReportDriver.update_override_query(query_2, report_id_2)
    assert expected_result_1 == result_1
    assert expected_result_2 == result_2
