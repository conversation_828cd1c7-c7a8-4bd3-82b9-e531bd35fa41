from unittest.mock import patch

import pandas as pd
from se_elastic_schema.validators.iso.isin import ISIN
from swarm.task.auditor import Auditor

from swarm_tasks.transform.steeleye.tr.identifiers.instrument import identifiers


class TestParams(object):
    def test_get_columns(self, input_asset_class_params_1):
        params = identifiers.Params(**input_asset_class_params_1)
        outcome = params.get_columns()
        expected = [
            "Asset Class",
            "Bloomberg FIGI ID",
            "Eurex ID",
            "Maturity Date",
            "__interest_rate_start_date__",
            "ISIN",
            "__notional_currency_1__",
            "Option Strike Price",
            "Option Type",
            "__swap_near_leg_date__",
            "Underlying Index Name",
            "__und_index_name_leg_2__",
            "__underlying_index_series__",
            "__underlying_symbol_expiry_code__",
            "Underlying Index Term",
            "__und_index_term_value__",
            "__underlying_index_version__",
            "Underlying Instrument ISIN/s",
            "Symbol",
            "__und_index_term_leg_2__",
            "__und_index_term_value_leg_2__",
            "Exchange MIC",
            "__venue_financial_instrument_short_name__",
            "__instrument_classification__",
            "Notional Currency 2",
            "Price Currency",
            "__exchange_symbol__",
        ]
        assert sorted(outcome) == sorted(expected)

    def test_get_columns_where_currencies_are_lists(self, input_asset_class_params_1):
        input_asset_class_params_1["currency_attribute"] = [
            "Price Currency",
            "Notional Currency 1",
        ]
        input_asset_class_params_1["notional_currency_2_attribute"] = [
            "Notional Currency 2",
            "Quantity Currency",
        ]
        params = identifiers.Params(**input_asset_class_params_1)
        outcome = params.get_columns()
        expected = [
            "Asset Class",
            "Bloomberg FIGI ID",
            "Eurex ID",
            "__exchange_symbol__",
            "Maturity Date",
            "__interest_rate_start_date__",
            "ISIN",
            "__notional_currency_1__",
            "Option Strike Price",
            "Option Type",
            "__swap_near_leg_date__",
            "Underlying Index Name",
            "__und_index_name_leg_2__",
            "__underlying_index_series__",
            "Underlying Index Term",
            "__und_index_term_value__",
            "__underlying_index_version__",
            "Underlying Instrument ISIN/s",
            "Symbol",
            "__und_index_term_leg_2__",
            "__und_index_term_value_leg_2__",
            "__underlying_symbol_expiry_code__",
            "Exchange MIC",
            "__venue_financial_instrument_short_name__",
            "__instrument_classification__",
            "Notional Currency 2",
            "Quantity Currency",
            "Price Currency",
            "Notional Currency 1",
        ]
        assert outcome == expected


class TestInstrumentIdentifiers:
    def test_empty_input_df_without_source_attribute_columns(
        self, input_asset_class_params_1
    ):
        df_empty = pd.DataFrame()
        params = identifiers.Params(**input_asset_class_params_1)
        mappings = identifiers.InstrumentIdentifiers(
            name="test-instr-identifiers", params=params
        )
        result = mappings.execute(source_frame=df_empty, params=params)
        assert result.empty

    def test_empty_input_df_with_source_attribute_columns(
        self, input_asset_class_params_1, input_source_df_1
    ):
        source_attributes = input_source_df_1.columns.to_list()
        df_no_data = pd.DataFrame(columns=source_attributes)

        params = identifiers.Params(**input_asset_class_params_1)
        mappings = identifiers.InstrumentIdentifiers(
            name="test-instr-identifiers", params=params
        )
        result = mappings.execute(source_frame=df_no_data, params=params)
        assert result.empty

    def test_input_df_with_a_single_record(
        self, input_asset_class_params_1, input_source_df_1, mocker
    ):
        mocked_isin_validator = mocker.patch.object(
            ISIN, "validate_isin_code", return_value=mocker.MagicMock(is_valid=True)
        )

        df_single_record = input_source_df_1.iloc[[1]]
        params = identifiers.Params(**input_asset_class_params_1)
        mappings = identifiers.InstrumentIdentifiers(
            name="test-instr-identifiers", params=params
        )
        outcome_df = mappings.execute(source_frame=df_single_record, params=params)

        mocked_isin_validator.assert_called()
        assert outcome_df.shape == (1, 1)
        assert outcome_df.columns.to_list() == ["marketIdentifiers.instrument"]
        assert (
            outcome_df.iloc[0].to_json()
            == '{"marketIdentifiers.instrument":[{"labelId":"XXXXUS0000000115CFD",'
            '"path":"instrumentDetails.instrument",'
            '"type":"OBJECT"},{"labelId":"XXXXUS0000000115JPYCFD",'
            '"path":"instrumentDetails.instrument","type":"OBJECT"}]}'
        )

    @patch.object(identifiers.InstrumentIdentifiers, "auditor")
    def test_retain_task_inputs(
        self,
        mock_auditor,
        interest_rate_swaps_process_data_source_frame: pd.DataFrame,
        input_params_interest_rate,
        expected_result_interest_rate_swaps_process_data,
    ) -> None:
        params = identifiers.Params(
            **input_params_interest_rate, retain_task_inputs=True
        )
        mappings = identifiers.InstrumentIdentifiers(
            name="test-instr-identifiers", params=params
        )
        mock_auditor.return_value = Auditor(task_name="Test")
        outcome_df = mappings.process(
            source_frame=interest_rate_swaps_process_data_source_frame,
            params=params,
            auditor=mock_auditor,
        )

        assert {
            "marketIdentifiers.instrument",
            "underlying_index_series_attribute",
            "bbg_figi_id_attribute",
            "underlying_index_version_attribute",
            "swap_near_leg_date_attribute",
            "underlying_index_term_value_attribute",
            "option_type_attribute",
            "underlying_symbol_attribute",
            "isin_attribute",
            "venue_attribute",
            "interest_rate_start_date_attribute",
            "underlying_isin_attribute",
            "asset_class_attribute",
            "eurex_id_attribute",
            "underlying_index_name_leg_2_attribute",
            "underlying_index_name_attribute",
            "expiry_date_attribute",
            "underlying_index_term_value_leg_2_attribute",
            "instrument_classification_attribute",
            "notional_currency_1_attribute",
            "currency_attribute",
            "notional_currency_2_attribute",
            "option_strike_price_attribute",
        } == set(outcome_df.columns) - set("marketIdentifiers.instrument")
