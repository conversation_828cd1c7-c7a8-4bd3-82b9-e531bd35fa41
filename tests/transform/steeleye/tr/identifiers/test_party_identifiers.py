from typing import Type

import pandas as pd
import pytest

from swarm_tasks.transform.steeleye.tr.identifiers import party_identifiers
from swarm_tasks.transform.steeleye.tr.identifiers.party_identifiers import Params


@pytest.fixture()
def params() -> Type[Params]:
    Params = party_identifiers.Params
    Params.target_attribute = "marketIdentifiers.parties"
    Params.buy_sell_side_attribute = "transactionDetails.buySellIndicator"
    Params.counterparty_identifier = "__counterparty_identifier__"
    Params.trader_identifier = "__trader_identifier__"
    Params.executing_entity_identifier = "__executing_entity_id__"
    Params.execution_within_firm_identifier = "__exec_within_firm__"
    Params.investment_decision_within_firm_identifier = "__investment_dec_within_firm__"
    Params.buyer_identifier = "__buyer_id__"
    Params.seller_identifier = "__seller_id__"
    Params.buyer_decision_maker_identifier = "__buyer_decision_maker__"
    Params.seller_decision_maker_identifier = "__seller_decision_maker__"
    Params.use_buy_mask_for_buyer_seller = False
    Params.use_buy_mask_for_buyer_seller_decision_maker = False
    return Params


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    creates empty dataframe
    """
    df = pd.DataFrame()
    return df


@pytest.fixture()
def all_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "transactionDetails.buySellIndicator": ["BUYI", "SELL", "BUYI"],
            "__executing_entity_id__": ["lei:exec_id", "lei:exec_id", pd.NA],
            "__exec_within_firm__": [
                "algo:exec_within_id",
                "algo:exec_within_id",
                pd.NA,
            ],
            "__investment_dec_within_firm__": [
                "algo:inv_dec_id",
                "algo:inv_dec_id",
                pd.NA,
            ],
            "__buyer_id__": ["id:buyer_id", "id:buyer_id", pd.NA],
            "__seller_id__": ["id:seller_id", "id:seller_id", pd.NA],
            "__buyer_decision_maker__": [
                "id:buy_dec_maker_id",
                "id:buy_dec_maker_id",
                pd.NA,
            ],
            "__seller_decision_maker__": [
                "id:sell_dec_maker_id",
                "id:sell_dec_maker_id",
                pd.NA,
            ],
        }
    )
    return df


class TestPartyIdentifiers(object):
    """
    Test cases for "TestPartyIdentifiers" class
    """

    def test_empty_input_df_without_source_columns(
        self, empty_source_df: pd.DataFrame, params: Params
    ):
        task = party_identifiers.PartyIdentifiers(name="test-parties", params=params)
        result = task.execute(empty_source_df, params)
        assert result.empty

    def test_all_col_in_source_df(
        self, all_col_in_source_df: pd.DataFrame, params: Params
    ):
        task = party_identifiers.PartyIdentifiers(name="test-parties", params=params)
        expected_result = pd.DataFrame(
            {
                "marketIdentifiers.parties": [
                    "[{'labelId': 'lei:exec_id', 'path': "
                    "'parties.executingEntity', 'type': "
                    "<IdentifierType.OBJECT>}, {'labelId': "
                    "'algo:exec_within_id', 'path': 'parties.executionWithinFirm', 'type': <IdentifierType.OBJECT>}, {'labelId': 'algo:inv_dec_id', 'path': 'parties.investmentDecisionWithinFirm', 'type': <IdentifierType.OBJECT>}, {'labelId': 'id:buyer_id', 'path': 'parties.buyer', 'type': <IdentifierType.ARRAY>}, {'labelId': 'id:seller_id', 'path': 'parties.seller', 'type': <IdentifierType.ARRAY>}, {'labelId': 'id:buy_dec_maker_id', 'path': 'parties.buyerDecisionMaker', 'type': <IdentifierType.ARRAY>}, {'labelId': 'id:sell_dec_maker_id', 'path': 'parties.sellerDecisionMaker', 'type': <IdentifierType.ARRAY>}]",
                    "[{'labelId': 'lei:exec_id', 'path': 'parties.executingEntity', "
                    "'type': <IdentifierType.OBJECT>}, {'labelId': 'algo:exec_within_id', 'path': 'parties.executionWithinFirm', 'type': <IdentifierType.OBJECT>}, {'labelId': 'algo:inv_dec_id', 'path': 'parties.investmentDecisionWithinFirm', 'type': <IdentifierType.OBJECT>}, {'labelId': 'id:buyer_id', 'path': 'parties.buyer', 'type': <IdentifierType.ARRAY>}, {'labelId': 'id:seller_id', 'path': 'parties.seller', 'type': <IdentifierType.ARRAY>}, {'labelId': 'id:buy_dec_maker_id', 'path': 'parties.buyerDecisionMaker', 'type': <IdentifierType.ARRAY>}, {'labelId': 'id:sell_dec_maker_id', 'path': 'parties.sellerDecisionMaker', 'type': <IdentifierType.ARRAY>}]",
                    pd.NA,
                ]
            }
        )

        result = task.execute(all_col_in_source_df, params)
        assert result.astype("str").equals(expected_result.astype("str"))

    def test_with_use_buy_mask_true(
        self, all_col_in_source_df: pd.DataFrame, params: Params
    ):
        params.use_buy_mask_for_buyer_seller = True
        params.use_buy_mask_for_buyer_seller_decision_maker = True
        task = party_identifiers.PartyIdentifiers(name="test-parties", params=params)
        expected_result = pd.DataFrame(
            {
                "marketIdentifiers.parties": [
                    "[{'labelId': 'lei:exec_id', 'path': "
                    "'parties.executingEntity', 'type': "
                    "<IdentifierType.OBJECT>}, {'labelId': "
                    "'algo:exec_within_id', 'path': 'parties.executionWithinFirm', 'type': <IdentifierType.OBJECT>}, {'labelId': 'algo:inv_dec_id', 'path': 'parties.investmentDecisionWithinFirm', 'type': <IdentifierType.OBJECT>}, {'labelId': 'id:buyer_id', 'path': 'parties.buyer', 'type': <IdentifierType.ARRAY>}, {'labelId': 'id:seller_id', 'path': 'parties.seller', 'type': <IdentifierType.ARRAY>}, {'labelId': 'id:buy_dec_maker_id', 'path': 'parties.buyerDecisionMaker', 'type': <IdentifierType.ARRAY>}, {'labelId': 'id:sell_dec_maker_id', 'path': 'parties.sellerDecisionMaker', 'type': <IdentifierType.ARRAY>}]",
                    "[{'labelId': 'lei:exec_id', 'path': 'parties.executingEntity', "
                    "'type': <IdentifierType.OBJECT>}, {'labelId': 'algo:exec_within_id', 'path': 'parties.executionWithinFirm', 'type': <IdentifierType.OBJECT>}, {'labelId': 'algo:inv_dec_id', 'path': 'parties.investmentDecisionWithinFirm', 'type': <IdentifierType.OBJECT>}, {'labelId': 'id:seller_id', 'path': 'parties.buyer', 'type': <IdentifierType.ARRAY>}, {'labelId': 'id:buyer_id', 'path': 'parties.seller', 'type': <IdentifierType.ARRAY>}, {'labelId': 'id:sell_dec_maker_id', 'path': 'parties.buyerDecisionMaker', 'type': <IdentifierType.ARRAY>}, {'labelId': 'id:buy_dec_maker_id', 'path': 'parties.sellerDecisionMaker', 'type': <IdentifierType.ARRAY>}]",
                    pd.NA,
                ]
            }
        )

        result = task.execute(all_col_in_source_df, params)
        assert result.astype("str").equals(expected_result.astype("str"))
