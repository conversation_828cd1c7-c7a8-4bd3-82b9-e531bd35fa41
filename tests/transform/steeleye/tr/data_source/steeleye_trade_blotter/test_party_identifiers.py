from typing import Optional
from typing import Type

import pandas as pd
import pytest
from addict import addict
from swarm.conf import Settings
from swarm.conf import SettingsCls
from swarm.task.base import BaseParams

from swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter import (
    party_identifiers,
)
from swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.party_identifiers import (
    DFColumns,
)
from swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.party_identifiers import (
    Params,
)
from swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.static import (
    SteelEyeTradeBlotterColumns,
)


@pytest.fixture()
def params() -> Type[party_identifiers.Params]:
    params = party_identifiers.Params
    params.firm_id = None
    params.override_discretionary = False
    params.override_non_discretionary = False
    params.override_non_lei_prefix = None
    return params


# Execution Within Firm tests


@pytest.fixture()
def exec_wf_all_data_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            DFColumns.CLIENT_MANDATE: ["DISCRETIONARY"] * 5,
            SteelEyeTradeBlotterColumns.INVESTMENT_DECISION_MAKER: [
                "Inv1",
                "Inv2",
                "Inv3",
                "Inv4",
                "Inv5",
            ],
            SteelEyeTradeBlotterColumns.TRADER_ID: [
                "TraderId1",
                "TraderId2",
                "TraderId3",
                "TraderId4",
                "TraderId5",
            ],
            "transactionDetails.tradingCapacity": ["Deal"] * 5,
            SteelEyeTradeBlotterColumns.EXECUTION_WITHIN_FIRM: [pd.NA] * 5,
        }
    )
    return df


@pytest.fixture()
def exec_wf_clnt_nore_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            DFColumns.CLIENT_MANDATE: ["DISCRETIONARY"] * 5,
            SteelEyeTradeBlotterColumns.INVESTMENT_DECISION_MAKER: [pd.NA] * 5,
            SteelEyeTradeBlotterColumns.TRADER_ID: [pd.NA] * 5,
            "transactionDetails.tradingCapacity": ["Deal"] * 5,
            SteelEyeTradeBlotterColumns.EXECUTION_WITHIN_FIRM: [pd.NA] * 5,
        }
    )
    return df


@pytest.fixture()
def exec_wf_partial_clnt_nore_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            DFColumns.CLIENT_MANDATE: ["DISCRETIONARY"] * 5,
            SteelEyeTradeBlotterColumns.INVESTMENT_DECISION_MAKER: [
                "Inv1",
                "Inv2",
                pd.NA,
                "Inv4",
                pd.NA,
            ],
            SteelEyeTradeBlotterColumns.TRADER_ID: [
                "TraderId1",
                pd.NA,
                pd.NA,
                "TraderId4",
                "TraderId5",
            ],
            "transactionDetails.tradingCapacity": ["Deal"] * 5,
            SteelEyeTradeBlotterColumns.EXECUTION_WITHIN_FIRM: [
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                "Exec1",
            ],
        }
    )
    return df


@pytest.fixture()
def source_frame_with_exec_wf() -> pd.DataFrame:
    # `source_frame` with `EXECUTION_WITHIN_FIRM` column for direct assignment
    return pd.DataFrame(
        {
            DFColumns.CLIENT_MANDATE: ["DISCRETIONARY"] * 3,
            SteelEyeTradeBlotterColumns.INVESTMENT_DECISION_MAKER: [
                "Inv1",
                "Inv2",
                pd.NA,
            ],
            SteelEyeTradeBlotterColumns.EXECUTION_WITHIN_FIRM: [
                "DirectAssign1",
                "DirectAssign2",
                "DirectAssign3",
            ],
            SteelEyeTradeBlotterColumns.TRADER_ID: ["TraderId1", "TraderId2", pd.NA],
            "transactionDetails.tradingCapacity": ["Deal", "Deal", "MATCH"],
        }
    )


@pytest.fixture()
def all_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "transactionDetails.buySellIndicator": ["BUYI", "SELL", "BUYI"],
            SteelEyeTradeBlotterColumns.CLIENT_ID: [
                "549300F17W4S6ZFU9W05",
                "213800Z2VDF3HH6JC714",
                pd.NA,
            ],
            SteelEyeTradeBlotterColumns.COUNTERPARTY_ID: [
                "UBS AG",
                "J.P. Morgan Securities " "plc",
                pd.NA,
            ],
            SteelEyeTradeBlotterColumns.EXECUTING_ENTITY_ID: [
                "input_id_1",
                "input_id_2",
                pd.NA,
            ],
            SteelEyeTradeBlotterColumns.INVESTMENT_DECISION_MAKER: [
                "549300O9HJEK4028LZ52",
                "549300O9HJEK4028LZ52",
                pd.NA,
            ],
            SteelEyeTradeBlotterColumns.TRADER_ID: ["asoulas", "asoulas", pd.NA],
            DFColumns.TRADING_CAPACITY: ["AOTC", "AOTC", pd.NA],
        }
    )
    return df


class TestParties(object):
    """
    Test cases for "TestParties" class
    """

    def test_exec_wf_all_data_with_deal_df(
        self,
        exec_wf_all_data_df: pd.DataFrame,
        params: Optional[BaseParams],
    ):
        task = party_identifiers.PartyIdentifiers(name="test-parties", params=params)
        expected_result = pd.Series(
            ["TraderId1", "TraderId2", "TraderId3", "TraderId4", "TraderId5"]
        )
        deal_mask = pd.Series(True, index=exec_wf_all_data_df.index)

        result = task._get_execution_within_firm(
            df=exec_wf_all_data_df, deal_mask=deal_mask
        )

        assert expected_result.equals(result)

    def test_exec_wf_all_data_no_deal_df(
        self,
        exec_wf_all_data_df: pd.DataFrame,
        params: Optional[BaseParams],
    ):
        task = party_identifiers.PartyIdentifiers(name="test-parties", params=params)
        expected_result = pd.Series(
            ["TraderId1", "TraderId2", "TraderId3", "TraderId4", "TraderId5"]
        )
        deal_mask = pd.Series(False, index=exec_wf_all_data_df.index)

        result = task._get_execution_within_firm(
            df=exec_wf_all_data_df, deal_mask=deal_mask
        )

        assert expected_result.equals(result)

    def test_exec_wf_clnt_nore_df(
        self,
        exec_wf_clnt_nore_df: pd.DataFrame,
        params: Optional[BaseParams],
    ):
        task = party_identifiers.PartyIdentifiers(name="test-parties", params=params)
        expected_result = pd.Series(["clnt:nore"] * 5)
        deal_mask = pd.Series(False, index=exec_wf_clnt_nore_df.index)

        result = task._get_execution_within_firm(
            df=exec_wf_clnt_nore_df, deal_mask=deal_mask
        )

        assert expected_result.equals(result)

    def test_exec_wf_partial_clnt_nore_df(
        self,
        exec_wf_partial_clnt_nore_df: pd.DataFrame,
        params: Optional[BaseParams],
    ):
        task = party_identifiers.PartyIdentifiers(name="test-parties", params=params)
        expected_result = pd.Series(
            ["TraderId1", "Inv2", "clnt:nore", "TraderId4", "Exec1"]
        )
        deal_mask = pd.Series(False, index=exec_wf_partial_clnt_nore_df.index)

        result = task._get_execution_within_firm(
            df=exec_wf_partial_clnt_nore_df, deal_mask=deal_mask
        )

        assert expected_result.equals(result)

    def test_all_col_in_source_df(
        self, all_col_in_source_df: pd.DataFrame, params: Params, mocker
    ):
        Settings.STACK = "dev-shared-2"
        Settings.FLOW_ID = "dummy.dev.steeleye.co:tr-universal-steeleye-trade-blotter"
        mock_connections = mocker.patch.object(
            SettingsCls, "connections", new_callable=mocker.PropertyMock
        )
        mock_connections.return_value = addict.Dict(
            {
                "tenant-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                        "expiry": "&expiry",
                    },
                    "MAX_TERMS_SIZE": 1024,
                }
            }
        )
        self.mock_get_tenant_lei(mocker, all_col_in_source_df)

        task = party_identifiers.PartyIdentifiers(name="test-parties", params=params)
        expected_result = pd.DataFrame(
            {
                "marketIdentifiers.parties": [
                    "[{'labelId': 'lei:549300f17w4s6zfu9w05', 'path': 'parties.buyer', 'type': <IdentifierType.ARRAY>}, {'labelId': 'account:input_id_1', 'path': 'parties.buyerDecisionMaker', 'type': <IdentifierType.ARRAY>}, {'labelId': 'account:input_id_1', 'path': 'parties.executingEntity', 'type': <IdentifierType.OBJECT>}, {'labelId': 'account:ubs ag', 'path': 'parties.seller', 'type': <IdentifierType.ARRAY>}, {'labelId': 'account:ubs ag', 'path': 'parties.counterparty', 'type': <IdentifierType.OBJECT>}, {'labelId': 'lei:549300o9hjek4028lz52', 'path': 'parties.investmentDecisionWithinFirm', 'type': <IdentifierType.OBJECT>}, {'labelId': 'account:asoulas', 'path': 'parties.executionWithinFirm', 'type': <IdentifierType.OBJECT>}, {'labelId': 'account:asoulas', 'path': 'parties.trader', 'type': <IdentifierType.ARRAY>}]",
                    "[{'labelId': 'account:j.p. morgan securities plc', 'path': 'parties.buyer', 'type': <IdentifierType.ARRAY>}, {'labelId': 'account:input_id_2', 'path': 'parties.executingEntity', 'type': <IdentifierType.OBJECT>}, {'labelId': 'lei:213800z2vdf3hh6jc714', 'path': 'parties.seller', 'type': <IdentifierType.ARRAY>}, {'labelId': 'account:input_id_2', 'path': 'parties.sellerDecisionMaker', 'type': <IdentifierType.ARRAY>}, {'labelId': 'account:j.p. morgan securities plc', 'path': 'parties.counterparty', 'type': <IdentifierType.OBJECT>}, {'labelId': 'lei:549300o9hjek4028lz52', 'path': 'parties.investmentDecisionWithinFirm', 'type': <IdentifierType.OBJECT>}, {'labelId': 'account:asoulas', 'path': 'parties.executionWithinFirm', 'type': <IdentifierType.OBJECT>}, {'labelId': 'account:asoulas', 'path': 'parties.trader', 'type': <IdentifierType.ARRAY>}]",
                    "[{'labelId': 'account:account_firm_id', 'path': 'parties.executingEntity', 'type': <IdentifierType.OBJECT>}, {'labelId': 'clnt:nore', 'path': 'parties.executionWithinFirm', 'type': <IdentifierType.OBJECT>}]",
                ]
            }
        )

        mock_client_map = mocker.patch.object(
            party_identifiers.PartyIdentifiers,
            "_get_client_map",
        )
        mock_client_map.return_value = {"intc": "DISCRETIONARY"}

        result = task.execute(
            source_frame=all_col_in_source_df,
            params=params,
        )
        assert result.astype("str").equals(expected_result.astype("str"))

    def test_all_col_in_source_df_no_tenant_lei_hit(
        self, all_col_in_source_df: pd.DataFrame, params: Params, mocker
    ):
        Settings.STACK = "dev-shared-2"
        Settings.FLOW_ID = "dummy.dev.steeleye.co:tr-universal-steeleye-trade-blotter"
        mock_connections = mocker.patch.object(
            SettingsCls, "connections", new_callable=mocker.PropertyMock
        )
        mock_connections.return_value = addict.Dict(
            {
                "tenant-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                        "expiry": "&expiry",
                    },
                    "MAX_TERMS_SIZE": 1024,
                }
            }
        )

        task = party_identifiers.PartyIdentifiers(name="test-parties", params=params)
        expected_result = pd.DataFrame(
            {
                "marketIdentifiers.parties": [
                    "[{'labelId': 'lei:549300f17w4s6zfu9w05', 'path': 'parties.buyer', 'type': <IdentifierType.ARRAY>}, {'labelId': 'account:input_id_1', 'path': 'parties.buyerDecisionMaker', 'type': <IdentifierType.ARRAY>}, {'labelId': 'account:input_id_1', 'path': 'parties.executingEntity', 'type': <IdentifierType.OBJECT>}, {'labelId': 'account:ubs ag', 'path': 'parties.seller', 'type': <IdentifierType.ARRAY>}, {'labelId': 'account:ubs ag', 'path': 'parties.counterparty', 'type': <IdentifierType.OBJECT>}, {'labelId': 'lei:549300o9hjek4028lz52', 'path': 'parties.investmentDecisionWithinFirm', 'type': <IdentifierType.OBJECT>}, {'labelId': 'account:asoulas', 'path': 'parties.executionWithinFirm', 'type': <IdentifierType.OBJECT>}, {'labelId': 'account:asoulas', 'path': 'parties.trader', 'type': <IdentifierType.ARRAY>}]",
                    "[{'labelId': 'account:j.p. morgan securities plc', 'path': 'parties.buyer', 'type': <IdentifierType.ARRAY>}, {'labelId': 'account:input_id_2', 'path': 'parties.executingEntity', 'type': <IdentifierType.OBJECT>}, {'labelId': 'lei:213800z2vdf3hh6jc714', 'path': 'parties.seller', 'type': <IdentifierType.ARRAY>}, {'labelId': 'account:input_id_2', 'path': 'parties.sellerDecisionMaker', 'type': <IdentifierType.ARRAY>}, {'labelId': 'account:j.p. morgan securities plc', 'path': 'parties.counterparty', 'type': <IdentifierType.OBJECT>}, {'labelId': 'lei:549300o9hjek4028lz52', 'path': 'parties.investmentDecisionWithinFirm', 'type': <IdentifierType.OBJECT>}, {'labelId': 'account:asoulas', 'path': 'parties.executionWithinFirm', 'type': <IdentifierType.OBJECT>}, {'labelId': 'account:asoulas', 'path': 'parties.trader', 'type': <IdentifierType.ARRAY>}]",
                    "[{'labelId': 'clnt:nore', 'path': 'parties.executionWithinFirm', 'type': <IdentifierType.OBJECT>}]",
                ]
            }
        )

        mock_client_map = mocker.patch.object(
            party_identifiers.PartyIdentifiers,
            "_get_client_map",
        )
        mock_client_map.return_value = {"intc": "DISCRETIONARY"}

        result = task.execute(
            source_frame=all_col_in_source_df,
            params=params,
        )
        assert result.astype("str").equals(expected_result.astype("str"))

    def test_df_with_execution_within_firm(
        self, source_frame_with_exec_wf: pd.DataFrame, params: Params
    ):
        task = party_identifiers.PartyIdentifiers(name="test-parties", params=params)
        deal_mask = (
            source_frame_with_exec_wf["transactionDetails.tradingCapacity"] == "Deal"
        )

        result = task._get_execution_within_firm(
            df=source_frame_with_exec_wf,
            deal_mask=deal_mask,
        )

        expected_result = pd.Series(
            ["DirectAssign1", "DirectAssign2", "DirectAssign3"],
            index=source_frame_with_exec_wf.index,
            name=SteelEyeTradeBlotterColumns.EXECUTION_WITHIN_FIRM,
        )
        pd.testing.assert_series_equal(result, expected_result)

    @staticmethod
    def mock_get_tenant_lei(mocker, test_df: pd.DataFrame):
        mock_get_tenant_lei = mocker.patch.object(
            party_identifiers, "run_get_tenant_lei"
        )
        tenant_lei_df = pd.DataFrame(index=test_df.index)
        tenant_lei_df = tenant_lei_df.assign(
            **{DFColumns.TENANT_LEI: "account_firm_id"}
        )
        mock_get_tenant_lei.return_value = tenant_lei_df
        return mock_get_tenant_lei
