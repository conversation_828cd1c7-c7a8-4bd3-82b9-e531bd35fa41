import pandas as pd
import pytest

from swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.static import (
    SteelEyeTradeBlotterColumns,
)
from swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.transaction_ref_no import (
    Params,
)
from swarm_tasks.transform.steeleye.tr.data_source.steeleye_trade_blotter.transaction_ref_no import (
    TransactionRefNo,
)


@pytest.fixture()
def transaction_id_frame() -> pd.DataFrame:
    """
    Represents a source dataframe parsed from an input csv file.
    """
    df = pd.DataFrame(
        {
            SteelEyeTradeBlotterColumns.ORDER_ID: ["o1", "o2", pd.NA, pd.NA, pd.NA],
            SteelEyeTradeBlotterColumns.TRADE_ID: ["t1", pd.NA, "t3", "t4", pd.NA],
            SteelEyeTradeBlotterColumns.TRADE_DATE: [
                "2020/01/01",
                "2020/01/01",
                "2020/01/01",
                pd.NA,
                "2020/01/01",
            ],
        }
    )
    return df


@pytest.fixture()
def transaction_ref_no_frame() -> pd.DataFrame:
    """
    Represents a source dataframe parsed from an input csv file.
    """
    df = pd.DataFrame(
        {
            SteelEyeTradeBlotterColumns.ORDER_ID: [
                "o1",
                pd.NA,
                "o3",
                pd.NA,
                "o5reallylongtestcaseohmygod",
            ],
            SteelEyeTradeBlotterColumns.TRADE_ID: [
                "t1",
                pd.NA,
                "t3",
                pd.NA,
                "t5reallylongtestcaseohmygod",
            ],
            "transactionDetails.buySellIndicator": [
                "BUYI",
                "BUYI",
                "BUYI",
                pd.NA,
                "BUYI",
            ],
            "transactionDetails.tradingDateTime": [
                "2020/01/01",
                "2020/01/01",
                pd.NA,
                pd.NA,
                "2020/01/01",
            ],
        }
    )
    return df


@pytest.fixture()
def transaction_ref_no_with_outbound_time() -> pd.DataFrame:
    return pd.DataFrame(
        {
            SteelEyeTradeBlotterColumns.ORDER_ID: [
                "o1",
            ],
            SteelEyeTradeBlotterColumns.TRADE_ID: [
                "t1",
            ],
            "transactionDetails.buySellIndicator": [
                "BUYI",
            ],
            "transactionDetails.tradingDateTime": [
                "9999/01/01",
            ],
        }
    )


class TestTransactionRefNo:
    def test_make_transaction_id(self, transaction_id_frame: pd.DataFrame):
        mapping = TransactionRefNo(name="test-transaction-ref-no")
        outcome_df = mapping._make_transaction_id(
            transaction_id_frame, params=Params(**{})
        )
        expected_df = pd.Series(["o1|t1", "o2", "t3|2020/01/01", "t4"])
        assert outcome_df.equals(expected_df)

    def test_transaction_ref_no(self, transaction_ref_no_frame: pd.DataFrame):
        mapping = TransactionRefNo(name="test-transaction-ref-no", params=Params(**{}))
        outcome_df = mapping.execute(transaction_ref_no_frame, params=Params(**{}))
        expected_df = pd.DataFrame(
            {
                "reportDetails.transactionRefNo": [
                    "O1T120200101BUYI",
                    "20200101BUYI",
                    "O3T3BUYI",
                    pd.NA,
                    "O5REALLYLONGTESTCASEOHMYGODT5REALLYLONGTESTCASEOHMYG",
                ]
            }
        )
        assert outcome_df.equals(expected_df)

    def test_transaction_ref_no_with_outbound_time(
        self, transaction_ref_no_with_outbound_time: pd.DataFrame
    ):
        mapping = TransactionRefNo(name="test-transaction-ref-no", params=Params(**{}))
        outcome_df = mapping.execute(
            transaction_ref_no_with_outbound_time, params=Params(**{})
        )
        expected_df = pd.DataFrame(
            {
                "reportDetails.transactionRefNo": [
                    "O1T199990101BUYI",
                ]
            }
        )
        assert outcome_df.equals(expected_df)
