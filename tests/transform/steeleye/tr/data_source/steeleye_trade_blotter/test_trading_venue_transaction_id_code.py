import pandas as pd
import pytest

from swarm_tasks.steeleye.tr.data_source.steeleye_trade_blotter import (
    trading_venue_transaction_id_code,
)


TRX_DETAILS_VENUE = "transactionDetails.venue"
TRX_DETAILS_ULTIMATE_VENUE = "transactionDetails.ultimateVenue"
TRX_REF_NO = "reportDetails.transactionRefNo"
TARGET_ATTRIBUTE = "reportDetails.tradingVenueTransactionIdCode"


@pytest.fixture()
def source_dataframe() -> pd.DataFrame:
    """Represents a source dataframe parsed from an input csv file."""
    df = pd.DataFrame(
        {
            TRX_DETAILS_VENUE: [
                "BTQG",
                pd.NA,
                "NOT_A_VENUE",
                "ELIX",
                "NOT_A_VENUE2",
                "MAEL",
                "GMGE",
            ],
            TRX_DETAILS_ULTIMATE_VENUE: [
                "BTQG",
                pd.NA,
                "NOT_A_VENUE",
                "ELIX",
                "NOT_A_VENUE2",
                "MAEL",
                "GMGE",
            ],
            TRX_REF_NO: ["REF1", "REF2", pd.NA, pd.NA, "REF3", "REF4", "GMG_REF"],
            "EXECUTINGENTITYID": [
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                "CustomVenue1234",
            ],
        }
    )
    return df


class TestTradingVenueTransactionIdCode:
    def test_empty_source_frame(self):
        source_frame = pd.DataFrame(
            {
                TRX_DETAILS_VENUE: [pd.NA],
                TRX_REF_NO: [pd.NA],
                TRX_DETAILS_ULTIMATE_VENUE: [pd.NA],
            }
        )

        params = trading_venue_transaction_id_code.Params(
            venue_type="UK_VENUES",
        )

        task = trading_venue_transaction_id_code.TradingVenueTransactionIdCode(
            name="test-trading-venue-transaction-id-code", params=params
        )

        outcome_df = task.execute(source_frame=source_frame, params=params)

        expected_df = pd.DataFrame({TARGET_ATTRIBUTE: [pd.NA]})
        assert outcome_df.equals(expected_df)

    def test_normal_behaviour(self, source_dataframe):

        params = trading_venue_transaction_id_code.Params(
            venue_type="UK_VENUES",
        )

        task = trading_venue_transaction_id_code.TradingVenueTransactionIdCode(
            name="test-trading-venue-transaction-id-code", params=params
        )

        outcome_df = task.execute(source_frame=source_dataframe, params=params)

        expected_df = pd.DataFrame(
            {TARGET_ATTRIBUTE: ["REF1", pd.NA, pd.NA, pd.NA, pd.NA, "REF4", pd.NA]}
        )
        assert outcome_df.equals(expected_df)

    def test_normal_behaviour_with_custom_params(self, source_dataframe):

        params = trading_venue_transaction_id_code.Params(
            venue_type="UK_VENUES",
            custom_venue="GMGE",
            custom_execution_entity="CustomVenue1234",
        )

        task = trading_venue_transaction_id_code.TradingVenueTransactionIdCode(
            name="test-trading-venue-transaction-id-code", params=params
        )

        outcome_df = task.execute(source_frame=source_dataframe, params=params)

        expected_df = pd.DataFrame(
            {TARGET_ATTRIBUTE: ["REF1", pd.NA, pd.NA, pd.NA, pd.NA, "REF4", "GMG_REF"]}
        )
        assert outcome_df.equals(expected_df)

    def test_not_valid_venue_validator(self, source_dataframe):

        with pytest.raises(KeyError):

            params = trading_venue_transaction_id_code.Params(
                venue_type="PT_VENUES",
            )

            task = trading_venue_transaction_id_code.TradingVenueTransactionIdCode(
                name="test-trading-venue-transaction-id-code", params=params
            )

            task.execute(source_frame=source_dataframe, params=params)
