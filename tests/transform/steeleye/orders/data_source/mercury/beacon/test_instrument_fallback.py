import pandas as pd
import pytest

from swarm_tasks.order.feed.mercury.beacon import instrument_fallback as fb
from swarm_tasks.order.feed.mercury.beacon.resources.static import MercuryBeaconColumns


@pytest.fixture()
def all_col_in_source_df() -> pd.DataFrame:
    """Simulates the source dataframe parsed from an input csv file and modified by upstream tasks.
    Four rows are included with each of the 4 TYPES C, F, X and P. The fifth row has an existing
    linked instrument
    """
    df = pd.DataFrame(
        {
            MercuryBeaconColumns.TYPE: ["C", "F", "X", "P", "C"],
            MercuryBeaconColumns.SYMBOL: [
                "BTCUSD",
                "BTCUSD",
                "BTCPERP",
                "BTCUSD",
                "BTCUSD",
            ],
            MercuryBeaconColumns.EXPIRATION: [
                "2021-03-26",
                "2021-06-25",
                pd.NA,
                "2021-04-02",
                "2021-04-02",
            ],
            MercuryBeaconColumns.STRIKE: [
                16000.00000,
                0.00000,
                0.00000,
                60000.00000,
                20000.00000,
            ],
            "instrumentDetails.instrument": [
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                "existing_instrument",
            ],
        }
    )
    return df


@pytest.fixture()
def missing_some_col_in_source_df() -> pd.DataFrame:
    """Simulates the source dataframe parsed from an input csv file and modified by upstream tasks, but with
    the expiry date missing in the source data frame.
    Four rows are included with each of the 4 TYPES C, F, X and P. The fifth row has an existing
    linked instrument
    """
    df = pd.DataFrame(
        {
            MercuryBeaconColumns.TYPE: ["C", "F", "X", "P", "C"],
            MercuryBeaconColumns.SYMBOL: [
                "BTCUSD",
                "BTCUSD",
                "BTCPERP",
                "BTCUSD",
                "BTCUSD",
            ],
            MercuryBeaconColumns.STRIKE: [
                16000.00000,
                0.00000,
                0.00000,
                60000.00000,
                20000.00000,
            ],
            "instrumentDetails.instrument": [
                pd.NA,
                pd.NA,
                pd.NA,
                pd.NA,
                "existing_instrument",
            ],
        }
    )
    return df


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    creates empty dataframe
    """
    result = pd.DataFrame()

    return result


class TestInstrumentFallBack(object):
    """
    Test cases for "InstrumentFallBack" class
    """

    def test_empty_input_df_without_source_columns(self, empty_source_df: pd.DataFrame):
        """Tests if InstrumentFallback returns an empty target df when an
        empty source df is passed"""
        task = fb.InstrumentFallback(name="test-instrument-fallback")
        result = task.execute(empty_source_df)
        assert result.empty

    def test_all_col_five_rows_in_source_df(self, all_col_in_source_df: pd.DataFrame):
        """This function tests the result for the source frame with 4 rows: 4 different TYPES C, F, X and P.
        The fifth row contains an already-linked instrument"""
        # The fields which are not expected in the output are commented in the expected_result below
        expected_result = pd.DataFrame(
            {
                "instrumentDetails.instrument": [
                    {  # TYPE = 'C'
                        "derivative.expiryDate": "2021-03-26",
                        "derivative.optionExerciseStyle": "EURO",
                        "derivative.optionType": "CALL",
                        "derivative.priceMultiplier": 1,
                        "derivative.strikePrice": 16000.00000,
                        "derivative.strikePriceCurrency": "USD",
                        # "fxDerivatives.notionalCurrency2": pd.NA,
                        "ext.priceNotation": "MONE",
                        "ext.quantityNotation": "UNIT",
                        "ext.strikePriceType": "MntryVal",
                        "instrumentClassification": "OCEFPS",
                        "instrumentFullName": "Options on BTC Futures, Mar2021 C 16000.0",
                        "notionalCurrency1": "USD",
                        "isCreatedThroughFallback": True,
                        "venue.tradingVenue": "XXXX",
                        "ext.aii.mic": "XXXX",
                        "ext.bestExAssetClassMain": "Currency Derivatives",
                        "ext.bestExAssetClassSub": "Swaps and other currency derivatives",
                    },
                    {  # TYPE = 'F'
                        "derivative.expiryDate": "2021-06-25",
                        # "derivative.optionExerciseStyle": pd.NA,
                        # "derivative.optionType": pd.NA,
                        "derivative.priceMultiplier": 1,
                        # "derivative.strikePrice": pd.NA,
                        "derivative.strikePriceCurrency": "USD",
                        # "fxDerivatives.notionalCurrency2": pd.NA,
                        "ext.priceNotation": "MONE",
                        "ext.quantityNotation": "UNIT",
                        # "ext.strikePriceType": pd.NA,
                        "instrumentClassification": "FFIPXX",
                        "instrumentFullName": "BTC Futures, Jun2021",
                        "notionalCurrency1": "USD",
                        "isCreatedThroughFallback": True,
                        "venue.tradingVenue": "XXXX",
                        "ext.aii.mic": "XXXX",
                        "ext.bestExAssetClassMain": "Equity Derivatives",
                        "ext.bestExAssetClassSub": "Futures and options admitted to trading on a trading venue",
                    },
                    {  # TYPE = 'X'
                        # "derivative.expiryDate": pd.NA,
                        # "derivative.optionExerciseStyle": pd.NA,
                        # "derivative.optionType": pd.NA,
                        "derivative.priceMultiplier": 1,
                        # "derivative.strikePrice": pd.NA,
                        "derivative.strikePriceCurrency": "USD",
                        "fxDerivatives.notionalCurrency2": "BTC",
                        "ext.priceNotation": "MONE",
                        "ext.quantityNotation": "UNIT",
                        # "ext.strikePriceType": pd.NA,
                        "instrumentClassification": "JFTXCC",
                        "instrumentFullName": "USD/BTC CFD",
                        "notionalCurrency1": "USD",
                        "isCreatedThroughFallback": True,
                        "venue.tradingVenue": "XXXX",
                        "ext.aii.mic": "XXXX",
                        "ext.bestExAssetClassMain": "Currency Derivatives",
                        "ext.bestExAssetClassSub": "Swaps and other currency derivatives",
                    },
                    {  # TYPE = 'P'
                        "derivative.expiryDate": "2021-04-02",
                        "derivative.optionExerciseStyle": "EURO",
                        "derivative.optionType": "PUTO",
                        "derivative.priceMultiplier": 1,
                        "derivative.strikePrice": 60000.00000,
                        "derivative.strikePriceCurrency": "USD",
                        # "fxDerivatives.notionalCurrency2": pd.NA,
                        "ext.priceNotation": "MONE",
                        "ext.quantityNotation": "UNIT",
                        "ext.strikePriceType": "MntryVal",
                        "instrumentClassification": "OPEFPS",
                        "instrumentFullName": "Options on BTC Futures, Apr2021 P 60000.0",
                        "notionalCurrency1": "USD",
                        "isCreatedThroughFallback": True,
                        "venue.tradingVenue": "XXXX",
                        "ext.aii.mic": "XXXX",
                        "ext.bestExAssetClassMain": "Currency Derivatives",
                        "ext.bestExAssetClassSub": "Swaps and other currency derivatives",
                    },
                    "existing_instrument",
                ],
            }
        )

        task = fb.InstrumentFallback(name="test-instrument-fallback")
        result = task.execute(all_col_in_source_df)
        assert result.equals(expected_result)

    def test_missing_some_cols_in_source_df(
        self, missing_some_col_in_source_df: pd.DataFrame
    ):
        """This function tests the result for the source frame with 4 rows: 4 different TYPES C, F, X and P."""
        # The fields which are not expected in the output are commented in the expected_result below
        expected_result = pd.DataFrame(
            {
                "instrumentDetails.instrument": [
                    {  # TYPE = 'C'
                        # "derivative.expiryDate": pd.NA,
                        "derivative.optionExerciseStyle": "EURO",
                        "derivative.optionType": "CALL",
                        "derivative.priceMultiplier": 1,
                        "derivative.strikePrice": 16000.00000,
                        "derivative.strikePriceCurrency": "USD",
                        # "fxDerivatives.notionalCurrency2": pd.NA,
                        "ext.priceNotation": "MONE",
                        "ext.quantityNotation": "UNIT",
                        "ext.strikePriceType": "MntryVal",
                        "instrumentClassification": "OCEFPS",
                        "instrumentFullName": "Options on BTC Futures, NaT C 16000.0",
                        "notionalCurrency1": "USD",
                        "isCreatedThroughFallback": True,
                        "venue.tradingVenue": "XXXX",
                        "ext.aii.mic": "XXXX",
                        "ext.bestExAssetClassMain": "Currency Derivatives",
                        "ext.bestExAssetClassSub": "Swaps and other currency derivatives",
                    },
                    {  # TYPE = 'F'
                        # "derivative.expiryDate": pd.NA,
                        # "derivative.optionExerciseStyle": pd.NA,
                        # "derivative.optionType": pd.NA,
                        "derivative.priceMultiplier": 1,
                        # "derivative.strikePrice": pd.NA,
                        "derivative.strikePriceCurrency": "USD",
                        # "fxDerivatives.notionalCurrency2": pd.NA,
                        "ext.priceNotation": "MONE",
                        "ext.quantityNotation": "UNIT",
                        # "ext.strikePriceType": pd.NA,
                        "instrumentClassification": "FFIPXX",
                        "instrumentFullName": "BTC Futures, NaT",
                        "notionalCurrency1": "USD",
                        "isCreatedThroughFallback": True,
                        "venue.tradingVenue": "XXXX",
                        "ext.aii.mic": "XXXX",
                        "ext.bestExAssetClassMain": "Equity Derivatives",
                        "ext.bestExAssetClassSub": "Futures and options admitted to trading on a trading venue",
                    },
                    {  # TYPE = 'X'
                        # "derivative.expiryDate": pd.NA,
                        # "derivative.optionExerciseStyle": pd.NA,
                        # "derivative.optionType": pd.NA,
                        "derivative.priceMultiplier": 1,
                        # "derivative.strikePrice": pd.NA,
                        "derivative.strikePriceCurrency": "USD",
                        "fxDerivatives.notionalCurrency2": "BTC",
                        "ext.priceNotation": "MONE",
                        "ext.quantityNotation": "UNIT",
                        # "ext.strikePriceType": pd.NA,
                        "instrumentClassification": "JFTXCC",
                        "instrumentFullName": "USD/BTC CFD",
                        "notionalCurrency1": "USD",
                        "isCreatedThroughFallback": True,
                        "venue.tradingVenue": "XXXX",
                        "ext.aii.mic": "XXXX",
                        "ext.bestExAssetClassMain": "Currency Derivatives",
                        "ext.bestExAssetClassSub": "Swaps and other currency derivatives",
                    },
                    {  # TYPE = 'P'
                        # "derivative.expiryDate": pd.NA,
                        "derivative.optionExerciseStyle": "EURO",
                        "derivative.optionType": "PUTO",
                        "derivative.priceMultiplier": 1,
                        "derivative.strikePrice": 60000.00000,
                        "derivative.strikePriceCurrency": "USD",
                        # "fxDerivatives.notionalCurrency2": pd.NA,
                        "ext.priceNotation": "MONE",
                        "ext.quantityNotation": "UNIT",
                        "ext.strikePriceType": "MntryVal",
                        "instrumentClassification": "OPEFPS",
                        "instrumentFullName": "Options on BTC Futures, NaT P 60000.0",
                        "notionalCurrency1": "USD",
                        "isCreatedThroughFallback": True,
                        "venue.tradingVenue": "XXXX",
                        "ext.aii.mic": "XXXX",
                        "ext.bestExAssetClassMain": "Currency Derivatives",
                        "ext.bestExAssetClassSub": "Swaps and other currency derivatives",
                    },
                    "existing_instrument",
                ],
            }
        )

        task = fb.InstrumentFallback(name="test-instrument-fallback")
        result = task.execute(missing_some_col_in_source_df)
        assert result.equals(expected_result)

    def test_instrument_full_name(self):
        """Tests the _instrument_full_name function for all 4 Types 'C', 'F', 'X' and 'P'."""
        row = pd.Series(
            {
                "Type": "C",
                "temp_currency": "BTC",
                "temp_expiry_date_mmmyyyy": "Mar2021",
                "__strike_price__": 16000.00000,
            }
        )
        expected_result = "Options on BTC Futures, Mar2021 C 16000.0"
        task = fb.InstrumentFallback(name="test-instrument-fallback")
        result = task._instrument_full_name(row)
        assert result == expected_result
