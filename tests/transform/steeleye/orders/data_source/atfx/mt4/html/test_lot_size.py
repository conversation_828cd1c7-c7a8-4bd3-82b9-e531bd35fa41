from pathlib import Path

import numpy as np
import pandas as pd
import pytest
from se_core_tasks.core.core_dataclasses import ExtractPathResult

from swarm_tasks.transform.steeleye.orders.data_source.atfx.mt4.html.identifiers import (
    lot_size as fb,
)


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    creates empty dataframe
    """
    result = pd.DataFrame()

    return result


@pytest.fixture()
def all_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "Symbol": ["US500", "GLOBAL500", pd.NA],
            "temp.initialQuantity": [10, 0, pd.NA],
            "Lots": [10, 20, 10],
        }
    )
    return df


@pytest.fixture()
def missing_some_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {"temp.initialQuantity": [np.nan, 10, np.nan], "Lots": [10, 20, 10]}
    )
    return df


@pytest.fixture()
def json_data() -> dict:
    data = {"Products": ["US500", "GLOBAL500", pd.NA], "Contract Size": [10, 20, 0]}
    return data


class TestLotSize(object):
    """
    Test cases for "TestLotSize" class
    """

    def test_empty_input_df_without_source_columns(
        self, empty_source_df: pd.DataFrame, mocker
    ):
        task = fb.LotSize(name="test-lot-size")
        mock_read = mocker.patch.object(fb.LotSize, "_LotSize__read_lot_cache")
        mock_read.return_value = {"US500": 10, "GLOBAL500": 10}
        extractor_result = ExtractPathResult(path=Path("/dummy/path"))
        result = task.execute(empty_source_df, extractor_result)
        assert result.empty
        assert mock_read.not_called

    def test_all_col_in_source_df(self, all_col_in_source_df: pd.DataFrame, mocker):
        extractor_result = ExtractPathResult(path=Path("/dummy/path"))
        task = fb.LotSize(name="test-lot-size")
        expected_result = pd.DataFrame(
            {
                "priceFormingData.initialQuantity": [100, 2000, 0],
                "priceFormingData.tradedQuantity": [100, 2000, 0],
            }
        )
        mock_read = mocker.patch.object(fb.LotSize, "_LotSize__read_lot_cache")
        mock_read.return_value = {"US500": 10, "GLOBAL500": 100}
        result = task.execute(all_col_in_source_df, extractor_result)
        assert result.equals(expected_result.astype(float))
        assert mock_read.called

    def test_missing_some_col_in_source_df(
        self, missing_some_col_in_source_df: pd.DataFrame, mocker
    ):
        extractor_result = ExtractPathResult(path=Path("/dummy/path"))
        task = fb.LotSize(name="test-lot-size")
        expected_result = pd.DataFrame(
            {
                "priceFormingData.initialQuantity": [0, 200, 0],
                "priceFormingData.tradedQuantity": [0, 200, 0],
            }
        )
        mock_read = mocker.patch.object(fb.LotSize, "_LotSize__read_lot_cache")
        mock_read.return_value = {"US500": 10, "GLOBAL500": 100}
        result = task.execute(missing_some_col_in_source_df, extractor_result)
        assert result.equals(expected_result.astype(float))
        assert mock_read.not_called
