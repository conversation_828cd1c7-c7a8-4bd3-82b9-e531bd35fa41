import re

import pytest
from swarm.task.transform.result import TransformResult

from swarm_tasks.transform.regex.regex_group_dict import Params
from swarm_tasks.transform.regex.regex_group_dict import RegexSearchGroupDict
from swarm_tasks.transform.regex.regex_group_dict import StringParser


class TestRegexSearchGroupDict:
    def test_pattern_not_found_raises_value_error(self):
        params = Params(pattern=r"(?P<year>\d{4})-(?P<month>\d{2})-(?P<day>\d{2})")
        task = RegexSearchGroupDict(name="RegexSearchGroupDict", params=params)
        with pytest.raises(ValueError):
            task.execute(params, string="19-01-01")

    @pytest.mark.parametrize(
        "string, pattern",
        [
            ("19-01-01", r"(?P<year>\d{2})-(?P<month>\d{2})-(?P<day>\d{2})"),
            ("name: foobar, age: 42", r"name: (?P<name>\w+),"),
        ],
    )
    def test_match_returns_transform_result_with_expected_columns(
        self, string, pattern
    ):
        params = Params(pattern=pattern)
        task = RegexSearchGroupDict(name="RegexSearchGroupDict", params=params)
        match_group = re.search(pattern, string).groupdict()

        result = task.execute(params, string=string)

        assert isinstance(result, TransformResult)
        assert (key in result.target.columns for key in match_group.keys())

    def test_pattern_without_named_subgroup_raises_validation_error(self):
        pattern = r"\d\.\d+.\d-\w+\.\+4"
        with pytest.raises(ValueError) as e:
            Params(pattern=pattern)

        assert e.match("No named subgroup in pattern")

    @pytest.mark.parametrize(
        "string, pattern, string_parser",
        [
            (
                "call_2020-08-10_%2B999",
                r"call_(?P<date>\d{4}-\d{2}-\d{2})_(?P<number>\+\d+)",
                StringParser.UNQUOTE,
            ),
            (
                "name-joe+bloggs",
                r"name-(?P<firstName>\w+)\s(?P<lastName>\w+)",
                StringParser.UNQUOTE_PLUS,
            ),
        ],
    )
    def test_parse_string_param_removes_escaped_chars(
        self, string, pattern, string_parser
    ):
        params = Params(pattern=pattern)
        task = RegexSearchGroupDict(name="RegexSearchGroupDict", params=params)
        with pytest.raises(ValueError):
            task.execute(params, string=string)

        params = Params(pattern=pattern, parse_string=string_parser)
        task = RegexSearchGroupDict(name="RegexSearchGroupDict", params=params)
        task.execute(params, string=string)
