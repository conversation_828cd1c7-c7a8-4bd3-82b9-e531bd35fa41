import pandas as pd
import pytest
from prefect.engine.signals import SKIP

from swarm_tasks.transform.regex.regex_get_columns_from_groups import Params
from swarm_tasks.transform.regex.regex_get_columns_from_groups import (
    RegexGetColumnsFromGroups,
)


@pytest.fixture()
def empty_source_frame() -> pd.DataFrame:
    df = pd.DataFrame(columns=["file_url", "realm"])
    return df


@pytest.fixture()
def batch_csv_source_frame() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "file_url": [
                "s3://dummy.dev.steeleye.co/stream/comms-voice-luna-advance-comms/"
                "20210701_100144_441614497395_441615498344_I_49.mp3",
                "s3://dummy.dev.steeleye.co/stream/comms-voice-luna-advance-comms/"
                "20210702_091932_447730761461_441615498342_I_12.mp3",
            ],
            "realm": ["dummy.dev.steeleye.co", "dummy.dev.steeleye.co"],
        }
    )
    return df


@pytest.fixture()
def batch_csv_source_frame_with_one_invalid_row() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "file_url": [
                "s3://dummy.dev.steeleye.co/stream/comms-voice-luna-advance-comms",
                "s3://dummy.dev.steeleye.co/stream/comms-voice-luna-advance-comms/"
                "20210701_100144_441614497395_441615498344_I_49.mp3",
                "s3://dummy.dev.steeleye.co/stream/comms-voice-luna-advance-comms/"
                "20210702_091932_447730761461_441615498342_I_12.mp3",
            ],
            "realm": [
                "dummy.dev.steeleye.co",
                "dummy.dev.steeleye.co",
                "dummy.dev.steeleye.co",
            ],
        }
    )
    return df


@pytest.fixture()
def expected_result_2_matching_rows() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "Date": ["20210701", "20210702"],
            "Time": ["100144", "091932"],
            "internalExtensionNumber": ["441614497395", "447730761461"],
            "externalNumber": ["441615498344", "441615498342"],
            "Direction": ["I", "I"],
            "CallId": ["49", "12"],
            "__source_key__": [
                "s3://dummy.dev.steeleye.co/stream/comms-voice-luna-advance-comms/"
                "20210701_100144_441614497395_441615498344_I_49.mp3",
                "s3://dummy.dev.steeleye.co/stream/comms-voice-luna-advance-comms/"
                "20210702_091932_447730761461_441615498342_I_12.mp3",
            ],
        }
    )
    return df


@pytest.fixture()
def expected_result_2_matching_rows_with_file_name_in_output() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "Date": ["20210701", "20210702"],
            "Time": ["100144", "091932"],
            "internalExtensionNumber": ["441614497395", "447730761461"],
            "externalNumber": ["441615498344", "441615498342"],
            "Direction": ["I", "I"],
            "CallId": ["49", "12"],
            "__source_key__": [
                "s3://dummy.dev.steeleye.co/stream/comms-voice-luna-advance-comms/"
                "20210701_100144_441614497395_441615498344_I_49.mp3",
                "s3://dummy.dev.steeleye.co/stream/comms-voice-luna-advance-comms/"
                "20210702_091932_447730761461_441615498342_I_12.mp3",
            ],
            "__file_name__": [
                "20210701_100144_441614497395_441615498344_I_49.mp3",
                "20210702_091932_447730761461_441615498342_I_12.mp3",
            ],
        }
    )
    return df


@pytest.fixture()
def expected_result_2_matching_rows_one_not_matching_row() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "Date": [pd.NA, "20210701", "20210702"],
            "Time": [pd.NA, "100144", "091932"],
            "internalExtensionNumber": [pd.NA, "441614497395", "447730761461"],
            "externalNumber": [pd.NA, "441615498344", "441615498342"],
            "Direction": [pd.NA, "I", "I"],
            "CallId": [pd.NA, "49", "12"],
            "__source_key__": [
                pd.NA,
                "s3://ashwath.dev.steeleye.co/stream/comms-voice-luna-advance-comms/"
                "20210701_100144_441614497395_441615498344_I_49.mp3",
                "s3://ashwath.dev.steeleye.co/stream/comms-voice-luna-advance-comms/"
                "20210702_091932_447730761461_441615498342_I_12.mp3",
            ],
        }
    )
    return df


class TestRegexGetColumnsFromGroups:
    """Test Suite for RegexGetColumnsFromGroups"""

    def test_empty_source_frame(self, empty_source_frame):
        """Test for when the source frame is empty"""
        params = Params(
            pattern=r"(?P<year>\d{4})-(?P<month>\d{2})-(?P<day>\d{2})",
            file_url_column="file_url",
            target_source_key_column="__source_key__",
        )
        task = RegexGetColumnsFromGroups(
            name="RegexGetColumnsFromGroups", params=params
        )
        with pytest.raises(SKIP):
            task.execute(source_frame=empty_source_frame, params=params)

    def test_pattern_not_found_returns_empty_frame(self, batch_csv_source_frame):
        """Test for the case in which the pattern in params is not found in the
        file_url column
        """
        params = Params(
            pattern=r"(?P<year>\d{4})-(?P<month>\d{2})-(?P<day>\d{2})",
            file_url_column="file_url",
            target_source_key_column="__source_key__",
        )
        task = RegexGetColumnsFromGroups(
            name="RegexGetColumnsFromGroups", params=params
        )
        result = task.execute(source_frame=batch_csv_source_frame, params=params)
        assert result.empty

    def test_two_files_match_pattern(
        self, batch_csv_source_frame, expected_result_2_matching_rows
    ):
        """Test for the case where the the other 2 rows contain expected s3 paths.
        A data frame is expected with 7 columns"""
        luna_pattern = r"(?P<Date>\d{4}\d{2}\d{2})_(?P<Time>\d{6})_(?P<internalExtensionNumber>\d+)_(?P<externalNumber>\d+)_(?P<Direction>\w)_(?P<CallId>\d+)(\[.\])?.mp3"  # noqa
        params = Params(
            pattern=luna_pattern,
            file_url_column="file_url",
            parse_string="unquote.plus",
            target_source_key_column="__source_key__",
        )
        task = RegexGetColumnsFromGroups(
            name="RegexGetColumnsFromGroups", params=params
        )
        result = task.execute(source_frame=batch_csv_source_frame, params=params)

        assert result.equals(expected_result_2_matching_rows)

    def test_two_files_match_pattern_one_does_not(
        self, batch_csv_source_frame, expected_result_2_matching_rows
    ):
        """Test for the case where the first row contains an invalid s3 path (no
        filename) while the other 2 rows contain expected s3 paths
        A data frame is expected with 7 columns, with the first row=pd.NA in all cols"""
        luna_pattern = r"(?P<Date>\d{4}\d{2}\d{2})_(?P<Time>\d{6})_(?P<internalExtensionNumber>\d+)_(?P<externalNumber>\d+)_(?P<Direction>\w)_(?P<CallId>\d+)(\[.\])?.mp3"  # noqa
        params = Params(
            pattern=luna_pattern,
            file_url_column="file_url",
            target_source_key_column="__source_key__",
        )
        task = RegexGetColumnsFromGroups(
            name="RegexGetColumnsFromGroups", params=params
        )
        result = task.execute(source_frame=batch_csv_source_frame, params=params)

        assert result.equals(expected_result_2_matching_rows)

    def test_pattern_without_named_subgroup_raises_validation_error(self):
        pattern = r"\d\.\d+.\d-\w+\.\+4"
        with pytest.raises(ValueError) as e:
            Params(
                pattern=pattern, file_url_column="foo", target_source_key_column="bar"
            )

        assert e.match("No named subgroup in pattern")

    def test_file_name_param(
        self,
        batch_csv_source_frame,
        expected_result_2_matching_rows_with_file_name_in_output,
    ):
        """Test that the file key is populated in the output when the param is used.
        A data frame is expected with 8 columns"""
        luna_pattern = r"(?P<Date>\d{4}\d{2}\d{2})_(?P<Time>\d{6})_(?P<internalExtensionNumber>\d+)_(?P<externalNumber>\d+)_(?P<Direction>\w)_(?P<CallId>\d+)(\[.\])?.mp3"  # noqa
        params = Params(
            pattern=luna_pattern,
            file_url_column="file_url",
            parse_string="unquote.plus",
            target_source_key_column="__source_key__",
            filename_in_target_column="__file_name__",
        )
        task = RegexGetColumnsFromGroups(
            name="RegexGetColumnsFromGroups", params=params
        )
        result = task.execute(source_frame=batch_csv_source_frame, params=params)

        assert result.equals(expected_result_2_matching_rows_with_file_name_in_output)
