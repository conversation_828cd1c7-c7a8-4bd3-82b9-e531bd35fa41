���"      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK&��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�transactionDetails.venue�� transactionDetails.ultimateVenue��instrumentDetails.instrument��workflow.isReported��workflow.status��workflow.eligibility��#workflow.eligibility.executionVenue��)workflow.eligibility.totvOnExecutionVenue��3tradersAlgosWaiversIndicators.shortSellingIndicator��*transactionDetails.branchMembershipCountry��:tradersAlgosWaiversIndicators.commodityDerivativeIndicator��+reportDetails.tradingVenueTransactionIdCode��transactionDetails.netAmount��transactionDetails.quantity��"transactionDetails.tradingCapacity��#transactionDetails.buySellIndicator��!transactionDetails.positionEffect��#transactionDetails.quantityNotation�� transactionDetails.priceNotation��.transmissionDetails.orderTransmissionIndicator��transactionDetails.pricePending��0instrumentDetails.instrument.ext.strikePriceType��transactionDetails.recordType��!transactionDetails.upFrontPayment��)transactionDetails.upFrontPaymentCurrency��#transactionDetails.quantityCurrency�� transactionDetails.priceCurrency��+transactionDetails.settlementAmountCurrency��transactionDetails.price��marketIdentifiers��date��"transactionDetails.tradingDateTime��=tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator��sourceIndex��	sourceKey��reportDetails.reportStatus��__meta_model__��reportDetails.transactionRefNo�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C               �t�bhL�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h!�]�(�XXXX�hmet�b�_dtype�hb�StringDtype���)��ubhhK ��h��R�(KKK��h!�]�(�pandas._libs.missing��NA���hz}�(�	sourceKey��retailbroker.cfd.indices.srp��&instrumentClassificationEMIRAssetClass��EQ��&id��XXXXUS30USDCFD��cfiGroup��Equity��cfiCategory��Forwards��+commoditiesOrEmissionAllowanceDerivativeInd�h�scalar���h�b1�����R�(Kh"NNNJ����J����K t�bC ���R��instrumentFullName��)US US30 Equity Index CFD - Wall Street 30��&key��,SteelEyeInstrument:XXXXUS30USDCFD:1579395480��notionalCurrency1��USD��'instrumentClassificationEMIRProductType��CFD��(instrumentClassificationEMIRContractType��CD��
cfiAttribute1��Index��
cfiAttribute2��Not Applicable/Undefined��
cfiAttribute3��Contract for difference��
cfiAttribute4��Cash��instrumentClassification��JEIXCC��ext.aii.mic��XXXX��ext.emirEligible�h�h�C���R��ext.underlyingOnFIRDS�h��#ext.alternativeInstrumentIdentifier��XXXXUS30CFD��ext.mifirEligible�h��ext.otcFlag�h��ext.bestExAssetClassSub��"Swaps and other equity derivatives��ext.exchangeSymbol��US30��ext.bestExAssetClassMain��Equity Derivatives��ext.quantityNotation��UNIT��ext.underlyingInstruments�G�      �ext.instrumentUniqueIdentifier��XXXXUS30USDCFD��&ext.additionalIdentifiers.seBlotter_13�G�      �&ext.additionalIdentifiers.seBlotter_12�G�      �&ext.additionalIdentifiers.seBlotter_15�G�      �%ext.additionalIdentifiers.seBlotter_2�G�      �&ext.additionalIdentifiers.seBlotter_14�G�      �%ext.additionalIdentifiers.seBlotter_1�G�      �&ext.additionalIdentifiers.seBlotter_17�G�      �%ext.additionalIdentifiers.seBlotter_4�G�      �&ext.additionalIdentifiers.seBlotter_16�G�      �%ext.additionalIdentifiers.seBlotter_3�G�      �&ext.additionalIdentifiers.seBlotter_19�G�      �%ext.additionalIdentifiers.seBlotter_6�G�      �&ext.additionalIdentifiers.seBlotter_18�G�      �%ext.additionalIdentifiers.seBlotter_5�G�      �%ext.additionalIdentifiers.seBlotter_8�G�      �%ext.additionalIdentifiers.seBlotter_7�G�      �%ext.additionalIdentifiers.seBlotter_9�G�      �&ext.additionalIdentifiers.seBlotter_20�G�      �&ext.additionalIdentifiers.seBlotter_11�G�      �&ext.additionalIdentifiers.seBlotter_10�G�      �ext.onFIRDS�h��ext.priceNotation��MONE��venue.tradingVenue��XXXX��"venue.financialInstrumentShortName���A Rolling Contract for Difference Index Product in USD with a Price Multiplier of 1 priced in monetary terms with the quantity noted in units with the delivery type being cash��)commodityAndEmissionAllowances.subProduct�� ��*commodityAndEmissionAllowances.baseProduct�h֌derivative.underlyingIndexName��US30��derivative.underlyingIndexTerm��MNTH��#derivative.underlyingIndexTermValue��3�� derivative.underlyingInstruments�G�      �derivative.deliveryType��CASH��derivative.priceMultiplier��1��2commodityAndEmissionAllowances.deliveryPointorZone�G�      �;commodityAndEmissionAllowances.deliveryInterconnectionPoint�G�      u}�(h|�retailbroker.sb.indices.srp�h~�EQ�h��XXXXGER30EURSB�h��Equity�h��Forwards�h�h�h��DAX (DE) SB�h��)VenueInstrument:XXXXGER30EURSB:1631187148�h��EUR�h��
EquityForward�h��SB�h��Index�h��Not Applicable/Undefined�h��
Spread-bet�h��Cash�h��JEIXSC�h��XXXX�h�h�h�h�h��XXXXGER30SB�h�h�h�h�h��"Swaps and other equity derivatives�h��DE30�h��Equity Derivatives�h��UNIT�h�]�}��instrumentIdCode��DE0008469008�sah��XXXXGER30EURSB�h��GERMAN�h��	GERMANY30�h��DAX 30�h��.DE30�h��DAX�h��DE0008469008�h��DA30�h��DAX30�h
DAX 30 Future�hÌDE30�h�h�hŌ
Germany 30�h�h�hǌGER30�hȌ1GER30�hɌGermany 30 - Rolling Cash�hʌ	IDX.DE.30�h�h�ȟGERMAN30�h͌D30EUR�h�h�hόMONE�hьXXXX�hӌ�A Rolling Spread-bet Index Product in EUR with a Price Multiplier of 1 priced in monetary terms with the quantity noted in units with the delivery type being cash�h�h�h�h�h،DE30�hڌMNTH�h�h�h�]�}��underlyingInstrumentCode��DE0008469008�sahߌCASH�h�h�h�G�      h�G�      u�se_schema.static.mifid2��RTS22TransactionStatus����
REPORTABLE���R�j   }�(�onFirds���eligible���reason��1Non-EEA traded Derivative with uToTV constituents��terminationDate��pandas._libs.tslibs.nattype��__nat_unpickle���N��R��totv���underlyingOnFirds���utotv���applicableVenues�]��discountedVenues�]��firstAdmissionToTrading�j+  uj!  hzhzhzhzhzhzhzhzhzhzhzhzhzhz�DEAL�j4  �BUYI�j5  hzhz�UNIT�j6  �MONE�j7  ��hzhzhzhz�USD��EUR�j8  j9  j8  j9  ]�(}�(�labelId��XXXXUS30CFD��path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�u}�(j<  �XXXXUS30USDCFD�j>  j?  j@  jF  u}�(j<  �lei:2138007775i9gy8yol39�j>  �
parties.buyer�j@  jC  �ARRAY���R�u}�(j<  �lei:2138007775i9gy8yol39�j>  �parties.buyerDecisionMaker�j@  jN  u}�(j<  �lei:2138007775i9gy8yol39�j>  �parties.executingEntity�j@  jF  u}�(j<  �lei:5493000v36fyizgtzl53�j>  �parties.seller�j@  jN  u}�(j<  �lei:5493000v36fyizgtzl53�j>  �parties.counterparty�j@  jF  u}�(j<  �account:algo�j>  �parties.executionWithinFirm�j@  jF  u}�(j<  �account:algo�j>  �parties.trader�j@  jN  ue]�(}�(j<  �XXXXGER30SB�j>  j?  j@  jF  u}�(j<  �XXXXGER30EURSB�j>  j?  j@  jF  u}�(j<  �lei:2138007775i9gy8yol39�j>  jK  j@  jN  u}�(j<  �lei:2138007775i9gy8yol39�j>  jQ  j@  jN  u}�(j<  �lei:2138007775i9gy8yol39�j>  jT  j@  jF  u}�(j<  �lei:5493000v36fyizgtzl53�j>  jW  j@  jN  u}�(j<  �lei:5493000v36fyizgtzl53�j>  jZ  j@  jF  u}�(j<  �account:algo�j>  j]  j@  jF  u}�(j<  �account:algo�j>  j`  j@  jN  ue�
2021-05-03��
2021-05-03��2021-05-03T10:38:40.000000Z��2021-05-03T19:35:54.000000Z��H/Users/<USER>/Desktop/se-data/IAPS-25/seBlotter.iaps25.genericCfd.txt�jx  �NEWT�jy  �RTS22Transaction�jz  �41175923742HEDGESEBLOTTERIAPS25GENERICCFDOZ1175923742��41176862372HEDGESEBLOTTERIAPS25GENERICCFDOZ1176862372�et�bhhK ��h��R�(KKK��h��C      �t�bhhK ��h��R�(KKK��h�f8�����R�(KhZNNNJ����J����K t�b�C@       @       @      �      �      �      �ffff>��@     ��@�t�bhhK ��h��R�(KKK��hY�C               �t�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bhLNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h&h'h)h*h+h,h-h.h/h0h1h3h4h5h6h7h9h:h;h>h?h@hBhChDhGhHhIhJet�bhLNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h(h8hEet�bhLNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h2h<h=hAet�bhLNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hFat�bhLNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�he�mgr_locs��builtins��slice���K KK��R�u}�(j�  huj�  hhK ��h��R�(KK��hY�C�                                                 	       
                                                                                                                       "       #       $       %       �t�bu}�(j�  j�  j�  hhK ��h��R�(KK��hY�C                      �t�bu}�(j�  j�  j�  hhK ��h��R�(KK��hY�C 
                            �t�bu}�(j�  j�  j�  j�  K!K"K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.