import time
from pathlib import Path

import pandas as pd
import pytest
from prefect import context
from prefect.utilities.collections import Dot<PERSON>ict
from se_elasticsearch.repository import get_repository_by_cluster_version
from swarm.schema.flow.bundle.components import ResourceConfig

from swarm_tasks.transform.steeleye.meta import assign_meta as am
from swarm_tasks.transform.steeleye.meta.assign_meta import AssignMeta

"""
Tunnel to dev-blue and uncomment skip ci to be able to run this test class
"""


@pytest.mark.skip(reason="skip CI")
class TestAssignMetaPerformance:
    def test_assign_meta_small(self):
        pkl_file_path = (
            Path(__file__)
            .parent.joinpath("data", "assign_meta_test_data_small.pkl")
            .as_posix()
        )
        data = pd.read_pickle(pkl_file_path)
        task, params, resources = self.__init_task()
        self.__run_task_multiple_executions(task, data, params, resources, 1)

    def test_assign_meta_big(self):
        pkl_file_path = (
            Path(__file__)
            .parent.joinpath("data", "assign_meta_test_data_big.pkl")
            .as_posix()
        )
        data = pd.read_pickle(pkl_file_path)
        task, params, resources = self.__init_task()
        self.__run_task_multiple_executions(task, data, params, resources, 1)

    @staticmethod
    def __init_task():
        resources = am.AssignMeta.resources_class(es_client_key="tenant-data")
        resource_config = ResourceConfig(
            host="elasticsearch.dev-blue.steeleye.co", port=9401
        )
        clients = {
            "tenant-data": get_repository_by_cluster_version(
                resource_config=resource_config
            )
        }
        identifier = DotDict({"tenant": "pinafore"})
        context.swarm = DotDict()
        context.swarm.identifier = identifier
        context.swarm.tenant_configuration = None
        params = am.Params(model_attribute="__meta_model__")
        assign_meta_task = am.AssignMeta(
            name="assign-meta", params=params, resources=resources
        )
        assign_meta_task.clients = clients
        return assign_meta_task, params, resources

    @staticmethod
    def __run_task_multiple_executions(
        task: AssignMeta, data: pd.DataFrame, params, resources, executions: int
    ):

        for _ in range(executions):
            start = time.time()
            task.execute(source_frame=data, params=params, resources=resources)
            end = time.time()
            difference = end - start
            print(f"Task ran for {difference} seconds\n")
