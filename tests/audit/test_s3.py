import os
import pathlib

import pytest

from swarm.audit.result.s3 import S3ResultAggregator


class TestS3ResultAggregator:
    def test_s3_consolidate_results_empty_target(self):
        path = pathlib.Path(__file__).parent.joinpath("data/results/empty").as_posix()
        s3 = S3ResultAggregator.consolidate_results(path)
        assert s3 is None

    @pytest.mark.skipif(os.environ.get("CI") == "true", reason="cannot run in CI")
    def test_s3_consolidate_results_s3_target(self):
        path = pathlib.Path(__file__).parent.joinpath("data/results/target").as_posix()
        s3 = S3ResultAggregator.consolidate_results(path)
        assert len(s3.uploaded) == 22
        assert s3.uploaded[0].bucket_name.endswith("uat.steeleye.co")
