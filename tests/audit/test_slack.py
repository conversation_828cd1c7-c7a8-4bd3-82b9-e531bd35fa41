import pendulum
from addict import addict

from swarm.audit.flow.flow_auditor import <PERSON>t<PERSON><PERSON>ult
from swarm.audit.slack.slack_report import <PERSON><PERSON>ck<PERSON><PERSON><PERSON>
from swarm.conf import Settings


class TestSlackReport:
    def test_slack_report_in_dev(self):
        audit_result = AuditResult(state="state message str")
        # using addict.Dict to simulate the Workflow because instancing it would be too complex
        workflow = addict.Dict(
            config=addict.Dict(
                bundle=addict.Dict(id="dummy-test-bundle", image="random-image")
            ),
            client=addict.Dict(name="client"),
            env="test",
            start_time=pendulum.now().utcnow(),
        )
        Settings.DEV = True
        Settings.FLOW_ID = "test-tenant.test-stack.steeleye.co:dummy-test-bundle"
        Settings.STACK = "test-stack"
        SlackReport(audit_result=audit_result, workflow=workflow).publish()
