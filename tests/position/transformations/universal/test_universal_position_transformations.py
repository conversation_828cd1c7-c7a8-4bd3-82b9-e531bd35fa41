import os
from pathlib import Path

import addict
import pandas as pd
import pytest
from prefect import context
from swarm.conf import SettingsCls
from swarm.task.auditor import Auditor

from swarm_tasks.position.transformations.universal.universal_position_transformations import (
    UniversalPositionTransformations,
)


SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
SOURCE_DATA_1 = TEST_FILES_DIR.joinpath("source_data.pkl")
ECB_RATES = TEST_FILES_DIR.joinpath("ecb_rates.pkl")
ECB_RATES_2 = TEST_FILES_DIR.joinpath("ecb_rates_2.pkl")
EXPECTED_RESULT_1 = TEST_FILES_DIR.joinpath("expected_data.pkl")
SOURCE_DATA_2 = TEST_FILES_DIR.joinpath("source_data_2.pkl")
EXPECTED_RESULT_2 = TEST_FILES_DIR.joinpath("expected_data_2.pkl")


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


class TestUniversalPositionTransformations:
    """Test for Universal Position blotter end to end"""

    def test_end_to_end_position_transformations(self, mocker):
        os.environ["SWARM_FILE_URL"] = "PositionsSampleData.csv"
        mock_connections = mocker.patch.object(
            SettingsCls, "connections", new_callable=mocker.PropertyMock
        )
        mock_connections.return_value = addict.Dict(
            {
                "reference-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                    },
                    "MAX_TERMS_SIZE": 1024,
                }
            }
        )
        ecb_rates = mocker.patch.object(
            UniversalPositionTransformations, "_get_ecb_rates"
        )
        ecb_rates.return_value = pd.read_pickle(ECB_RATES)
        source_frame = pd.read_pickle(SOURCE_DATA_1)
        task = UniversalPositionTransformations(
            source_frame=source_frame,
            logger=context.get("logger"),
            auditor=auditor,
        )

        result = task.process()
        result = result.drop(["sourceKey"], axis=1)
        expected_data = pd.read_pickle(EXPECTED_RESULT_1)
        pd.testing.assert_frame_equal(left=result, right=expected_data)

    def test_end_to_end_position_transformations_without_pnl(self, mocker):
        os.environ["SWARM_FILE_URL"] = "PositionsSampleData.csv"
        mock_connections = mocker.patch.object(
            SettingsCls, "connections", new_callable=mocker.PropertyMock
        )
        mock_connections.return_value = addict.Dict(
            {
                "reference-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                    },
                    "MAX_TERMS_SIZE": 1024,
                }
            }
        )
        ecb_rates = mocker.patch.object(
            UniversalPositionTransformations, "_get_ecb_rates"
        )
        ecb_rates.return_value = pd.read_pickle(ECB_RATES_2)
        source_frame = pd.read_pickle(SOURCE_DATA_2)
        task = UniversalPositionTransformations(
            source_frame=source_frame,
            logger=context.get("logger"),
            auditor=auditor,
        )

        result = task.process()
        result = result.drop(["sourceKey"], axis=1)
        expected_data = pd.read_pickle(EXPECTED_RESULT_2)
        pd.testing.assert_frame_equal(left=result, right=expected_data)
