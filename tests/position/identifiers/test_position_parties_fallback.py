from pathlib import Path

import addict
import pandas as pd
from swarm.conf import SettingsCls

from swarm_tasks.position.identifiers.position_parties_fallback import Params
from swarm_tasks.position.identifiers.position_parties_fallback import (
    PositionPartiesFallback,
)

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
SOURCE_DATA = TEST_FILES_DIR.joinpath("party_fallback_source.pkl")
EXPECTED_RESULT = TEST_FILES_DIR.joinpath("party_fallback_output.pkl")


class TestPositionPartiesFallback:
    """Test for PositionPartiesFallback end to end"""

    def test_end_to_end_parties_fallback(self, mocker):
        mock_connections = mocker.patch.object(
            SettingsCls, "connections", new_callable=mocker.PropertyMock
        )
        mock_connections.return_value = addict.Dict(
            {
                "tenant-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                    },
                    "MAX_TERMS_SIZE": 1024,
                }
            }
        )

        source_frame = pd.read_pickle(SOURCE_DATA)
        params = Params(source_attribute="__party__", level="level")
        task = PositionPartiesFallback(name="PositionPartiesFallback", params=params)
        result = task.execute(
            source_frame=source_frame,
            params=params,
        )
        expected = pd.read_pickle(EXPECTED_RESULT)
        pd.testing.assert_frame_equal(left=result, right=expected)
