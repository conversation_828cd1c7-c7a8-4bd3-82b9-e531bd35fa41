����      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�	ACCOUNTID��CLIENTID��FUNDID��PORTFOLIOMANAGERID��RISKENTITYID��TRADERID��level�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KM��h�i8�����R�(K�<�NNNJ����J����K t�b�B�                                                                  	       
                     
                                                                                                                                             !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       3       4       5       6       7       8       9       :       ;       <       =       >       ?       @       A       B       C       D       E       F       G       H       I       J       K       L       M       N       O       P       Q       R       S       T       U       V       W       X       Y       Z       [       \       ]       ^       _       `       a       b       c       d       e       f       g       h       i       j       k       l       m       n       o       p       q       r       s       t       u       v       w       x       y       z       {       |       }       ~              �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �                                                              	      
                  
                                                                                                                         !      "      #      $      %      &      '      (      )      *      +      ,      -      .      /      0      1      2      3      4      5      6      7      8      9      :      ;      <      =      >      ?      @      A      B      C      D      E      F      G      H      I      J      K      L      M      N      O      P      Q      R      S      T      U      V      W      X      Y      Z      [      \      ]      ^      _      `      a      b      c      d      e      f      g      h      i      j      k      l      m      n      o      p      q      r      s      t      u      v      w      x      y      z      {      |      }      ~            �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �                                                             	      
                  
                                                            �t�bh-�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KM��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�pandas._libs.missing��NA���hT�client��client��client��client��client��client��client��client�hThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThThT�client_xxxxxxxxxxxxx�h]h]h]h]hThThThThT�client�h^h^h^h^h^h^h^h^h^hThThThThThThThThThThThThThThTet�b�_dtype�hC�StringDtype���)��ubhhK ��h��R�(KKM��h!�]�(G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account_xxxxxxxxxxxx�h�h�h�h��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account�G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �new_fund_id��new_fund_id��254900S94F07IE342O78�h�h��new_fund_id��new_fund_id��new_fund_id�G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      e(G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��portfolio_xxxxxxxxxx�h�h��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio�G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��254900MXWWM823ETP479�h�h�h�h׌risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk�G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      e(G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader3��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader1��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��Trader2��81560058E659F533DB13�j  j  j  j  �Trader2��Trader�j  �Fund�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �	Portfolio�j  j  j  j  j  j  j  j  j  �Risk Entity�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �Account�j  j  j  j  e(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �Client�j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j  j  j  j  j  et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bh-Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h%h'h(h)h*h+et�bh-Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hF�mgr_locs��builtins��slice���KKK��R�u}�(j%  hfj&  hhK ��h��R�(KK��h:�C0                                           �t�bueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.