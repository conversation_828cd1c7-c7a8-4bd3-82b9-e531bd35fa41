���h      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�marketIdentifiers.parties��parties.fileIdentifier�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KM��h�i8�����R�(K�<�NNNJ����J����K t�b�B�                                                                  	       
                     
                                                                                                                                             !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       3       4       5       6       7       8       9       :       ;       <       =       >       ?       @       A       B       C       D       E       F       G       H       I       J       K       L       M       N       O       P       Q       R       S       T       U       V       W       X       Y       Z       [       \       ]       ^       _       `       a       b       c       d       e       f       g       h       i       j       k       l       m       n       o       p       q       r       s       t       u       v       w       x       y       z       {       |       }       ~              �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �                                                              	      
                  
                                                                                                                         !      "      #      $      %      &      '      (      )      *      +      ,      -      .      /      0      1      2      3      4      5      6      7      8      9      :      ;      <      =      >      ?      @      A      B      C      D      E      F      G      H      I      J      K      L      M      N      O      P      Q      R      S      T      U      V      W      X      Y      Z      [      \      ]      ^      _      `      a      b      c      d      e      f      g      h      i      j      k      l      m      n      o      p      q      r      s      t      u      v      w      x      y      z      {      |      }      ~            �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �                                                             	      
                  
                                                            �t�bh(�__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KKM��h!�]�(]�}�(�labelId��
id:trader2��path��
parties.value��type��se_schema.static.market��IdentifierType����OBJECT���R�ua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�id:new_fund_id�hGhHhIhOua]�}�(hE�id:new_fund_id�hGhHhIhOua]�}�(hE�lei:254900s94f07ie342o78�hGhHhIhOua]�}�(hE�lei:254900s94f07ie342o78�hGhHhIhOua]�}�(hE�lei:254900s94f07ie342o78�hGhHhIhOua]�}�(hE�id:new_fund_id�hGhHhIhOua]�}�(hE�id:new_fund_id�hGhHhIhOua]�}�(hE�id:new_fund_id�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�id:portfolio�hGhHhIhOua]�}�(hE�id:portfolio�hGhHhIhOua]�}�(hE�id:portfolio�hGhHhIhOua]�}�(hE�id:portfolio�hGhHhIhOua]�}�(hE�id:portfolio�hGhHhIhOua]�}�(hE�id:portfolio_xxxxxxxxxx�hGhHhIhOua]�}�(hE�id:portfolio_xxxxxxxxxx�hGhHhIhOua]�}�(hE�id:portfolio_xxxxxxxxxx�hGhHhIhOua]�}�(hE�id:portfolio�hGhHhIhOua]�}�(hE�id:portfolio�hGhHhIhOua]�}�(hE�lei:254900mxwwm823etp479�hGhHhIhOua]�}�(hE�lei:254900mxwwm823etp479�hGhHhIhOua]�}�(hE�lei:254900mxwwm823etp479�hGhHhIhOua]�}�(hE�lei:254900mxwwm823etp479�hGhHhIhOua]�}�(hE�lei:254900mxwwm823etp479�hGhHhIhOua]�}�(hE�id:risk�hGhHhIhOua]�}�(hE�id:risk�hGhHhIhOua]�}�(hE�id:risk�hGhHhIhOua]�}�(hE�id:risk�hGhHhIhOua]�}�(hE�id:risk�hGhHhIhOua]�}�(hE�id:risk�hGhHhIhOua]�}�(hE�id:risk�hGhHhIhOua]�}�(hE�id:risk�hGhHhIhOua]�}�(hE�id:risk�hGhHhIhOua]�}�(hE�id:risk�hGhHhIhOua]�}�(hE�id:risk�hGhHhIhOua]�}�(hE�id:risk�hGhHhIhOua]�}�(hE�id:risk�hGhHhIhOua]�}�(hE�id:risk�hGhHhIhOua]�}�(hE�id:risk�hGhHhIhOua]�}�(hE�id:risk�hGhHhIhOua]�}�(hE�id:risk�hGhHhIhOua]�}�(hE�id:risk�hGhHhIhOua]�}�(hE�id:risk�hGhHhIhOua]�}�(hE�id:risk�hGhHhIhOua]�}�(hE�id:risk�hGhHhIhOua]�}�(hE�id:risk�hGhHhIhOua]�}�(hE�id:risk�hGhHhIhOua]�}�(hE�id:risk�hGhHhIhOua]�}�(hE�id:risk�hGhHhIhOua]�}�(hE�id:account_xxxxxxxxxxxx�hGhHhIhOua]�}�(hE�id:account_xxxxxxxxxxxx�hGhHhIhOua]�}�(hE�id:account_xxxxxxxxxxxx�hGhHhIhOua]�}�(hE�id:account_xxxxxxxxxxxx�hGhHhIhOua]�}�(hE�id:account_xxxxxxxxxxxx�hGhHhIhOua]�}�(hE�
id:account�hGhHhIhOua]�}�(hE�
id:account�hGhHhIhOua]�}�(hE�
id:account�hGhHhIhOua]�}�(hE�
id:account�hGhHhIhOua]�}�(hE�
id:account�hGhHhIhOua]�}�(hE�
id:account�hGhHhIhOua]�}�(hE�
id:account�hGhHhIhOua]�}�(hE�
id:account�hGhHhIhOua]�}�(hE�
id:account�hGhHhIhOua]�}�(hE�
id:account�hGhHhIhOua]�}�(hE�
id:account�hGhHhIhOua]�}�(hE�
id:account�hGhHhIhOua]�}�(hE�
id:account�hGhHhIhOua]�}�(hE�
id:account�hGhHhIhOua]�}�(hE�
id:account�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader1�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader3�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOua]�}�(hE�id:client_xxxxxxxxxxxxx�hGhHhIhOua]�}�(hE�id:client_xxxxxxxxxxxxx�hGhHhIhOua]�}�(hE�id:client_xxxxxxxxxxxxx�hGhHhIhOua]�}�(hE�id:client_xxxxxxxxxxxxx�hGhHhIhOua]�}�(hE�id:client_xxxxxxxxxxxxx�hGhHhIhOua�pandas._libs.missing��NA���j:  j:  j:  j:  ]�}�(hE�	id:client�hGhHhIhOua]�}�(hE�	id:client�hGhHhIhOua]�}�(hE�	id:client�hGhHhIhOua]�}�(hE�	id:client�hGhHhIhOua]�}�(hE�	id:client�hGhHhIhOua]�}�(hE�	id:client�hGhHhIhOua]�}�(hE�	id:client�hGhHhIhOua]�}�(hE�	id:client�hGhHhIhOua]�}�(hE�	id:client�hGhHhIhOua]�}�(hE�	id:client�hGhHhIhOuaj:  j:  j:  j:  j:  j:  j:  j:  j:  j:  ]�}�(hE�lei:81560058e659f533db13�hGhHhIhOua]�}�(hE�lei:81560058e659f533db13�hGhHhIhOua]�}�(hE�lei:81560058e659f533db13�hGhHhIhOua]�}�(hE�lei:81560058e659f533db13�hGhHhIhOua]�}�(hE�
id:trader2�hGhHhIhOuaet�bhhK ��h��R�(KKM��h!�]�(�trader2��trader2��new_fund_id��new_fund_id��254900s94f07ie342o78��254900s94f07ie342o78��254900s94f07ie342o78��new_fund_id��new_fund_id��new_fund_id��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��	portfolio��	portfolio��	portfolio��	portfolio��	portfolio��portfolio_xxxxxxxxxx��portfolio_xxxxxxxxxx��portfolio_xxxxxxxxxx��	portfolio��	portfolio��254900mxwwm823etp479��254900mxwwm823etp479��254900mxwwm823etp479��254900mxwwm823etp479��254900mxwwm823etp479��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��risk��account_xxxxxxxxxxxx��account_xxxxxxxxxxxx��account_xxxxxxxxxxxx��account_xxxxxxxxxxxx��account_xxxxxxxxxxxx��account��account��account��account��account��account��account��account��account��account��account��account��account��account��account��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader1��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader3��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��trader2��client_xxxxxxxxxxxxx��client_xxxxxxxxxxxxx��client_xxxxxxxxxxxxx��client_xxxxxxxxxxxxx��client_xxxxxxxxxxxxx�j:  j:  j:  j:  j:  �client��client��client��client��client��client��client��client��client��client�j:  j:  j:  j:  j:  j:  j:  j:  j:  j:  �81560058e659f533db13��81560058e659f533db13��81560058e659f533db13��81560058e659f533db13��trader2�et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bh(Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bh(Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h@�mgr_locs��builtins��slice���K KK��R�u}�(j�  jk  j�  j�  KKK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.