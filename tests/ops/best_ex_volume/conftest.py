from typing import Any
from typing import Dict
from typing import List

import pandas as pd
import pytest


@pytest.fixture()
def best_ex_volume_metrics_result() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "__meta_model__": ["OrderState"],
            "__meta_parent__": ["TESTING_ID_ID:2:NEWO"],
            "&timestamp": [1681121559097],
            "bestExecutionData": [
                {
                    "largeInScale": False,
                    "pendingDisclosure": False,
                    "rts27ValueBand": 1,
                    "timeAcceptExecutePlaced": 0,
                    "timeAcceptExecuteReceived": 0,
                    "timeRfqResponsePlaced": 0,
                    "timeRfqResponseReceived": 0,
                    "timeToFill": 0,
                }
            ],
            "buyer": [
                [
                    {
                        "&id": "SHOCGB21",
                        "&key": "MarketPerson:",
                        "name": "SHOCGB21",
                        "retailOrProfessional": "N/A",
                    }
                ]
            ],
            "buyerDecisionMaker": [
                [
                    {
                        "&id": "testing",
                        "&key": "MarketPerson:",
                        "name": "testing",
                        "retailOrProfessional": "N/A",
                    }
                ]
            ],
            "buyerDecisionMakerFileIdentifier": ["id:testing"],
            "buyerFileIdentifier": ["id:shocgb21"],
            "buySell": ["2"],
            "clientFileIdentifier": ["id:CLIENT"],
            "clientIdentifiers": [
                {
                    "client": [
                        {
                            "&id": "CLIENT",
                            "&key": "MarketCounterparty:",
                            "name": "CLIENT",
                        }
                    ]
                }
            ],
            "counterparty": [
                {"&id": "TEST", "&key": "MarketCounterparty:", "name": "TEST"}
            ],
            "counterpartyFileIdentifier": ["id:testing"],
            "dataSourceName": ["FreeTrade LiquidMetrix"],
            "date": ["2023-03-13"],
            "hierarchy": ["Standalone"],
            "id": ["TESTING_ID_ID"],
            "marketIdentifiers": [
                [
                    {
                        "labelId": "GB0000163088",
                        "path": "instrumentDetails.instrument",
                        "type": "OBJECT",
                    },
                    {"labelId": "id:shocgb21", "path": "buyer", "type": "ARRAY"},
                    {
                        "labelId": "id:testing",
                        "path": "buyerDecisionMaker",
                        "type": "ARRAY",
                    },
                    {
                        "labelId": "id:testing",
                        "path": "reportDetails.executingEntity",
                        "type": "OBJECT",
                    },
                    {"labelId": "id:testing", "path": "seller", "type": "ARRAY"},
                    {
                        "labelId": "id:testing",
                        "path": "sellerDecisionMaker",
                        "type": "ARRAY",
                    },
                    {
                        "labelId": "id:shocgb21",
                        "path": "counterparty",
                        "type": "OBJECT",
                    },
                    {
                        "labelId": "id:CLIENT",
                        "path": "clientIdentifiers.client",
                        "type": "ARRAY",
                    },
                ]
            ],
            "marDetails": [{"isPersonalAccountDealing": False}],
            "reportDetails": [
                {
                    "executingEntity": {
                        "&id": "testing",
                        "&key": "MarketCounterparty:",
                        "fileIdentifier": "id:testing",
                        "name": "testing",
                    },
                    "transactionRefNo": "TESTING_ID_ID",
                }
            ],
            "seller": [
                [
                    {
                        "&id": "testing",
                        "&key": "MarketPerson:",
                        "name": "testing",
                        "retailOrProfessional": "N/A",
                    }
                ]
            ],
            "sellerDecisionMaker": [
                [
                    {
                        "&id": "testing",
                        "&key": "MarketPerson:",
                        "name": "testing",
                        "retailOrProfessional": "N/A",
                    }
                ]
            ],
            "sellerDecisionMakerFileIdentifier": ["id:testing"],
            "sellerFileIdentifier": ["id:testing"],
            "sourceIndex": ["2670"],
            "sourceKey": ["s3://test.dev.steeleye.co/test.csv"],
            "instrumentDetails.instrument": [
                {
                    "&id": "*******************",
                    "&key": "FcaFirdsInstrument:*******************:*************",
                    "cfiAttribute1": "Voting",
                    "cfiAttribute2": "Free",
                    "cfiAttribute3": "Fully Paid",
                    "cfiAttribute4": "Registered",
                    "cfiCategory": "Equity",
                    "cfiGroup": "Common / Ordinary shares",
                    "commoditiesOrEmissionAllowanceDerivativeInd": False,
                    "ext": {
                        "aii": {"daily": "XXXXGBPO"},
                        "alternativeInstrumentIdentifier": "XXXXGBPO",
                        "bestExAssetClassMain": "Equity",
                        "instrumentIdCodeType": "ID",
                        "instrumentUniqueIdentifier": "*******************",
                        "mifirEligible": True,
                        "onFIRDS": True,
                        "pricingReferences": {"ICE": "isin/GB0000163088/GBP"},
                        "venueInEEA": True,
                    },
                    "instrumentClassification": "ESVUFR",
                    "instrumentClassificationEMIRContractType": "OT",
                    "instrumentFullName": "SPEEDY HIRE PLC ORD 5P",
                    "instrumentIdCode": "GB0000163088",
                    "isCreatedThroughFallback": False,
                    "issuerOrOperatorOfTradingVenueId": "213800U78SIYAZDYXM61",
                    "notionalCurrency1": "GBP",
                    "sourceKey": "FULINS_E_20230408_01of01.xml",
                    "venue": {
                        "admissionToTradingApprovalDate": "2008-09-19T00:00:00.000Z",
                        "admissionToTradingOrFirstTradeDate": "2008-09-22T00:00:00.000Z",
                        "admissionToTradingRequestDate": "2008-09-19T00:00:00.000Z",
                        "financialInstrumentShortName": "SPEY HIRE/PAR VTG FPD 0.05",
                        "issuerRequestForAdmissionToTrading": True,
                        "tradingVenue": "XLON",
                    },
                }
            ],
            "priceFormingData.initialQuantity": [10.0],
            "priceFormingData.price": [0.3511],
            "priceFormingData.tradedQuantity": [10.0],
            "executionDetails.buySellIndicator": ["SELL"],
            "executionDetails.limitPrice": [0.3511],
            "executionDetails.orderStatus": ["FILL"],
            "executionDetails.orderType": ["Market"],
            "executionDetails.tradingCapacity": ["AOTC"],
            "executionDetails.validityPeriod": [["GTDV"]],
            "orderIdentifiers.internalOrderIdCode": ["CLIENT"],
            "orderIdentifiers.orderIdCode": ["TESTING_ID_ID"],
            "orderIdentifiers.transactionRefNo": ["TESTING_ID_ID_FILL"],
            "transactionDetails.buySellIndicator": ["SELL"],
            "transactionDetails.price": [0.3511],
            "transactionDetails.priceCurrency": ["GBP"],
            "transactionDetails.tradingCapacity": ["AOTC"],
            "transactionDetails.tradingDateTime": ["2023-03-13T09:06:09.384Z"],
            "transactionDetails.ultimateVenue": ["XLON"],
            "transactionDetails.venue": ["XOFF"],
            "timestamps.orderReceived": ["2023-03-13T09:06:08.948Z"],
            "timestamps.orderStatusUpdated": ["2023-03-13T09:06:09.384Z"],
            "timestamps.orderSubmitted": ["2023-03-13T09:06:08.948Z"],
            "timestamps.tradingDateTime": ["2023-03-13T09:06:09.384Z"],
            "transactionDetails.priceNotation": ["MONE"],
            "bestExecutionData.orderVolume": [pd.NA],
            "bestExecutionData.transactionVolume": [
                {
                    "native": 2290.2601,
                    "nativeCurrency": "GBP",
                    "ecbRefRate": {
                        "refRateDate": "2023-03-13T13:15:00.000000Z",
                        "EUR": 2591.6714948511935,
                        "USD": 2774.643502387688,
                        "JPY": 367913.68540907546,
                        "BGN": 5068.791109629964,
                        "CZK": 61544.422988231294,
                        "DKK": 19293.439276270226,
                        "GBP": 2290.2601,
                        "HUF": 1013213.9709120741,
                        "PLN": 12166.342665429444,
                        "RON": 12742.73040588435,
                        "SEK": 29646.130229602804,
                        "CHF": 2526.8797074799136,
                        "NOK": 29517.3241563087,
                        "TRY": 52644.88224206177,
                        "AUD": 4163.779423627928,
                        "BRL": 14579.707161434875,
                        "CAD": 3821.4196191580845,
                        "CNY": 19026.237945151068,
                        "HKD": 21760.451372219075,
                        "IDR": 42757137.15490551,
                        "ILS": 10059.313740115424,
                        "INR": 228186.3084356682,
                        "KRW": 3626240.8388808416,
                        "MXN": 52334.39999697861,
                        "MYR": 12477.60241196107,
                        "NZD": 4464.15414988118,
                        "PHP": 152576.***********,
                        "SGD": 3742.************,
                        "THB": 95891.***********,
                        "ZAR": 50419.***********,
                    },
                }
            ],
        }
    )


@pytest.fixture()
def elastic_raw_result() -> List[Dict[Any, Any]]:
    return [
        {
            "_index": "test_order_20230220",
            "_type": "OrderState",
            "_id": "TESTING_ID",
            "_score": 0.0,
            "_routing": "TESTING_ID_ID:2:NEWO",
            "_parent": "TESTING_ID_ID:2:NEWO",
            "_source": {
                "date": "2023-03-13",
                "seller": [
                    {
                        "&id": "testing",
                        "&key": "MarketPerson:",
                        "name": "testing",
                        "retailOrProfessional": "N/A",
                    }
                ],
                "sourceKey": "s3://test.dev.steeleye.co/test.csv",
                "&id": "TESTING_ID",
                "transactionDetails": {
                    "buySellIndicator": "SELL",
                    "price": 0.3511,
                    "priceCurrency": "GBP",
                    "tradingCapacity": "AOTC",
                    "tradingDateTime": "2023-03-13T09:06:09.384000Z",
                    "ultimateVenue": "XLON",
                    "venue": "XOFF",
                },
                "buyerFileIdentifier": "id:shocgb21",
                "reportDetails": {
                    "executingEntity": {
                        "&id": "testing",
                        "&key": "MarketCounterparty:",
                        "fileIdentifier": "id:testing",
                        "name": "testing",
                    },
                    "transactionRefNo": "TESTING_ID_ID",
                },
                "timestamps": {
                    "orderReceived": "2023-03-13T09:06:08.948000Z",
                    "orderStatusUpdated": "2023-03-13T09:06:09.384000Z",
                    "orderSubmitted": "2023-03-13T09:06:08.948000Z",
                    "tradingDateTime": "2023-03-13T09:06:09.384000Z",
                },
                "&parent": "TESTING_ID_ID:2:NEWO",
                "executionDetails": {
                    "buySellIndicator": "SELL",
                    "limitPrice": 0.3511,
                    "orderStatus": "FILL",
                    "orderType": "Market",
                    "tradingCapacity": "AOTC",
                    "validityPeriod": ["GTDV"],
                },
                "sourceIndex": "2670",
                "marDetails": {"isPersonalAccountDealing": False},
                "sellerDecisionMakerFileIdentifier": "id:testing",
                "&model": "OrderState",
                "&version": 1,
                "instrumentDetails": {
                    "instrument": {
                        "&id": "*******************",
                        "&key": "FcaFirdsInstrument:*******************:*************",
                        "cfiAttribute1": "Voting",
                        "cfiAttribute2": "Free",
                        "cfiAttribute3": "Fully Paid",
                        "cfiAttribute4": "Registered",
                        "cfiCategory": "Equity",
                        "cfiGroup": "Common / Ordinary shares",
                        "commoditiesOrEmissionAllowanceDerivativeInd": False,
                        "ext": {
                            "aii": {"daily": "XXXXGBPO"},
                            "alternativeInstrumentIdentifier": "XXXXGBPO",
                            "bestExAssetClassMain": "Equity",
                            "instrumentIdCodeType": "ID",
                            "instrumentUniqueIdentifier": "*******************",
                            "mifirEligible": True,
                            "onFIRDS": True,
                            "pricingReferences": {"ICE": "isin/GB0000163088/GBP"},
                            "venueInEEA": True,
                        },
                        "instrumentClassification": "ESVUFR",
                        "instrumentClassificationEMIRContractType": "OT",
                        "instrumentFullName": "SPEEDY HIRE PLC ORD 5P",
                        "instrumentIdCode": "GB0000163088",
                        "isCreatedThroughFallback": False,
                        "issuerOrOperatorOfTradingVenueId": "213800U78SIYAZDYXM61",
                        "notionalCurrency1": "GBP",
                        "sourceKey": "FULINS_E_20230408_01of01.xml",
                        "venue": {
                            "admissionToTradingApprovalDate": "2008-09-19T00:00:00",
                            "admissionToTradingOrFirstTradeDate": "2008-09-22T00:00:00",
                            "admissionToTradingRequestDate": "2008-09-19T00:00:00",
                            "financialInstrumentShortName": "SPEY HIRE/PAR VTG FPD 0.05",
                            "issuerRequestForAdmissionToTrading": True,
                            "tradingVenue": "XLON",
                        },
                    }
                },
                "buyerDecisionMaker": [
                    {
                        "&id": "testing",
                        "&key": "MarketPerson:",
                        "name": "testing",
                        "retailOrProfessional": "N/A",
                    }
                ],
                "id": "TESTING_ID_ID",
                "&timestamp": 1681121559097,
                "buyerDecisionMakerFileIdentifier": "id:testing",
                "buySell": "2",
                "clientFileIdentifier": "id:CLIENT",
                "clientIdentifiers": {
                    "client": [
                        {
                            "&id": "CLIENT",
                            "&key": "MarketCounterparty:",
                            "name": "CLIENT",
                        }
                    ]
                },
                "marketIdentifiers": [
                    {
                        "labelId": "GB0000163088",
                        "path": "instrumentDetails.instrument",
                        "type": "OBJECT",
                    },
                    {
                        "labelId": "id:shocgb21",
                        "path": "buyer",
                        "type": "ARRAY",
                    },
                    {
                        "labelId": "id:testing",
                        "path": "buyerDecisionMaker",
                        "type": "ARRAY",
                    },
                    {
                        "labelId": "id:testing",
                        "path": "reportDetails.executingEntity",
                        "type": "OBJECT",
                    },
                    {
                        "labelId": "id:testing",
                        "path": "seller",
                        "type": "ARRAY",
                    },
                    {
                        "labelId": "id:testing",
                        "path": "sellerDecisionMaker",
                        "type": "ARRAY",
                    },
                    {
                        "labelId": "id:shocgb21",
                        "path": "counterparty",
                        "type": "OBJECT",
                    },
                    {
                        "labelId": "id:CLIENT",
                        "path": "clientIdentifiers.client",
                        "type": "ARRAY",
                    },
                ],
                "hierarchy": "Standalone",
                "orderIdentifiers": {
                    "internalOrderIdCode": "CLIENT",
                    "orderIdCode": "TESTING_ID_ID",
                    "transactionRefNo": "TESTING_ID_ID_FILL",
                },
                "sellerDecisionMaker": [
                    {
                        "&id": "testing",
                        "&key": "MarketPerson:",
                        "name": "testing",
                        "retailOrProfessional": "N/A",
                    }
                ],
                "sellerFileIdentifier": "id:testing",
                "&key": "OrderState:TESTING_ID:1681121559097",
                "bestExecutionData": {
                    "largeInScale": False,
                    "pendingDisclosure": False,
                    "rts27ValueBand": 1,
                    "timeAcceptExecutePlaced": 0,
                    "timeAcceptExecuteReceived": 0,
                    "timeRfqResponsePlaced": 0,
                    "timeRfqResponseReceived": 0,
                    "timeToFill": 0,
                },
                "dataSourceName": "FreeTrade LiquidMetrix",
                "buyer": [
                    {
                        "&id": "SHOCGB21",
                        "&key": "MarketPerson:",
                        "name": "SHOCGB21",
                        "retailOrProfessional": "N/A",
                    }
                ],
                "priceFormingData": {
                    "initialQuantity": 10.0,
                    "price": 0.3511,
                    "tradedQuantity": 10.0,
                },
                "counterparty": {
                    "&id": "TEST",
                    "&key": "MarketCounterparty:",
                    "name": "TEST",
                },
                "counterpartyFileIdentifier": "id:testing",
                "&validationErrors": [
                    {
                        "field_path": "tradersAlgosWaiversIndicators.investmentDecisionWithinFirm.structure.desks.0.name",
                        "message": "'Desk' must be populated in the event of discretionary order / trade flow",
                        "code": "SE_DV-342",
                        "category": "Parties",
                        "modules_affected": [
                            "Best Execution",
                            "Market Abuse",
                            "Orders",
                        ],
                        "severity": "MEDIUM",
                        "source": "steeleye",
                    },
                    {
                        "field_path": "executionDetails.orderType",
                        "message": "'Order Type' must be 'Limit' when 'Limit Price' is populated",
                        "code": "SE_DV-343",
                        "category": "Prices",
                        "modules_affected": [
                            "Best Execution",
                            "Market Abuse",
                            "Orders",
                        ],
                        "severity": "MEDIUM",
                        "source": "steeleye",
                    },
                ],
                "&user": "system",
            },
        }
    ]


@pytest.fixture()
def expected_elastic_result_df() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "__meta_model__": ["OrderState"],
            "__meta_parent__": ["TESTING_ID_ID:2:NEWO"],
            "&timestamp": [1681121559097],
            "bestExecutionData": [
                {
                    "largeInScale": False,
                    "pendingDisclosure": False,
                    "rts27ValueBand": 1,
                    "timeAcceptExecutePlaced": 0,
                    "timeAcceptExecuteReceived": 0,
                    "timeRfqResponsePlaced": 0,
                    "timeRfqResponseReceived": 0,
                    "timeToFill": 0,
                }
            ],
            "buyer": [
                [
                    {
                        "&id": "SHOCGB21",
                        "&key": "MarketPerson:",
                        "name": "SHOCGB21",
                        "retailOrProfessional": "N/A",
                    }
                ]
            ],
            "buyerDecisionMaker": [
                [
                    {
                        "&id": "testing",
                        "&key": "MarketPerson:",
                        "name": "testing",
                        "retailOrProfessional": "N/A",
                    }
                ]
            ],
            "buyerDecisionMakerFileIdentifier": ["id:testing"],
            "buyerFileIdentifier": ["id:shocgb21"],
            "buySell": ["2"],
            "clientFileIdentifier": ["id:CLIENT"],
            "clientIdentifiers": [
                {
                    "client": [
                        {
                            "&id": "CLIENT",
                            "&key": "MarketCounterparty:",
                            "name": "CLIENT",
                        }
                    ]
                }
            ],
            "counterparty": [
                {"&id": "TEST", "&key": "MarketCounterparty:", "name": "TEST"}
            ],
            "counterpartyFileIdentifier": ["id:testing"],
            "dataSourceName": ["FreeTrade LiquidMetrix"],
            "date": ["2023-03-13T00:00:00.000Z"],
            "hierarchy": ["Standalone"],
            "id": ["TESTING_ID_ID"],
            "marketIdentifiers": [
                [
                    {
                        "labelId": "GB0000163088",
                        "path": "instrumentDetails.instrument",
                        "type": "OBJECT",
                    },
                    {"labelId": "id:shocgb21", "path": "buyer", "type": "ARRAY"},
                    {
                        "labelId": "id:testing",
                        "path": "buyerDecisionMaker",
                        "type": "ARRAY",
                    },
                    {
                        "labelId": "id:testing",
                        "path": "reportDetails.executingEntity",
                        "type": "OBJECT",
                    },
                    {"labelId": "id:testing", "path": "seller", "type": "ARRAY"},
                    {
                        "labelId": "id:testing",
                        "path": "sellerDecisionMaker",
                        "type": "ARRAY",
                    },
                    {
                        "labelId": "id:shocgb21",
                        "path": "counterparty",
                        "type": "OBJECT",
                    },
                    {
                        "labelId": "id:CLIENT",
                        "path": "clientIdentifiers.client",
                        "type": "ARRAY",
                    },
                ]
            ],
            "marDetails": [{"isPersonalAccountDealing": False}],
            "reportDetails": [
                {
                    "executingEntity": {
                        "&id": "testing",
                        "&key": "MarketCounterparty:",
                        "fileIdentifier": "id:testing",
                        "name": "testing",
                    },
                    "transactionRefNo": "TESTING_ID_ID",
                }
            ],
            "seller": [
                [
                    {
                        "&id": "testing",
                        "&key": "MarketPerson:",
                        "name": "testing",
                        "retailOrProfessional": "N/A",
                    }
                ]
            ],
            "sellerDecisionMaker": [
                [
                    {
                        "&id": "testing",
                        "&key": "MarketPerson:",
                        "name": "testing",
                        "retailOrProfessional": "N/A",
                    }
                ]
            ],
            "sellerDecisionMakerFileIdentifier": ["id:testing"],
            "sellerFileIdentifier": ["id:testing"],
            "sourceIndex": ["2670"],
            "sourceKey": ["s3://test.dev.steeleye.co/test.csv"],
            "instrumentDetails.instrument": [
                {
                    "&id": "*******************",
                    "&key": "FcaFirdsInstrument:*******************:*************",
                    "cfiAttribute1": "Voting",
                    "cfiAttribute2": "Free",
                    "cfiAttribute3": "Fully Paid",
                    "cfiAttribute4": "Registered",
                    "cfiCategory": "Equity",
                    "cfiGroup": "Common / Ordinary shares",
                    "commoditiesOrEmissionAllowanceDerivativeInd": False,
                    "ext": {
                        "aii": {"daily": "XXXXGBPO"},
                        "alternativeInstrumentIdentifier": "XXXXGBPO",
                        "bestExAssetClassMain": "Equity",
                        "instrumentIdCodeType": "ID",
                        "instrumentUniqueIdentifier": "*******************",
                        "mifirEligible": True,
                        "onFIRDS": True,
                        "pricingReferences": {"ICE": "isin/GB0000163088/GBP"},
                        "venueInEEA": True,
                    },
                    "instrumentClassification": "ESVUFR",
                    "instrumentClassificationEMIRContractType": "OT",
                    "instrumentFullName": "SPEEDY HIRE PLC ORD 5P",
                    "instrumentIdCode": "GB0000163088",
                    "isCreatedThroughFallback": False,
                    "issuerOrOperatorOfTradingVenueId": "213800U78SIYAZDYXM61",
                    "notionalCurrency1": "GBP",
                    "sourceKey": "FULINS_E_20230408_01of01.xml",
                    "venue": {
                        "admissionToTradingApprovalDate": "2008-09-19T00:00:00.000Z",
                        "admissionToTradingOrFirstTradeDate": "2008-09-22T00:00:00.000Z",
                        "admissionToTradingRequestDate": "2008-09-19T00:00:00.000Z",
                        "financialInstrumentShortName": "SPEY HIRE/PAR VTG FPD 0.05",
                        "issuerRequestForAdmissionToTrading": True,
                        "tradingVenue": "XLON",
                    },
                }
            ],
            "priceFormingData.initialQuantity": [10.0],
            "priceFormingData.price": [0.3511],
            "priceFormingData.tradedQuantity": [10.0],
            "executionDetails.buySellIndicator": ["SELL"],
            "executionDetails.limitPrice": [0.3511],
            "executionDetails.orderStatus": ["FILL"],
            "executionDetails.orderType": ["Market"],
            "executionDetails.tradingCapacity": ["AOTC"],
            "executionDetails.validityPeriod": [["GTDV"]],
            "orderIdentifiers.internalOrderIdCode": ["CLIENT"],
            "orderIdentifiers.orderIdCode": ["TESTING_ID_ID"],
            "orderIdentifiers.transactionRefNo": ["TESTING_ID_ID_FILL"],
            "transactionDetails.buySellIndicator": ["SELL"],
            "transactionDetails.price": [0.3511],
            "transactionDetails.priceCurrency": ["GBP"],
            "transactionDetails.tradingCapacity": ["AOTC"],
            "transactionDetails.tradingDateTime": ["2023-03-13T09:06:09.384Z"],
            "transactionDetails.ultimateVenue": ["XLON"],
            "transactionDetails.venue": ["XOFF"],
            "timestamps.orderReceived": ["2023-03-13T09:06:08.948Z"],
            "timestamps.orderStatusUpdated": ["2023-03-13T09:06:09.384Z"],
            "timestamps.orderSubmitted": ["2023-03-13T09:06:08.948Z"],
            "timestamps.tradingDateTime": ["2023-03-13T09:06:09.384Z"],
            "transactionDetails.priceNotation": ["MONE"],
        }
    )


@pytest.fixture()
def best_ex_volume_result() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "bestExecutionData.orderVolume": [pd.NA],
            "bestExecutionData.transactionVolume": [
                {
                    "native": 2290.2601,
                    "nativeCurrency": "GBP",
                    "ecbRefRate": {
                        "refRateDate": "2023-03-13T13:15:00.000000Z",
                        "EUR": 2591.6714948511935,
                        "USD": 2774.643502387688,
                        "JPY": 367913.68540907546,
                        "BGN": 5068.791109629964,
                        "CZK": 61544.422988231294,
                        "DKK": 19293.439276270226,
                        "GBP": 2290.2601,
                        "HUF": 1013213.9709120741,
                        "PLN": 12166.342665429444,
                        "RON": 12742.73040588435,
                        "SEK": 29646.130229602804,
                        "CHF": 2526.8797074799136,
                        "NOK": 29517.3241563087,
                        "TRY": 52644.88224206177,
                        "AUD": 4163.779423627928,
                        "BRL": 14579.707161434875,
                        "CAD": 3821.4196191580845,
                        "CNY": 19026.237945151068,
                        "HKD": 21760.451372219075,
                        "IDR": 42757137.15490551,
                        "ILS": 10059.313740115424,
                        "INR": 228186.3084356682,
                        "KRW": 3626240.8388808416,
                        "MXN": 52334.39999697861,
                        "MYR": 12477.60241196107,
                        "NZD": 4464.15414988118,
                        "PHP": 152576.***********,
                        "SGD": 3742.************,
                        "THB": 95891.***********,
                        "ZAR": 50419.***********,
                    },
                }
            ],
        }
    )
