from pathlib import Path

import pytest
from prefect.engine.signals import FAIL
from prefect.engine.signals import SKIP

from swarm_tasks.io.read.filter_text_file_lines import FilterTextFileLines
from swarm_tasks.io.read.filter_text_file_lines import Params


SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data/txt")
TEXT_FILE_PATH = TEST_FILES_DIR.joinpath("test_filter_text_file_lines").as_posix()


@pytest.fixture()
def params() -> Params:
    return Params(
        line_regex="<message_type>CONVERSATION</message_type>",
        target_extension=".xml",
        xml_root_tag="root",
    )


@pytest.fixture()
def params_regex_does_not_match() -> Params:
    return Params(
        line_regex="<message_type>123</message_type>",
        target_extension=".xml",
        xml_root_tag="root",
    )


class TestFilterTextFileLines:
    def test_root_validator(self):
        with pytest.raises(ValueError) as e:
            Params(target_extension="csv", xml_root_tag="root", line_regex="foo")

        assert e.match(
            "`xml_root_tag` parameter can only be specified if `target_extension` is `.xml`"
        )

    def test_missing_file(self, params):
        file_url = "i_am_a_file_that_does_not_exist.xml"
        task = FilterTextFileLines(name="test", params=params)
        with pytest.raises(FAIL) as e:
            task.execute(params=params, file_url=file_url)
        assert e.match(
            f"It was not possible to read {file_url} due to the following error:"
        )

    def test_file_does_not_match_regex(self, params_regex_does_not_match):
        task = FilterTextFileLines(name="test", params=params_regex_does_not_match)
        with pytest.raises(SKIP) as e:
            task.execute(params=params_regex_does_not_match, file_url=TEXT_FILE_PATH)
        assert e.match(
            f"The pattern '{params_regex_does_not_match.line_regex}' was not found in the file {TEXT_FILE_PATH}"
        )

    def test_file_end_to_end(self, params):
        task = FilterTextFileLines(name="test", params=params)
        result = task.execute(params=params, file_url=TEXT_FILE_PATH)

        with open(result.path, "r") as result_file:
            result_list = result_file.readlines()

        # root begin tag + 2 tags with conversations + root end tag
        assert (
            params.line_regex in result_list[0] and params.line_regex in result_list[1]
        )
        assert result_list[0].startswith(f"<{params.xml_root_tag}>")
        assert result_list[1].endswith(f"</{params.xml_root_tag}>")
