from pathlib import Path

import addict
import pandas as pd
import pytest
from prefect.engine import signals
from se_core_tasks.core.core_dataclasses import ExtractPathResult
from se_core_tasks.core.core_dataclasses import FileSplitterResult
from swarm.task.auditor import Auditor

from swarm_tasks.io.read import nested_json_file_splitter
from swarm_tasks.io.read.nested_json_file_splitter import NestedJsonFileSplitter
from swarm_tasks.io.read.nested_json_file_splitter import Params


SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data/json")
EMPTY_FILE_PATH = ExtractPathResult(path=TEST_FILES_DIR.joinpath(r"empty-file.json"))
EMPTY_JSON_ARRAY_FILE_PATH = ExtractPathResult(
    path=TEST_FILES_DIR.joinpath(r"empty-json-array.json")
)
INVALID_JSON_FILE_PATH = ExtractPathResult(
    path=TEST_FILES_DIR.joinpath(r"invalid-json.json")
)

SOME_ROWS_NULL_JSON_FILE_PATH = ExtractPathResult(
    path=TEST_FILES_DIR.joinpath(r"nested-json-with-some-null-rows.json")
)
EXPECTED_SOME_ROWS_NULL_JSON_FILE_PATH = ExtractPathResult(
    path=SCRIPT_PATH.joinpath(
        r"data/expected_result/expected_result_test_unstack_when_some_rows_are_null.pkl"
    )
)

SINGLE_ITEM_NESTED_FILE_PATH = ExtractPathResult(
    path=TEST_FILES_DIR.joinpath(r"nested-json-1.json")
)

TWO_ITEMS_NESTED_FILE_PATH = ExtractPathResult(
    path=TEST_FILES_DIR.joinpath(r"nested-json-2.json")
)

NON_EXISTENT_FILE_PATH = ExtractPathResult(
    path=TEST_FILES_DIR.joinpath(r"non-existent-file.json")
)

NORMALISE_COLUMNS_FILE_PATH = ExtractPathResult(
    path=TEST_FILES_DIR.joinpath(r"normalise-columns.json")
)


@pytest.fixture()
def expected_result_1_nested_json_record_creates_2_rows_in_df() -> pd.DataFrame:
    """Expected result for nested-json-1.json (which contains one json record with a nested column)"""
    df = pd.DataFrame(
        {
            "__swarm_raw_index__": [0, 1],
            "tradeId": [9662, 9662],
            "settlementDate": ["2019-05-09T00:00:00", "2019-05-09T00:00:00"],
            "isin": ["USF1R15XK938", "USF1R15XK938"],
            "tradeCurrency": ["USD", "USD"],
            "maturityDate": ["2164-06-19T00:00:00", "2164-06-19T00:00:00"],
            "issueDate": ["2019-03-25T00:00:00", "2019-03-25T00:00:00"],
            "yield": [6.20000000, 6.20000000],
            "spread": [0.0, 0.0],
            "tradeDate": ["2019-05-07T00:00:00", "2019-05-07T00:00:00"],
            "clientFwcId": ["FWC-UK-35", "FWC-UK-85"],
            "clientTraderName": ["NICK GRAY", "MATIAS NUIN"],
            "side": ["Buy", "Sell"],
            "netConsideration": [-5782317.14, 5785136.14],
            "grossConsideration": [-5736665.00, 5739484.00],
            "price": [101.75000000, 101.80000000],
            "quantity": [5638000, 5638000],
            "accruedInterest": [45652.14000000, 45652.14000000],
            "tradeBookingId": [23434, 23435],
            "utcExecutionTime": ["09:44:19", "09:44:19"],
            "executionVenue": ["KBLM", "KBLM"],
            "associatedEntity.type": ["Broker", "Broker"],
            "associatedEntity.firmWideId": ["FWB-15", "FWB-15"],
        }
    )
    return df


@pytest.fixture()
def expected_result_2_nested_json_records_create_5_rows_in_df() -> pd.DataFrame:
    """Expected result for nested-json-2.json (which contains 2 json records with 1 nested column each).
    The result will contain 4 records"""
    df = pd.DataFrame(
        {
            "__swarm_raw_index__": [0, 1, 2, 3, 4],
            "tradeId": [9662, 9662, 9679, 9679, 9679],
            "settlementDate": [
                "2019-05-09T00:00:00",
                "2019-05-09T00:00:00",
                "2019-05-10T00:00:00",
                "2019-05-10T00:00:00",
                "2019-05-10T00:00:00",
            ],
            "isin": [
                "USF1R15XK938",
                "USF1R15XK938",
                "XS1586367945",
                "XS1586367945",
                "XS1586367945",
            ],
            "tradeCurrency": ["USD", "USD", "USD", "USD", "USD"],
            "maturityDate": [
                "2164-06-19T00:00:00",
                "2164-06-19T00:00:00",
                "2164-06-19T00:00:00",
                "2164-06-19T00:00:00",
                "2164-06-19T00:00:00",
            ],
            "issueDate": [
                "2019-03-25T00:00:00",
                "2019-03-25T00:00:00",
                "2017-03-28T00:00:00",
                "2017-03-28T00:00:00",
                "2017-03-28T00:00:00",
            ],
            "yield": [6.20000000, 6.20000000, 7.91000000, 7.91000000, 7.91000000],
            "spread": [0.0, 0.0, 0.0, 0.0, 0.0],
            "tradeDate": [
                "2019-05-07T00:00:00",
                "2019-05-07T00:00:00",
                "2019-05-08T00:00:00",
                "2019-05-08T00:00:00",
                "2019-05-08T00:00:00",
            ],
            "clientFwcId": [
                "FWC-UK-35",
                "FWC-UK-85",
                "FWC-UK-89",
                "FWC-UK-1",
                "FWC-UK-35",
            ],
            "clientTraderName": [
                "NICK GRAY",
                "MATIAS NUIN",
                "REISS LEA",
                "OMAR MECHRI",
                "NICK GRAY",
            ],
            "side": ["Buy", "Sell", "Buy", "Sell", "Sell"],
            "netConsideration": [
                -5782317.14,
                5785136.14,
                -4678229.17,
                1020126.46,
                2724184.38,
            ],
            "grossConsideration": [
                -5736665.00,
                5739484.00,
                -4642500.00,
                1012337.50,
                2703390.00,
            ],
            "price": [
                101.75000000,
                101.80000000,
                92.85000000,
                92.87500000,
                92.90000000,
            ],
            "quantity": [5638000, 5638000, 5000000, 1090000, 2910000],
            "accruedInterest": [
                45652.14000000,
                45652.14000000,
                35729.17000000,
                7788.96000000,
                20794.38000000,
            ],
            "tradeBookingId": [23434, 23435, 23472, 23473, 23474],
            "utcExecutionTime": [
                "09:44:19",
                "09:44:19",
                "08:36:44",
                "08:38:03",
                "08:38:03",
            ],
            "executionVenue": ["KBLM", "KBLM", "KBLM", "KBLM", "KBLM"],
            "associatedEntitytype": ["Broker", "Broker", "Broker", "Broker", "Broker"],
            "associatedEntityfirmWideId": [
                "FWB-15",
                "FWB-15",
                "FWB-15",
                "FWB-14",
                "FWB-14",
            ],
            "__source_index__": [0, 0, 1, 1, 1],
        }
    )
    return df


class TestNestedJsonFileSplitter:
    @staticmethod
    def teardown_method():
        for i in range(3):
            if SCRIPT_PATH.joinpath(f"batch_{i}.csv").exists():
                SCRIPT_PATH.joinpath(f"batch_{i}.csv").unlink()

    def test_empty_file_raises_skip(self):
        params = Params(
            columns_to_unstack=["legs"],
            unstacked_columns_name_format="fully_qualified",
            drop_source_index=True,
        )
        task = NestedJsonFileSplitter(name="NestedJsonFileSplitter", params=params)

        with pytest.raises(signals.SKIP):
            task.execute(params=params, extractor_result=EMPTY_FILE_PATH)

    def test_empty_array_file_raises_skip(self):
        params = Params(
            columns_to_unstack=["legs"],
            unstacked_columns_name_format="fully_qualified",
            drop_source_index=True,
        )
        task = NestedJsonFileSplitter(name="NestedJsonFileSplitter", params=params)

        with pytest.raises(signals.SKIP):
            task.execute(params=params, extractor_result=EMPTY_JSON_ARRAY_FILE_PATH)

    def test_invalid_json_file_raises_skip(self):
        params = Params(
            columns_to_unstack=["legs"],
            unstacked_columns_name_format="fully_qualified",
            drop_source_index=True,
        )
        task = NestedJsonFileSplitter(name="NestedJsonFileSplitter", params=params)

        with pytest.raises(signals.SKIP):
            task.execute(params=params, extractor_result=INVALID_JSON_FILE_PATH)

    def test_non_existent_file_raises_file_not_found_exception(self):
        params = Params(
            columns_to_unstack=["legs"],
            unstacked_columns_name_format="fully_qualified",
            drop_source_index=True,
        )
        task = NestedJsonFileSplitter(name="NestedJsonFileSplitter", params=params)

        with pytest.raises(FileNotFoundError):
            task.execute(params=params, extractor_result=NON_EXISTENT_FILE_PATH)

    def test_file_with_single_nested_item_returns_two_row_dataframe(
        self, expected_result_1_nested_json_record_creates_2_rows_in_df
    ):
        nested_json_file_splitter.context = addict.Dict(
            {"swarm": {"sources_dir": SCRIPT_PATH}}
        )

        params = Params(
            columns_to_unstack=["legs"],
            unstacked_columns_name_format="fully_qualified",
            drop_source_index=True,
        )
        task = NestedJsonFileSplitter(name="NestedJsonFileSplitter", params=params)
        result = task.execute(params, extractor_result=SINGLE_ITEM_NESTED_FILE_PATH)
        # Result will be a list of FileSplitterResult objects (each with 2 params path and batch_index)
        assert len(result) == 1
        df = pd.read_csv(result[0].path)
        assert df.equals(expected_result_1_nested_json_record_creates_2_rows_in_df)

    def test_file_with_two_nested_items_returns_five_row_dataframe(
        self, expected_result_2_nested_json_records_create_5_rows_in_df
    ):
        nested_json_file_splitter.context = addict.Dict(
            {"swarm": {"sources_dir": SCRIPT_PATH}}
        )

        params = Params(
            columns_to_unstack=["legs"],
            unstacked_columns_name_format="fully_qualified_no_symbols",
            source_index_field_name="__source_index__",
            drop_source_index=False,
        )
        task = NestedJsonFileSplitter(name="NestedJsonFileSplitter", params=params)
        result = task.execute(params, extractor_result=TWO_ITEMS_NESTED_FILE_PATH)
        # Result will be a list of FileSplitterResult objects (each with 2 params path and batch_index)
        assert len(result) == 1
        df = pd.read_csv(result[0].path)
        assert df.equals(expected_result_2_nested_json_records_create_5_rows_in_df)

    def test_chunk_size_returns_expected_number_of_batch_files(self):
        nested_json_file_splitter.context = addict.Dict(
            {"swarm": {"sources_dir": SCRIPT_PATH}}
        )
        expected = [
            FileSplitterResult(path=SCRIPT_PATH.joinpath("batch_0.csv"), batch_index=0),
            FileSplitterResult(path=SCRIPT_PATH.joinpath("batch_1.csv"), batch_index=1),
            FileSplitterResult(path=SCRIPT_PATH.joinpath("batch_2.csv"), batch_index=2),
        ]

        params = Params(
            chunksize=2,
            columns_to_unstack=["legs"],
            unstacked_columns_name_format="innermost_hierarchy_level",
            drop_source_index=False,
        )
        task = NestedJsonFileSplitter(name="NestedJsonFileSplitter", params=params)
        result = task.execute(params, extractor_result=TWO_ITEMS_NESTED_FILE_PATH)

        assert len(result) == len(expected)
        assert result == expected

    def test_normalise_columns_and_dataframe_columns(self):
        nested_json_file_splitter.context = addict.Dict(
            {"swarm": {"sources_dir": SCRIPT_PATH}}
        )
        expected = [
            FileSplitterResult(path=SCRIPT_PATH.joinpath("batch_0.csv"), batch_index=0),
            FileSplitterResult(path=SCRIPT_PATH.joinpath("batch_1.csv"), batch_index=1),
        ]

        params = Params(
            chunksize=3,
            columns_to_unstack=["legs"],
            unstacked_columns_name_format="innermost_hierarchy_level",
            drop_source_index=False,
            normalise_columns=True,
            dataframe_columns=["executing_entity"],
        )
        task = NestedJsonFileSplitter(name="NestedJsonFileSplitter", params=params)
        result = task.execute(params, extractor_result=NORMALISE_COLUMNS_FILE_PATH)

        assert len(result) == len(expected)
        assert result == expected

    def test_audit_message(self):
        auditor = Auditor(task_name="Dummy")
        nested_json_file_splitter.context = addict.Dict(
            {"swarm": {"sources_dir": SCRIPT_PATH}}
        )

        params = Params(
            chunksize=2,
            columns_to_unstack=["legs"],
            unstacked_columns_name_format="innermost_hierarchy_level",
            drop_source_index=False,
            audit_input_rows=True,
        )
        task = NestedJsonFileSplitter(name="NestedJsonFileSplitter", params=params)
        task._auditor = auditor
        task.execute(params, extractor_result=TWO_ITEMS_NESTED_FILE_PATH)

        expected_counts = 0
        for index in range(3):
            expected_counts += pd.read_csv(
                SCRIPT_PATH.joinpath(f"batch_{index}.csv")
            ).shape[0]
        assert auditor.to_dataframe().loc[0, "ctx.input_total_count"] == expected_counts

    def test_unstack_when_some_rows_are_null(self):
        df = pd.read_json(SOME_ROWS_NULL_JSON_FILE_PATH.path)
        result = NestedJsonFileSplitter.unstack(
            df=df, columns_to_unstack=["Routes"], source_index_name="__source_index__"
        )
        expected = pd.read_pickle(EXPECTED_SOME_ROWS_NULL_JSON_FILE_PATH.path)
        assert result.shape[1] == 20
        assert result.equals(expected)
