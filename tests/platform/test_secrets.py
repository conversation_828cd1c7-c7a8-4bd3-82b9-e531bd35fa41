from swarm.platform.secrets import _SeLocalSecretsStore
from swarm.platform.secrets import SeSecretsStore


class TestAbsSecretsStore:
    def test_singleton_secret_store_instantiation(self):
        """
        Assert that only a single instance of SeSecretsStore is created
        Assert that as no secret names are passed to the constructor, no secrets will be available
        """
        x = SeSecretsStore()
        y = SeSecretsStore()
        assert id(x) == id(y)
        assert not SeSecretsStore.secrets

    def test_singleton_secret_store(self):
        """
        Assert that only a single instance of _SeLocalSecretsStore is created
        Assert every time the constructor is passed with secret names, those secrets are fetched and added
        to the public `.secrets` property
        Assert that whenever the `get_secret()` method is called, the resulting secret is added to the
        public `.secrets` property
        """
        _SeLocalSecretsStore.data = {"foo": 1, "bar": 2, "abc": 3, "def": 4}
        x = _SeLocalSecretsStore("foo", "bar")
        y = _SeLocalSecretsStore("abc")

        assert id(x) == id(y)
        assert (
            _SeLocalSecretsStore.secrets
            == {"foo": 1, "bar": 2, "abc": 3}
            == x.secrets
            == y.secrets
        )

        def_secret = x.get_secret("def")
        assert def_secret == 4
        assert x.secrets == {"foo": 1, "bar": 2, "abc": 3, "def": 4}
