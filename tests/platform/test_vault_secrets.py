import os
from unittest.mock import MagicMock

from swarm.platform.vault import _SeVaultLocalSecretsStore
from swarm.platform.vault import SeVaultKV2

os.environ["STACK"] = "test_stack"
os.environ["VAULT_URL"] = "http://awesome_url.com"


class TestAbstractVaultSecretsStore:
    def test_singleton_secret_store_instantiation(self, mocker):
        """
        Assert that only a single instance of SeVaultKV2 is created
        """
        mock_resource = mocker.patch("swarm.platform.vault.hvac")
        mock_resource.return_value = MagicMock()

        x = SeVaultKV2()
        y = SeVaultKV2()
        z = SeVaultKV2(reinitialize=True)
        assert id(x) == id(y)
        assert id(x) != id(z)

    def test_singleton_vault_secret_store(self):
        """
        Test _SeVaultLocalSecretsStore instantiation and secret fetch
        """
        x = _SeVaultLocalSecretsStore(data={"foo": 2})

        assert x.get_secrets(path="foo") == 2
