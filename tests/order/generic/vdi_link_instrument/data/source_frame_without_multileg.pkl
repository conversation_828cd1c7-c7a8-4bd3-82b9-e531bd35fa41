���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK7��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo�� executionDetails.tradingCapacity��executionDetails.validityPeriod��	hierarchy��	_order.id��_orderState.id��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers��_order.__meta_model__��_orderState.__meta_model__��
orderClass��"orderIdentifiers.aggregatedOrderId��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��.orderIdentifiers.tradingVenueTransactionIdCode��!orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��"priceFormingData.remainingQuantity��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��	sourceKey��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��timestamps.tradingDateTime��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��!transactionDetails.positionEffect��transactionDetails.quantity��$_order.transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime��
__symbol__��	__price__��__last_px__��__stop_px__��__newo_in_file__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C                      �t�bh]Nu��R�e]�hhK ��h��R�(KK7K��h!�]�(�SELL��pandas._libs.missing��NA���hzhwhzhz�ICE POF Exchange�h{h{�
2023-05-08�hzhzhwhzhz�NEWO�h}h}�PARF�hzhz�Limit�hzhz�0|1749|KBLJUWA��|UDS Download is completed for market type Liffe Equity Derivatives - Non-US Based (3200b537c7c-ed44-11ed-9508-0242c0a80004).��|UDS Download is completed for market type Liffe Equity Derivatives - Non-US Based (320c411a4fe-ed4a-11ed-9508-0242c0a80004).��AOTC�hzhz]��DAVY�aG�      G�      �
Standalone�h�h��183491413559|5792127�hzhzh�hzhz]�(}�(�labelId��id:kyte��path��buyer��type��se_schema.static.market��IdentifierType����ARRAY���R�u}�(h��id:kyte�h��reportDetails.executingEntity�h�h��OBJECT���R�u}�(h��id:ice�h��seller�h�h�u}�(h��id:ice�h��counterparty�h�h�u}�(h��id:1001�h��1tradersAlgosWaiversIndicators.executionWithinFirm�h�h�u}�(h��id:2119�h��clientIdentifiers.client�h�h�u}�(h��id:kbltt-fx1�h��trader�h�h�ue]�(}�(h��id:kyte�h�h�h�h�u}�(h��id:kyte�h�h�h�h�u}�(h��id:ice�h�h�h�h�u}�(h��id:ice�h�h�h�h�ue]�(}�(h��id:kyte�h�h�h�h�u}�(h��id:kyte�h�h�h�h�u}�(h��id:ice�h�h�h�h�u}�(h��id:ice�h�h�h�h�ue�id:kyte��id:kyte��id:kyte��id:kyte��id:kyte��id:kyte��id:ice��id:ice��id:ice��id:ice��id:ice��id:ice�hzhzhzhzhzhzhzhzhz�id:1001�hzhz�id:2119�hzhz�id:kbltt-fx1�hzhz]�(h�h�h�h�h�h�h�e]�(h�h�h�h�e]�(h�h�h�h�e�Order�h�hΌ
OrderState�h�hό
Regular Trade�hzhzhzhzhz�2764138�hzhzh�hzhzh�hzhz�2764139�hzhzG@I      hzhzG@H�     hzhzG?�      hzhzh�hzhz��s3://shrenik.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/kyte_test/0020a396ca42a542f1487d9e9545dc7f3776a2de433e6a0c4ad68d509d09d47b_9406.fix���s3://shrenik.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/kyte_test/00ec882de2ff0fb4b8a2ef4d170f9b68885b29d47b9e7a422d00f9167cb6b9da_892.fix���s3://shrenik.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/kyte_test/017f1b31265488a2b60632237b78c55d523c5456cd602a97f5f106915c45a4cc_892.fix��2023-05-08T06:29:57.254312Z�hzhz�2023-05-08T06:29:57.254312Z�hzhz�2023-05-08T06:29:57.254312Z�hzhzh�hzhzhwhzhzh�hzhz�Open�hzhzG?�      hzhz�Market Side�h�h�h�hzhzh�hzhz�5792127�hzhzG@X;33333hzhzG@X;33333hzhzhzhzhz���et�ba]�h
h}�(hhhK ��h��R�(KK7��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3h4h5h6h7h8h9h:h;h<h=h>h?h@hAhBhChDhEhFhGhHhIhJhKhLhMhNhOhPhQhRhShThUhVhWhXhYhZh[et�bh]Nu��R�a}��0.14.1�}�(�axes�h
�blocks�]�}�(�values�ht�mgr_locs��builtins��slice���K K7K��R�uaust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.