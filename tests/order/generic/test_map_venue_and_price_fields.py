import pandas as pd

from swarm_tasks.order.generic.map_venue_and_price_fields import MapVenueAndPriceFields
from swarm_tasks.order.generic.map_venue_and_price_fields import Params


class TestMapVenueAndPriceFields:
    """Test Suite for MapVenueAndPriceFields"""

    def test_end_to_end_map_venue_and_price_fields(
        self,
        source_frame_map_venue_and_price_fields,
        expected_df_map_venue_and_price_fields,
    ):
        params = Params(
            source_price="__price__",
            source_last_px="__last_px__",
            source_stop_px="__stop_px__",
        )
        task = MapVenueAndPriceFields(name="MapVenueAndPriceFields", params=params)
        result = task.process(
            source_frame=source_frame_map_venue_and_price_fields, params=params
        )
        assert result.fillna(pd.NA).equals(expected_df_map_venue_and_price_fields)

    def test_end_to_end_map_venue_and_price_fields_without_price_display_factor(
        self,
        source_frame_map_venue_and_price_fields,
        expected_df_map_venue_and_price_fields_without_price_display_factor,
    ):
        params = Params(
            source_price="__price__",
            source_last_px="__last_px__",
            source_stop_px="__stop_px__",
            ignore_price_display_factor=True,
        )
        task = MapVenueAndPriceFields(name="MapVenueAndPriceFields", params=params)
        result = task.process(
            source_frame=source_frame_map_venue_and_price_fields, params=params
        )
        assert result.fillna(pd.NA).equals(
            expected_df_map_venue_and_price_fields_without_price_display_factor
        )
