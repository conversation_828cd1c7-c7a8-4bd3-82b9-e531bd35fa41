import pandas as pd
import pytest
from prefect import context
from swarm.task.auditor import Auditor

from swarm_tasks.order.generic.convert_multileg_trades_to_single_leg import (
    ConvertMultilegTradesToSingleLeg,
)
from swarm_tasks.order.generic.convert_multileg_trades_to_single_leg import Params


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


class TestCmeMultilegTradesToSingleLeg:
    """Test suite for CmeMultilegTradesToSingleLeg task"""

    def test_empty_source_frame_multileg_to_single_leg(self, auditor):
        params = Params(security_id_col="__security_id__", trade_date="date")
        task = ConvertMultilegTradesToSingleLeg(
            name="CmeMultilegTradesToSingleLeg", params=params
        )
        result = task.process(
            source_frame=pd.DataFrame(),
            params=params,
            logger=context.get("logger"),
            auditor=auditor,
        )
        assert result.equals(pd.DataFrame())

    def test_end_to_end_test_for_multileg_to_single_leg(
        self,
        source_multileg_data,
        expected_cme_multileg_trades_to_single_leg,
        auditor,
    ):
        params = Params(security_id_col="__security_id__", trade_date="date")
        task = ConvertMultilegTradesToSingleLeg(
            name="CmeMultilegTradesToSingleLeg", params=params
        )
        result = task.process(
            source_frame=source_multileg_data,
            params=params,
            logger=context.get("logger"),
            auditor=auditor,
        )
        assert result.equals(expected_cme_multileg_trades_to_single_leg)
