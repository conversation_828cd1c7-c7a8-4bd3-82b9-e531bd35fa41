import pytest
from pandas.testing import assert_frame_equal

from swarm_tasks.order.generic.utils import replace_parfs_with_flat_average


@pytest.fixture()
def params(source_flat_average_df):
    return {
        "df": source_flat_average_df,
        "execution_price_col": "__last_px__",
        "fill_quantity_col": "_orderState.priceFormingData.tradedQuantity",
        "order_id_col": "_order.id",
        "route_id_col": "_order.id",
        "order_status_temp_col": "_orderState.executionDetails.orderStatus",
        "trading_date_time_temp_col": "_orderState.transactionDetails.tradingDateTime",
        "clean_temp_column": True,
    }


class TestReplaceParfsWithFlatAverage:
    """Test suite for replace_parfs_with_flat_average function"""

    def test_replace_parfs_with_flat_average(self, params, expected_flat_average_df):
        result = replace_parfs_with_flat_average(**params)
        result = result.reset_index(drop=True)
        expected_result = expected_flat_average_df.drop(columns=["__order_id__"])
        assert_frame_equal(result, expected_result)

    def test_replace_parfs_with_flat_average_with_clean_false(
        self,
        params,
        expected_flat_average_df,
    ):
        params["clean_temp_column"] = False
        result = replace_parfs_with_flat_average(**params)
        result = result.reset_index(drop=True)
        assert_frame_equal(result, expected_flat_average_df)
