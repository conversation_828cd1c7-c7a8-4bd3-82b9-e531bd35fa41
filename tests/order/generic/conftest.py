import numpy as np
import pandas as pd
import pytest


@pytest.fixture()
def source_multileg_data():
    multileg_dict = {
        "_order.buySell": {1: "BUYI", 2: "SELL", 4: "SELL"},
        "_orderState.buySell": {1: "BUYI", 2: "SELL", 4: "SELL"},
        "date": {1: "2022-04-06", 2: "2022-04-20", 4: "2022-04-20"},
        "executionDetails.buySellIndicator": {1: "BUYI", 2: "SELL", 4: "SELL"},
        "_order.id": {
            1: "123456:1232445",
            2: "58117532338451|877440",
            4: "58117532338451|877440",
        },
        "_orderState.id": {
            1: "123456:1232445",
            2: "58117532338451|877440",
            4: "58117532338451|877440",
        },
        "orderIdentifiers.orderIdCode": {
            1: "123456:1232445",
            2: "58117532338451|877440",
            4: "58117532338451|877440",
        },
        "priceFormingData.initialQuantity": {1: pd.NA, 2: 649.0, 4: 649.0},
        "priceFormingData.remainingQuantity": {1: pd.NA, 2: 649.0, 4: 649.0},
        "_order.priceFormingData.tradedQuantity": {1: pd.NA, 2: pd.NA, 4: pd.NA},
        "_orderState.priceFormingData.tradedQuantity": {1: pd.NA, 2: pd.NA, 4: pd.NA},
        "transactionDetails.buySellIndicator": {1: pd.NA, 2: "SELL", 4: "SELL"},
        "__multi_leg_reporting_type__": {1: pd.NA, 2: pd.NA, 4: pd.NA},
        "__security_id__": {1: "1232445", 2: "877440", 4: "877440"},
        "__price__": {1: pd.NA, 2: 0.375, 4: 0.3984375},
        "__last_px__": {1: 23, 2: pd.NA, 4: pd.NA},
        "__stop_px__": {1: pd.NA, 2: pd.NA, 4: pd.NA},
        "instrumentDetails.instrument": {
            1: pd.NA,
            2: {
                "ext.exchangeSymbolLocal": "877440",
                "venue.tradingVenue": "XCBT",
                "derivative.priceDisplayFactor": "1.000000000",
                "derivative.legs": [
                    {"legRatioQty": "1", "legSide": "BUYI", "legSecurityId": "342128"},
                    {
                        "legRatioQty": "1",
                        "legSide": "BUYI",
                        "legSecurityId": "60974",
                        "legOptionDelta": 0.5,
                    },
                ],
                "derivative.priceMultiplier": np.nan,
                "derivative.strikePrice": np.nan,
            },
            4: {
                "ext.exchangeSymbolLocal": "444232",
                "venue.tradingVenue": "XCBT",
                "derivative.priceDisplayFactor": "1.000000000",
                "derivative.legs": np.nan,
                "derivative.priceMultiplier": np.nan,
                "derivative.strikePrice": np.nan,
            },
        },
        "__is_multileg_trade__": {1: True, 2: True, 4: True},
    }
    return pd.DataFrame(multileg_dict)


@pytest.fixture()
def expected_cme_multileg_trades_to_single_leg():
    single_leg_trades = {
        "_order.buySell": {0: "SELL", 1: "SELL"},
        "_orderState.buySell": {0: "SELL", 1: "SELL"},
        "date": {0: "2022-04-20", 1: "2022-04-20"},
        "executionDetails.buySellIndicator": {0: "SELL", 1: "SELL"},
        "_order.id": {0: "58117532338451|342128", 1: "58117532338451|60974"},
        "_orderState.id": {0: "58117532338451|342128", 1: "58117532338451|60974"},
        "orderIdentifiers.orderIdCode": {
            0: "58117532338451|342128",
            1: "58117532338451|60974",
        },
        "priceFormingData.initialQuantity": {0: 649, 1: 324},
        "priceFormingData.remainingQuantity": {0: 649, 1: 324},
        "_order.priceFormingData.tradedQuantity": {0: pd.NA, 1: pd.NA},
        "_orderState.priceFormingData.tradedQuantity": {0: pd.NA, 1: pd.NA},
        "transactionDetails.buySellIndicator": {0: "SELL", 1: "SELL"},
        "__multi_leg_reporting_type__": {0: pd.NA, 1: pd.NA},
        "__security_id__": {0: "342128", 1: "60974"},
        "__price__": {0: 0.375, 1: 0.375},
        "__last_px__": {0: pd.NA, 1: pd.NA},
        "__stop_px__": {0: pd.NA, 1: pd.NA},
        "__is_multileg_trade__": {0: True, 1: True},
        "orderIdentifiers.tradingVenueTransactionIdCode": {
            0: "58117532338451|342128",
            1: "58117532338451|60974",
        },
    }
    return pd.DataFrame(single_leg_trades)


@pytest.fixture()
def source_frame_fetch_instrument():
    source_frame = {
        "date": {0: "2022-04-06", 1: "2022-04-06", 2: "2022-04-06"},
        "__security_id__": {0: "309476", 1: "146095", 2: pd.NA},
    }
    return pd.DataFrame(source_frame)


@pytest.fixture()
def expected_fetch_instruments_df():
    instruments = {
        "sourceKey": {
            0: "cmeInstruments.XCBT.OPAFPS.20220406",
            1: "cmeInstruments.XCBT.FFDXSX.20220404",
            2: "cmeInstruments.XCBT.OPAFPS.20220406",
        },
        "&id": {
            0: "XCBT|OZNM2P1225|20220520|USD",
            1: "XCBT|ZNM2|20220621|USD",
            2: "XCBT|OZNM2P1225|20220520|USD",
        },
        "instrumentFullName": {
            0: "XCBT - OZNM2 P1225 - Option - 20-May-22",
            1: "XCBT - ZNM2 - Future - 21-Jun-22",
            2: "XCBT - OZNM2 P1225 - Option - 20-May-22",
        },
        "notionalCurrency1": {0: "USD", 1: "USD", 2: "USD"},
        "ext.exchangeSymbolLocal": {0: "309476", 1: "146095", 2: "309476"},
        "ext.exchangeSymbol": {0: "OZNM2 P1225", 1: "ZNM2", 2: "OZNM2 P1225"},
        "ext.strikePriceType": {0: "MntryVal", 1: pd.NA, 2: "MntryVal"},
        "ext.quantityNotation": {0: "UNIT", 1: "UNIT", 2: "UNIT"},
        "ext.instrumentUniqueIdentifier": {
            0: "XCBT|OZNM2P1225|20220520|USD",
            1: "XCBT|ZNM2|20220621|USD",
            2: "XCBT|OZNM2P1225|20220520|USD",
        },
        "ext.alternativeInstrumentIdentifier": {
            0: "XCBTOZNP2022-05 00:00:00122.50000000",
            1: "XCBTZNFF2022-06 00:00:00",
            2: "XCBTOZNP2022-05 00:00:00122.50000000",
        },
        "venue.tradingVenue": {0: "XCBT", 1: "XCBT", 2: "XCBT"},
        "derivative.optionType": {0: "PUTO", 1: pd.NA, 2: "PUTO"},
        "derivative.legs": {
            0: [
                {"legRatioQty": "1", "legSide": "BUYI", "legSecurityId": "342128"},
                {"legRatioQty": "1", "legSide": "BUYI", "legSecurityId": "60974"},
            ],
            1: pd.NA,
            2: [
                {"legRatioQty": "1", "legSide": "BUYI", "legSecurityId": "342128"},
                {"legRatioQty": "1", "legSide": "BUYI", "legSecurityId": "60974"},
            ],
        },
        "derivative.priceDisplayFactor": {0: 1, 1: 1, 2: 1},
        "derivative.strikePriceCurrency": {0: "USD", 1: pd.NA, 2: "USD"},
        "derivative.deliveryType": {0: "PHYS", 1: "OPTL", 2: "PHYS"},
        "derivative.isUserDefinedSpread": {0: False, 1: False, 2: False},
        "derivative.priceMultiplier": {
            0: "100000.000000000",
            1: "100000.000000000",
            2: "100000.000000000",
        },
        "derivative.strikePrice": {0: "122.500000000", 1: pd.NA, 2: "122.500000000"},
        "&timestamp": {0: 1650175291, 1: 1650275291, 2: 1645105291},
    }
    return pd.DataFrame(instruments)


@pytest.fixture()
def expected_target_df_fetch_instruments():
    fetched_instruments = {
        "instrumentDetails.instrument": {
            0: {
                "sourceKey": "cmeInstruments.XCBT.OPAFPS.20220406",
                "&id": "XCBT|OZNM2P1225|20220520|USD",
                "instrumentFullName": "XCBT - OZNM2 P1225 - Option - 20-May-22",
                "notionalCurrency1": "USD",
                "ext.exchangeSymbolLocal": "309476",
                "ext.exchangeSymbol": "OZNM2 P1225",
                "ext.strikePriceType": "MntryVal",
                "ext.quantityNotation": "UNIT",
                "ext.instrumentUniqueIdentifier": "XCBT|OZNM2P1225|20220520|USD",
                "ext.alternativeInstrumentIdentifier": "XCBTOZNP2022-05 00:00:00122.50000000",
                "venue.tradingVenue": "XCBT",
                "derivative.optionType": "PUTO",
                "derivative.legs": [
                    {"legRatioQty": "1", "legSide": "BUYI", "legSecurityId": "342128"},
                    {"legRatioQty": "1", "legSide": "BUYI", "legSecurityId": "60974"},
                ],
                "derivative.priceDisplayFactor": 1,
                "derivative.strikePriceCurrency": "USD",
                "derivative.deliveryType": "PHYS",
                "derivative.isUserDefinedSpread": False,
                "derivative.priceMultiplier": "100000.000000000",
                "derivative.strikePrice": "122.500000000",
            },
            1: {
                "sourceKey": "cmeInstruments.XCBT.FFDXSX.20220404",
                "&id": "XCBT|ZNM2|20220621|USD",
                "instrumentFullName": "XCBT - ZNM2 - Future - 21-Jun-22",
                "notionalCurrency1": "USD",
                "ext.exchangeSymbolLocal": "146095",
                "ext.exchangeSymbol": "ZNM2",
                "ext.strikePriceType": pd.NA,
                "ext.quantityNotation": "UNIT",
                "ext.instrumentUniqueIdentifier": "XCBT|ZNM2|20220621|USD",
                "ext.alternativeInstrumentIdentifier": "XCBTZNFF2022-06 00:00:00",
                "venue.tradingVenue": "XCBT",
                "derivative.optionType": pd.NA,
                "derivative.legs": pd.NA,
                "derivative.priceDisplayFactor": 1,
                "derivative.strikePriceCurrency": pd.NA,
                "derivative.deliveryType": "OPTL",
                "derivative.isUserDefinedSpread": False,
                "derivative.priceMultiplier": "100000.000000000",
                "derivative.strikePrice": pd.NA,
            },
            2: pd.NA,
        },
        "__is_multileg_trade__": {0: True, 1: False, 2: True},
    }
    return pd.DataFrame(fetched_instruments)


@pytest.fixture()
def expected_fetch_instruments_df_only_cme():
    instruments = {
        "sourceKey": {
            0: "cmeInstruments.XCBT.OPAFPS.20220406",
        },
        "&id": {
            0: "XCBT|OZNM2P1225|20220520|USD",
        },
        "instrumentFullName": {
            0: "XCBT - OZNM2 P1225 - Option - 20-May-22",
        },
        "notionalCurrency1": {0: "USD"},
        "ext.exchangeSymbolLocal": {0: "309476"},
        "ext.exchangeSymbol": {0: "OZNM2 P1225"},
        "ext.strikePriceType": {0: "MntryVal"},
        "ext.quantityNotation": {0: "UNIT"},
        "ext.instrumentUniqueIdentifier": {
            0: "XCBT|OZNM2P1225|20220520|USD",
        },
        "ext.alternativeInstrumentIdentifier": {
            0: "XCBTOZNP2022-05 00:00:00122.50000000",
        },
        "venue.tradingVenue": {0: "XCBT"},
        "derivative.optionType": {0: "PUTO"},
        "derivative.legs": {
            0: [
                {"legRatioQty": "1", "legSide": "BUYI", "legSecurityId": "342128"},
                {"legRatioQty": "1", "legSide": "BUYI", "legSecurityId": "60974"},
            ],
        },
        "derivative.priceDisplayFactor": {0: 1},
        "derivative.strikePriceCurrency": {0: "USD"},
        "derivative.deliveryType": {0: "PHYS"},
        "derivative.isUserDefinedSpread": {0: False},
        "derivative.priceMultiplier": {0: "100000.000000000"},
        "derivative.strikePrice": {0: "122.500000000"},
        "&timestamp": {0: 1650175291},
    }
    return pd.DataFrame(instruments)


@pytest.fixture()
def expected_target_df_fetch_instruments_only_cme():
    fetched_instruments = {
        "instrumentDetails.instrument": {
            0: {
                "sourceKey": "cmeInstruments.XCBT.OPAFPS.20220406",
                "&id": "XCBT|OZNM2P1225|20220520|USD",
                "instrumentFullName": "XCBT - OZNM2 P1225 - Option - 20-May-22",
                "notionalCurrency1": "USD",
                "ext.exchangeSymbolLocal": "309476",
                "ext.exchangeSymbol": "OZNM2 P1225",
                "ext.strikePriceType": "MntryVal",
                "ext.quantityNotation": "UNIT",
                "ext.instrumentUniqueIdentifier": "XCBT|OZNM2P1225|20220520|USD",
                "ext.alternativeInstrumentIdentifier": "XCBTOZNP2022-05 00:00:00122.50000000",
                "venue.tradingVenue": "XCBT",
                "derivative.optionType": "PUTO",
                "derivative.legs": [
                    {"legRatioQty": "1", "legSide": "BUYI", "legSecurityId": "342128"},
                    {"legRatioQty": "1", "legSide": "BUYI", "legSecurityId": "60974"},
                ],
                "derivative.priceDisplayFactor": 1,
                "derivative.strikePriceCurrency": "USD",
                "derivative.deliveryType": "PHYS",
                "derivative.isUserDefinedSpread": False,
                "derivative.priceMultiplier": "100000.000000000",
                "derivative.strikePrice": "122.500000000",
            },
            1: pd.NA,
            2: pd.NA,
        },
        "__is_multileg_trade__": {0: True, 1: True, 2: True},
    }
    return pd.DataFrame(fetched_instruments)


@pytest.fixture()
def source_frame_map_venue_and_price_fields():
    source_frame = {
        "instrumentDetails.instrument": {
            0: {
                "notionalCurrency1": "USD",
                "ext.exchangeSymbolLocal": "309476",
                "venue.tradingVenue": "XCBT",
                "derivative.priceDisplayFactor": 5,
                "ext.priceNotation": "TEST",
                "derivative.strikePriceCurrency": "USD",
                "derivative.priceMultiplier": "100000.000000000",
            },
            1: {
                "notionalCurrency1": "USD",
                "ext.exchangeSymbolLocal": "146095",
                "ext.quantityNotation": "UNIT",
                "ext.priceNotation": "MONE",
                "venue.tradingVenue": "XCBT",
                "derivative.priceDisplayFactor": 1,
            },
        },
        "_order.buySell": {0: "BUYI", 1: "BUYI"},
        "_orderState.buySell": {0: "BUYI", 1: "BUYI"},
        "date": {0: "2022-04-06", 1: "2022-04-06"},
        "executionDetails.buySellIndicator": {0: "BUYI", 1: "BUYI"},
        "_order.executionDetails.orderStatus": {0: "NEWO", 1: "NEWO"},
        "executionDetails.orderType": {0: "Limit", 1: "Limit"},
        "_order.id": {0: "58116018859828|309476", 1: "58116018859828|146095"},
        "_orderState.id": {0: "58116018859828|309476", 1: "58116018859828|146095"},
        "orderIdentifiers.orderIdCode": {
            0: "58116018859828|309476",
            1: "58116018859828|146095",
        },
        "priceFormingData.initialQuantity": {0: 75, 1: 75},
        "priceFormingData.remainingQuantity": {0: 75, 1: 75},
        "transactionDetails.buySellIndicator": {0: "BUYI", 1: "BUYI"},
        "__security_id__": {0: "309476", 1: "146095"},
        "__price__": {0: 0.203125, 1: 0.203125},
        "__last_px__": {0: pd.NA, 1: pd.NA},
        "__stop_px__": {0: pd.NA, 1: pd.NA},
    }
    return pd.DataFrame(source_frame)


@pytest.fixture()
def expected_df_map_venue_and_price_fields():
    expected_value = {
        "transactionDetails.ultimateVenue": {0: "XCBT", 1: "XCBT"},
        "transactionDetails.venue": {0: "XCBT", 1: "XCBT"},
        "transactionDetails.priceCurrency": {0: "USD", 1: "USD"},
        "_orderState.priceFormingData.price": {0: pd.NA, 1: pd.NA},
        "_orderState.transactionDetails.price": {0: pd.NA, 1: pd.NA},
        "executionDetails.stopPrice": {0: pd.NA, 1: pd.NA},
        "executionDetails.limitPrice": {0: 1.015625, 1: 0.203125},
        "transactionDetails.priceNotation": {0: "TEST", 1: "MONE"},
        "transactionDetails.quantityNotation": {0: pd.NA, 1: "UNIT"},
    }
    return pd.DataFrame(expected_value)


@pytest.fixture()
def expected_df_map_venue_and_price_fields_without_price_display_factor():
    expected_value = {
        "transactionDetails.ultimateVenue": {0: "XCBT", 1: "XCBT"},
        "transactionDetails.venue": {0: "XCBT", 1: "XCBT"},
        "transactionDetails.priceCurrency": {0: "USD", 1: "USD"},
        "_orderState.priceFormingData.price": {0: pd.NA, 1: pd.NA},
        "_orderState.transactionDetails.price": {0: pd.NA, 1: pd.NA},
        "executionDetails.stopPrice": {0: pd.NA, 1: pd.NA},
        "executionDetails.limitPrice": {0: 0.203125, 1: 0.203125},
        "transactionDetails.priceNotation": {0: "TEST", 1: "MONE"},
        "transactionDetails.quantityNotation": {0: pd.NA, 1: "UNIT"},
    }
    return pd.DataFrame(expected_value)


@pytest.fixture()
def instruments_with_same_exchange_symbol_local():
    instruments = {
        "notionalCurrency1": {0: "USD", 1: "EUR"},
        "ext.exchangeSymbolLocal": {0: "876241", 1: "876241"},
        "venue.tradingVenue": {0: "XCBT", 1: "XCME"},
        "derivative.expiryDate": {0: "2022-03-15"},
        "derivative.priceDisplayFactor": {0: 1},
        "&timestamp": {0: 1650175291, 1: 1650275291321},
        "instrumentFullName": {0: "test", 1: "different_than_test"},
        "derivative.isUserDefinedSpread": {0: False, 1: True},
        "derivative.legs": {
            0: [
                {
                    "legOptionDelta": "0.2500",
                    "legRatioQty": "1",
                    "legSide": "BUYI",
                    "legSecurityId": "309476",
                },
                {"legRatioQty": "1", "legSide": "BUYI", "legSecurityId": "146095"},
            ],
            1: [
                {
                    "legOptionDelta": "0.3000",
                    "legRatioQty": "2",
                    "legSide": "BUYI",
                    "legSecurityId": "323223",
                },
                {"legRatioQty": "1", "legSide": "SELL", "legSecurityId": "232232"},
            ],
        },
    }
    return pd.DataFrame(instruments)


@pytest.fixture()
def source_frame_with_exchange_venue_col():
    data = [
        {
            "_order.buySell": "SELL",
            "__exchange_venue__": "Eurex",
            "__exchange_symbol_local__": "8347915",
            "_order.id": "008ba10e-ebbb-495d-a01b-3f7bff1c09ed|8347915",
            "__isin__": "DE000C671E22",
            "_orderState.buySell": "SELL",
            "orderIdentifiers.orderIdCode": "008ba10e-ebbb-495d-a01b-3f7bff1c09ed|8347915",
            "date": "2023-05-17",
            "__orig_security_id__": "8347915",
        },
        {
            "_order.buySell": "BUYI",
            "__exchange_venue__": "CME",
            "__exchange_symbol_local__": "254052",
            "_order.id": "7223e1b0-23a9-4a87-a13f-ae6332e2ac8a|254052",
            "__isin__": pd.NA,
            "_orderState.buySell": "BUYI",
            "orderIdentifiers.orderIdCode": "7223e1b0-23a9-4a87-a13f-ae6332e2ac8a|254052",
            "date": "2023-05-17",
        },
        {
            "_order.buySell": "SELL",
            "__exchange_venue__": "CME",
            "__exchange_symbol_local__": "254254",
            "_order.id": "7223e1b0-23a9-4a87-a13f-ae6332e2ac8a|254254",
            "__isin__": pd.NA,
            "_orderState.buySell": "SELL",
            "orderIdentifiers.orderIdCode": "7223e1b0-23a9-4a87-a13f-ae6332e2ac8a|254254",
            "date": "2023-05-17",
        },
        {
            "_order.buySell": "SELL",
            "__exchange_venue__": "Eurex",
            "__exchange_symbol_local__": "8347915",
            "_order.id": "008ba10e-ebbb-495d-a01b-3f7bff1c09ed|8347915",
            "__isin__": "DE000C671E22",
            "_orderState.buySell": "SELL",
            "orderIdentifiers.orderIdCode": "008ba10e-ebbb-495d-a01b-3f7bff1c09ed|8347915",
            "date": "2023-05-17",
        },
        {
            "_order.buySell": "SELL",
            "__exchange_venue__": "CME",
            "__exchange_symbol_local__": "46020",
            "_order.id": "7223e1b0-23a9-4a87-a13f-ae6332e2ac8a|46020",
            "__isin__": pd.NA,
            "_orderState.buySell": "SELL",
            "orderIdentifiers.orderIdCode": "7223e1b0-23a9-4a87-a13f-ae6332e2ac8a|46020",
            "date": "2023-05-17",
        },
    ]
    return pd.DataFrame(data)


@pytest.fixture()
def expected_fetch_instruments_cme_exchange_venue_col():
    data = {
        "sourceKey": {
            0: "cmeInstruments.XCME.FFDXSX.20230524.020040",
            1: "cmeInstruments.XCME.FFDXSX.20230524.020040",
            2: "cmeInstruments.XCME.FMDXSX.multileg.20230524.020040",
        },
        "&id": {
            0: "XCME|SR3Z4|20250318|USD",
            1: "XCME|SR3Z6|20270316|USD",
            2: "XCME|SR3Z4-SR3Z6|20250318|USD",
        },
        "instrumentFullName": {
            0: "XCME - SR3 - Dec-24 - Future",
            1: "XCME - SR3 - Dec-26 - Future",
            2: "XCME - SR3 - Dec-24 - Future Spread",
        },
        "&key": {
            0: "VenueDirectInstrument:XCME|SR3Z4|20250318|USD:1684895244",
            1: "VenueDirectInstrument:XCME|SR3Z6|20270316|USD:1684895244",
            2: "VenueDirectInstrument:XCME|SR3Z4-SR3Z6|20250318|USD:1684895561",
        },
        "&timestamp": {0: "1684895244", 1: "1684895244", 2: "1684895561"},
        "ext.exchangeSymbolLocal": {0: "254254", 1: "254052", 2: "46020"},
        "ext.instrumentUniqueIdentifier": {
            0: "XCME|SR3Z4|20250318|USD",
            1: "XCME|SR3Z6|20270316|USD",
            2: "XCME|SR3Z4-SR3Z6|20250318|USD",
        },
        "venue.tradingVenue": {0: "XCME", 1: "XCME", 2: "XCME"},
        "derivative.expiryDate": {0: "2025-03-18", 1: "2027-03-16", 2: "2025-03-18"},
        "derivative.priceDisplayFactor": {
            0: "0.010000000",
            1: "0.010000000",
            2: "1.000000000",
        },
        "derivative.isUserDefinedSpread": {0: False, 1: False, 2: False},
        "derivative.legs": {
            0: pd.NA,
            1: pd.NA,
            2: [
                {"legRatioQty": "1", "legSide": "BUYI", "legSecurityId": "254254"},
                {"legRatioQty": "1", "legSide": "SELL", "legSecurityId": "254052"},
            ],
        },
    }
    return pd.DataFrame(data)


@pytest.fixture()
def expected_fetch_instruments_eurex_exchange_venue_col():
    data = {
        "sourceKey": {0: "eurexInstruments.XEUR.FFDPSX.20230524.024508"},
        "&id": {0: "DE000C671E22EURXEUR"},
        "instrumentIdCode": {0: "DE000C671E22"},
        "instrumentFullName": {0: "XEUR - FGBL - Jun-23 - Future"},
        "&key": {0: "VenueDirectInstrument:DE000C671E22EURXEUR:1684896688"},
        "&timestamp": {0: "1684896688"},
        "ext.exchangeSymbolLocal": {0: "8347915"},
        "ext.instrumentUniqueIdentifier": {0: "DE000C671E22EURXEUR"},
        "venue.tradingVenue": {0: "XEUR"},
        "derivative.priceDisplayFactor": {0: 1},
        "derivative.isUserDefinedSpread": {0: False},
    }
    return pd.DataFrame(data)


@pytest.fixture()
def expected_output_with_exchange_venue_col():
    data = [
        {
            "instrumentDetails.instrument": {
                "sourceKey": "eurexInstruments.XEUR.FFDPSX.20230524.024508",
                "&id": "DE000C671E22EURXEUR",
                "instrumentFullName": "XEUR - FGBL - Jun-23 - Future",
                "&key": "VenueDirectInstrument:DE000C671E22EURXEUR:1684896688",
                "ext.exchangeSymbolLocal": "8347915",
                "ext.instrumentUniqueIdentifier": "DE000C671E22EURXEUR",
                "venue.tradingVenue": "XEUR",
                "derivative.expiryDate": pd.NA,
                "derivative.priceDisplayFactor": 1,
                "derivative.isUserDefinedSpread": False,
                "derivative.legs": pd.NA,
                "instrumentIdCode": "DE000C671E22",
            },
            "__is_multileg_trade__": False,
        },
        {
            "instrumentDetails.instrument": {
                "sourceKey": "cmeInstruments.XCME.FFDXSX.20230524.020040",
                "&id": "XCME|SR3Z6|20270316|USD",
                "instrumentFullName": "XCME - SR3 - Dec-26 - Future",
                "&key": "VenueDirectInstrument:XCME|SR3Z6|20270316|USD:1684895244",
                "ext.exchangeSymbolLocal": "254052",
                "ext.instrumentUniqueIdentifier": "XCME|SR3Z6|20270316|USD",
                "venue.tradingVenue": "XCME",
                "derivative.expiryDate": "2027-03-16",
                "derivative.priceDisplayFactor": "0.010000000",
                "derivative.isUserDefinedSpread": False,
                "derivative.legs": pd.NA,
                "instrumentIdCode": pd.NA,
            },
            "__is_multileg_trade__": False,
        },
        {
            "instrumentDetails.instrument": {
                "sourceKey": "cmeInstruments.XCME.FFDXSX.20230524.020040",
                "&id": "XCME|SR3Z4|20250318|USD",
                "instrumentFullName": "XCME - SR3 - Dec-24 - Future",
                "&key": "VenueDirectInstrument:XCME|SR3Z4|20250318|USD:1684895244",
                "ext.exchangeSymbolLocal": "254254",
                "ext.instrumentUniqueIdentifier": "XCME|SR3Z4|20250318|USD",
                "venue.tradingVenue": "XCME",
                "derivative.expiryDate": "2025-03-18",
                "derivative.priceDisplayFactor": "0.010000000",
                "derivative.isUserDefinedSpread": False,
                "derivative.legs": pd.NA,
                "instrumentIdCode": pd.NA,
            },
            "__is_multileg_trade__": False,
        },
        {
            "instrumentDetails.instrument": {
                "sourceKey": "eurexInstruments.XEUR.FFDPSX.20230524.024508",
                "&id": "DE000C671E22EURXEUR",
                "instrumentFullName": "XEUR - FGBL - Jun-23 - Future",
                "&key": "VenueDirectInstrument:DE000C671E22EURXEUR:1684896688",
                "ext.exchangeSymbolLocal": "8347915",
                "ext.instrumentUniqueIdentifier": "DE000C671E22EURXEUR",
                "venue.tradingVenue": "XEUR",
                "derivative.expiryDate": pd.NA,
                "derivative.priceDisplayFactor": 1,
                "derivative.isUserDefinedSpread": False,
                "derivative.legs": pd.NA,
                "instrumentIdCode": "DE000C671E22",
            },
            "__is_multileg_trade__": False,
        },
        {
            "instrumentDetails.instrument": {
                "sourceKey": "cmeInstruments.XCME.FMDXSX.multileg.20230524.020040",
                "&id": "XCME|SR3Z4-SR3Z6|20250318|USD",
                "instrumentFullName": "XCME - SR3 - Dec-24 - Future Spread",
                "&key": "VenueDirectInstrument:XCME|SR3Z4-SR3Z6|20250318|USD:1684895561",
                "ext.exchangeSymbolLocal": "46020",
                "ext.instrumentUniqueIdentifier": "XCME|SR3Z4-SR3Z6|20250318|USD",
                "venue.tradingVenue": "XCME",
                "derivative.expiryDate": "2025-03-18",
                "derivative.priceDisplayFactor": "1.000000000",
                "derivative.isUserDefinedSpread": False,
                "derivative.legs": [
                    {"legRatioQty": "1", "legSide": "BUYI", "legSecurityId": "254254"},
                    {"legRatioQty": "1", "legSide": "SELL", "legSecurityId": "254052"},
                ],
                "instrumentIdCode": pd.NA,
            },
            "__is_multileg_trade__": True,
        },
    ]
    return pd.DataFrame(data)


@pytest.fixture()
def source_flat_average_df():
    return pd.DataFrame(
        [
            {
                "transactionDetails.ultimateVenue": pd.NA,
                "executionDetails.tradingCapacity": "AOTC",
                "executionDetails.buySellIndicator": "SELL",
                "_orderState.buySell": "SELL",
                "_orderState.timestamps.tradingDateTime": "2024-03-07T17:29:46.000000Z",
                "orderIdentifiers.orderIdCode": "a5b03ff3-bb69-4d72-8959-0f6e2cff50ef|487769",
                "_orderState.id": "a5b03ff3-bb69-4d72-8959-0f6e2cff50ef|487769",
                "_order.transactionDetails.quantity": 0,
                "_orderState.transactionDetails.quantity": 16.0,
                "_orderState.orderIdentifiers.transactionRefNo": "jMdOcTDfA40cwoLT-XCME|8628|a5b03ff3-bb69-4d72-8959-0",
                "_orderState.transactionDetails.tradingDateTime": "2024-03-07T17:29:46.000000Z",
                "timestamps.orderSubmitted": "2024-03-07T17:29:46.000000Z",
                "date": "2024-03-07",
                "transactionDetails.settlementDate": pd.NA,
                "__expiry_date__": "2024-12-13",
                "transactionDetails.positionEffect": pd.NA,
                "executionDetails.orderType": "Limit",
                "__asset_class__": "future",
                "orderClass": pd.NA,
                "EXECUTION_WITHIN_FIRM": "NORE",
                "executionDetails.passiveOnlyIndicator": pd.NA,
                "transactionDetails.tradingCapacity": "AOTC",
                "EXECUTINGENTITYID": "lei:549300YCOYUK6X2LTB82",
                "transactionDetails.priceCurrency": "USD",
                "__last_px__": 466.25,
                "__price__": 465.0,
                "__option_strike_price__": pd.NA,
                "_orderState.transactionDetails.priceAverage": 466.25,
                "_orderState.__meta_parent__": "a5b03ff3-bb69-4d72-8959-0f6e2cff50ef|487769:SELL:NEWO",
                "executionDetails.outgoingOrderAddlInfo": "2502111205808332076|ZC|<EMAIL>",
                "__id_single_stock__": "2|a5b03ff3-bb69-4d72-8959-0f6e2cff50ef",
                "__side_trade_report_security_id__": "2|2502111205808332076",
                "__id_individual_leg_of_mleg__": "2|a5b03ff3-bb69-4d72-8959-0f6e2cff50ef|M|487769",
                "__id_mleg__": "2|a5b03ff3-bb69-4d72-8959-0f6e2cff50ef|M",
                "_order.priceFormingData.tradedQuantity": 0,
                "_order.transactionDetails.cumulativeQuantity": 0,
                "_order.transactionDetails.priceAverage": 0,
                "_order.__meta_model__": "Order",
                "_orderState.__meta_model__": "OrderState",
                "_orderState.sourceIndex": "0",
                "_order.sourceIndex": "0",
                "__cash__": "CASH",
                "dataSourceName": "TT",
                "__newo_in_file_col__": False,
                "_order.executionDetails.orderStatus": "NEWO",
                "__static_text_m__": "M",
                "transactionDetails.recordType": "Market Side",
                "transactionDetails.settlementAmountCurrency": "USD",
                "transactionDetails.quantityNotation": "UNIT",
                "_orderState.priceFormingData.remainingQuantity": 34.0,
                "__limit_price__": 465.0,
                "executionDetails.passiveAggressiveIndicator": pd.NA,
                "__option_type__": pd.NA,
                "_order.id": "a5b03ff3-bb69-4d72-8959-0f6e2cff50ef|487769",
                "__isin__": pd.NA,
                "orderIdentifiers.tradingVenueTransactionIdCode": "OSNALTNA017sTMwqI-00",
                "priceFormingData.initialQuantity": 50.0,
                "orderIdentifiers.aggregatedOrderId": "a5b03ff3-bb69-4d72-8959-0f6e2cff50ef:6",
                "_orderState.reportDetails.transactionRefNo": "jMdOcTDfA40cwoLT-XCME|8628|a5b03ff3-bb69-4d72-8959-0",
                "TRADERID": "JBrosterman",
                "CLIENTID": "CAX",
                "__underlying_symbol__": "ZC",
                "transactionDetails.settlementAmount": "0",
                "transactionDetails.priceNotation": "MONE",
                "_orderState.transactionDetails.cumulativeQuantity": 16.0,
                "_orderState.priceFormingData.tradedQuantity": 16.0,
                "_order.priceFormingData.remainingQuantity": 34.0,
                "__derivative_price_multiplier__": pd.NA,
                "transactionDetails.basketId": pd.NA,
                "transactionDetails.netAmount": "0",
                "transactionDetails.commissionAmount": "0",
                "transactionDetails.commissionAmountCurrency": "USD",
                "transactionDetails.commissionAmountType": pd.NA,
                "__raw_order_status__": "1",
                "__symbol__": "ZC",
                "__security_id__": "2502111205808332076",
                "INVESTMENTDECISIONMAKER": pd.NA,
                "COUNTERPARTYID": "JPM",
                "__option_type_mleg__": "",
                "_order.buySell": "SELL",
                "sourceKey": "s3://test.steeleye.co/ingress/raw/order-feed-tt-fix-controller/20240307/00053a9d3bfbb90a081075c1a2abf7e4065e0c8bb63bf8ca7957ad0ec33849e6_8628.fix",
                "orderIdentifiers.internalOrderIdCode": "1709764327340",
                "__market_segment_id__": pd.NA,
                "transactionDetails.buySellIndicator": "SELL",
                "__final_derived_side__": "2",
                "_orderState.timestamps.orderStatusUpdated": "2024-03-07T17:29:46.000000Z",
                "_order.timestamps.orderStatusUpdated": "2024-03-07T17:29:46.000000Z",
                "timestamps.orderReceived": "2024-03-07T17:29:46.000000Z",
                "_orderState.executionDetails.orderStatus": "PARF",
                "instrumentDetails.instrument": {
                    "sourceKey": "cmeInstruments.XCBT.FCAXSX.20240405.020040",
                    "instrumentClassificationEMIRAssetClass": "CO",
                    "&id": "XCBT|ZCZ4|20241213|USD",
                    "cfiGroup": "Commodities futures",
                    "cfiCategory": "Futures",
                    "commoditiesOrEmissionAllowanceDerivativeInd": True,
                    "instrumentFullName": "XCBT - ZC - Dec-24 - Future",
                    "&key": "VenueDirectInstrument:XCBT|ZCZ4|20241213|USD:1712336495",
                    "notionalCurrency1": "USD",
                    "instrumentClassificationEMIRProductType": "FUT",
                    "instrumentClassificationEMIRContractType": "FU",
                    "cfiAttribute1": "Agriculture",
                    "cfiAttribute2": "Not Applicable/Undefined",
                    "cfiAttribute3": "Standardized",
                    "cfiAttribute4": "Not Applicable/Undefined",
                    "instrumentClassification": "FCAXSX",
                    "ext.venueName": "Chicago Board Of Trade",
                    "ext.mifirEligible": False,
                    "ext.exchangeSymbolLocal": "487769",
                    "ext.emirEligible": True,
                    "ext.bestExAssetClassSub": "Futures and options admitted to trading on a trading venue",
                    "ext.exchangeSymbol": "ZCZ4",
                    "ext.quantityNotation": "UNIT",
                    "ext.bestExAssetClassMain": "Commodities derivatives and emission allowances Derivatives",
                    "ext.alternativeInstrumentIdentifier": "XCBTZCFF2024-12-13 00:00:00",
                    "ext.instrumentUniqueIdentifier": "XCBT|ZCZ4|20241213|USD",
                    "ext.priceNotation": "MONE",
                    "ext.exchangeSymbolRoot": "ZC",
                    "venue.tradingVenue": "XCBT",
                    "venue.financialInstrumentShortName": "ZCZ4",
                    "commodityAndEmissionAllowances.quantityUnitOfMeasure": "BUSHELS",
                    "derivative.expiryDate": "2024-12-13",
                    "derivative.priceDisplayFactor": "1",
                    "derivative.deliveryType": "OPTL",
                    "derivative.isUserDefinedSpread": False,
                    "derivative.priceMultiplier": "5000.000000000",
                },
                "marketIdentifiers.instrument": pd.NA,
            },
            {
                "transactionDetails.ultimateVenue": pd.NA,
                "executionDetails.tradingCapacity": "AOTC",
                "executionDetails.buySellIndicator": "BUYI",
                "_orderState.buySell": "BUYI",
                "_orderState.timestamps.tradingDateTime": "2024-03-07T18:21:54.000000Z",
                "orderIdentifiers.orderIdCode": "d8c27379-dd4f-4f58-a92f-687628f5b241|347006",
                "_orderState.id": "d8c27379-dd4f-4f58-a92f-687628f5b241|347006",
                "_order.transactionDetails.quantity": 0,
                "_orderState.transactionDetails.quantity": 100.0,
                "_orderState.orderIdentifiers.transactionRefNo": "jM2wcTDfA70cxEwj-XCME-0|9096|d8c27379-dd4f-4f58-a92f",
                "_orderState.transactionDetails.tradingDateTime": "2024-03-07T18:21:54.000000Z",
                "timestamps.orderSubmitted": "2024-03-07T18:21:54.000000Z",
                "date": "2024-03-07",
                "transactionDetails.settlementDate": pd.NA,
                "__expiry_date__": "2024-07-12",
                "transactionDetails.positionEffect": pd.NA,
                "executionDetails.orderType": "Limit",
                "__asset_class__": "future",
                "orderClass": pd.NA,
                "EXECUTION_WITHIN_FIRM": "NORE",
                "executionDetails.passiveOnlyIndicator": pd.NA,
                "transactionDetails.tradingCapacity": "AOTC",
                "EXECUTINGENTITYID": "lei:549300YCOYUK6X2LTB82",
                "transactionDetails.priceCurrency": "USD",
                "__last_px__": 448.5,
                "__price__": -18.5,
                "__option_strike_price__": pd.NA,
                "_orderState.transactionDetails.priceAverage": 448.2890625,
                "_orderState.__meta_parent__": "d8c27379-dd4f-4f58-a92f-687628f5b241|347006:BUYI:NEWO",
                "executionDetails.outgoingOrderAddlInfo": "16169186788407864635|ZC|<EMAIL>",
                "__id_single_stock__": "1|d8c27379-dd4f-4f58-a92f-687628f5b241",
                "__side_trade_report_security_id__": "1|16169186788407864635",
                "__id_individual_leg_of_mleg__": "1|d8c27379-dd4f-4f58-a92f-687628f5b241|M|347006",
                "__id_mleg__": "1|d8c27379-dd4f-4f58-a92f-687628f5b241|M",
                "_order.priceFormingData.tradedQuantity": 0,
                "_order.transactionDetails.cumulativeQuantity": 0,
                "_order.transactionDetails.priceAverage": 0,
                "_order.__meta_model__": "Order",
                "_orderState.__meta_model__": "OrderState",
                "_orderState.sourceIndex": "1",
                "_order.sourceIndex": "1",
                "__cash__": "CASH",
                "dataSourceName": "TT",
                "__newo_in_file_col__": False,
                "_order.executionDetails.orderStatus": "NEWO",
                "__static_text_m__": "M",
                "transactionDetails.recordType": "Market Side",
                "transactionDetails.settlementAmountCurrency": "USD",
                "transactionDetails.quantityNotation": "UNIT",
                "_orderState.priceFormingData.remainingQuantity": 402.0,
                "__limit_price__": pd.NA,
                "executionDetails.passiveAggressiveIndicator": pd.NA,
                "__option_type__": pd.NA,
                "_order.id": "d8c27379-dd4f-4f58-a92f-687628f5b241|347006",
                "__isin__": pd.NA,
                "orderIdentifiers.tradingVenueTransactionIdCode": "OSNALTNA017sTaI1f-00",
                "priceFormingData.initialQuantity": 502.0,
                "orderIdentifiers.aggregatedOrderId": "d8c27379-dd4f-4f58-a92f-687628f5b241:21:1",
                "_orderState.reportDetails.transactionRefNo": "jM2wcTDfA70cxEwj-XCME-0|9096|d8c27379-dd4f-4f58-a92f",
                "TRADERID": "JBrosterman",
                "CLIENTID": "CAX",
                "__underlying_symbol__": "ZC",
                "transactionDetails.settlementAmount": "0",
                "transactionDetails.priceNotation": "MONE",
                "_orderState.transactionDetails.cumulativeQuantity": 100.0,
                "_orderState.priceFormingData.tradedQuantity": 100.0,
                "_order.priceFormingData.remainingQuantity": 402.0,
                "__derivative_price_multiplier__": pd.NA,
                "transactionDetails.basketId": pd.NA,
                "transactionDetails.netAmount": "0",
                "transactionDetails.commissionAmount": "0",
                "transactionDetails.commissionAmountCurrency": "USD",
                "transactionDetails.commissionAmountType": pd.NA,
                "__raw_order_status__": "1",
                "__symbol__": "ZC",
                "__security_id__": "16169186788407864635",
                "INVESTMENTDECISIONMAKER": pd.NA,
                "COUNTERPARTYID": "JPM",
                "__option_type_mleg__": "",
                "_order.buySell": "BUYI",
                "sourceKey": "s3://test.steeleye.co/ingress/raw/order-feed-tt-fix-controller/20240307/00681c47836b651f4b0bcfc782594364d40ec1a4ef84b95db7a9b14895606ffc_9096.fix",
                "orderIdentifiers.internalOrderIdCode": "1709764327342",
                "__market_segment_id__": pd.NA,
                "transactionDetails.buySellIndicator": "BUYI",
                "__final_derived_side__": "1",
                "_orderState.timestamps.orderStatusUpdated": "2024-03-07T18:21:54.000000Z",
                "_order.timestamps.orderStatusUpdated": "2024-03-07T18:21:54.000000Z",
                "timestamps.orderReceived": "2024-03-07T18:21:54.000000Z",
                "_orderState.executionDetails.orderStatus": "PARF",
                "instrumentDetails.instrument": {
                    "sourceKey": "cmeInstruments.XCBT.FCAXSX.20240405.020040",
                    "instrumentClassificationEMIRAssetClass": "CO",
                    "&id": "XCBT|ZCN4|20240712|USD",
                    "cfiGroup": "Commodities futures",
                    "cfiCategory": "Futures",
                    "commoditiesOrEmissionAllowanceDerivativeInd": True,
                    "instrumentFullName": "XCBT - ZC - Jul-24 - Future",
                    "&key": "VenueDirectInstrument:XCBT|ZCN4|20240712|USD:1712336495",
                    "notionalCurrency1": "USD",
                    "instrumentClassificationEMIRProductType": "FUT",
                    "instrumentClassificationEMIRContractType": "FU",
                    "cfiAttribute1": "Agriculture",
                    "cfiAttribute2": "Not Applicable/Undefined",
                    "cfiAttribute3": "Standardized",
                    "cfiAttribute4": "Not Applicable/Undefined",
                    "instrumentClassification": "FCAXSX",
                    "ext.venueName": "Chicago Board Of Trade",
                    "ext.mifirEligible": False,
                    "ext.exchangeSymbolLocal": "347006",
                    "ext.emirEligible": True,
                    "ext.bestExAssetClassSub": "Futures and options admitted to trading on a trading venue",
                    "ext.exchangeSymbol": "ZCN4",
                    "ext.quantityNotation": "UNIT",
                    "ext.bestExAssetClassMain": "Commodities derivatives and emission allowances Derivatives",
                    "ext.alternativeInstrumentIdentifier": "XCBTZCFF2024-07-12 00:00:00",
                    "ext.instrumentUniqueIdentifier": "XCBT|ZCN4|20240712|USD",
                    "ext.priceNotation": "MONE",
                    "ext.exchangeSymbolRoot": "ZC",
                    "venue.tradingVenue": "XCBT",
                    "venue.financialInstrumentShortName": "ZCN4",
                    "commodityAndEmissionAllowances.quantityUnitOfMeasure": "BUSHELS",
                    "derivative.expiryDate": "2024-07-12",
                    "derivative.priceDisplayFactor": "1",
                    "derivative.deliveryType": "OPTL",
                    "derivative.isUserDefinedSpread": False,
                    "derivative.priceMultiplier": "5000.000000000",
                },
                "marketIdentifiers.instrument": pd.NA,
            },
            {
                "transactionDetails.ultimateVenue": pd.NA,
                "executionDetails.tradingCapacity": "AOTC",
                "executionDetails.buySellIndicator": "SELL",
                "_orderState.buySell": "SELL",
                "_orderState.timestamps.tradingDateTime": "2024-03-07T18:33:17.000000Z",
                "orderIdentifiers.orderIdCode": "d8c27379-dd4f-4f58-a92f-687628f5b241|487769",
                "_orderState.id": "d8c27379-dd4f-4f58-a92f-687628f5b241|487769",
                "_order.transactionDetails.quantity": 0,
                "_orderState.transactionDetails.quantity": 10.0,
                "_orderState.orderIdentifiers.transactionRefNo": "jM2wcTDfA70cxFwK-XCME-1|9687|d8c27379-dd4f-4f58-a92f",
                "_orderState.transactionDetails.tradingDateTime": "2024-03-07T18:33:17.000000Z",
                "timestamps.orderSubmitted": "2024-03-07T18:33:17.000000Z",
                "date": "2024-03-07",
                "transactionDetails.settlementDate": pd.NA,
                "__expiry_date__": "2024-12-13",
                "transactionDetails.positionEffect": pd.NA,
                "executionDetails.orderType": "Limit",
                "__asset_class__": "future",
                "orderClass": pd.NA,
                "EXECUTION_WITHIN_FIRM": "NORE",
                "executionDetails.passiveOnlyIndicator": pd.NA,
                "transactionDetails.tradingCapacity": "AOTC",
                "EXECUTINGENTITYID": "lei:549300YCOYUK6X2LTB82",
                "transactionDetails.priceCurrency": "USD",
                "__last_px__": 468.0,
                "__price__": -18.5,
                "__option_strike_price__": pd.NA,
                "_orderState.transactionDetails.priceAverage": 467.8617647059,
                "_orderState.__meta_parent__": "d8c27379-dd4f-4f58-a92f-687628f5b241|487769:SELL:NEWO",
                "executionDetails.outgoingOrderAddlInfo": "2502111205808332076|ZC|<EMAIL>",
                "__id_single_stock__": "2|d8c27379-dd4f-4f58-a92f-687628f5b241",
                "__side_trade_report_security_id__": "2|2502111205808332076",
                "__id_individual_leg_of_mleg__": "2|d8c27379-dd4f-4f58-a92f-687628f5b241|M|487769",
                "__id_mleg__": "2|d8c27379-dd4f-4f58-a92f-687628f5b241|M",
                "_order.priceFormingData.tradedQuantity": 0,
                "_order.transactionDetails.cumulativeQuantity": 0,
                "_order.transactionDetails.priceAverage": 0,
                "_order.__meta_model__": "Order",
                "_orderState.__meta_model__": "OrderState",
                "_orderState.sourceIndex": "2",
                "_order.sourceIndex": "2",
                "__cash__": "CASH",
                "dataSourceName": "TT",
                "__newo_in_file_col__": False,
                "_order.executionDetails.orderStatus": "NEWO",
                "__static_text_m__": "M",
                "transactionDetails.recordType": "Market Side",
                "transactionDetails.settlementAmountCurrency": "USD",
                "transactionDetails.quantityNotation": "UNIT",
                "_orderState.priceFormingData.remainingQuantity": 492.0,
                "__limit_price__": pd.NA,
                "executionDetails.passiveAggressiveIndicator": pd.NA,
                "__option_type__": pd.NA,
                "_order.id": "d8c27379-dd4f-4f58-a92f-687628f5b241|487769",
                "__isin__": pd.NA,
                "orderIdentifiers.tradingVenueTransactionIdCode": "OSNALTNA017sTaI1f-00",
                "priceFormingData.initialQuantity": 502.0,
                "orderIdentifiers.aggregatedOrderId": "d8c27379-dd4f-4f58-a92f-687628f5b241:171:2",
                "_orderState.reportDetails.transactionRefNo": "jM2wcTDfA70cxFwK-XCME-1|9687|d8c27379-dd4f-4f58-a92f",
                "TRADERID": "JBrosterman",
                "CLIENTID": "CAX",
                "__underlying_symbol__": "ZC",
                "transactionDetails.settlementAmount": "0",
                "transactionDetails.priceNotation": "MONE",
                "_orderState.transactionDetails.cumulativeQuantity": 10.0,
                "_orderState.priceFormingData.tradedQuantity": 10.0,
                "_order.priceFormingData.remainingQuantity": 492.0,
                "__derivative_price_multiplier__": pd.NA,
                "transactionDetails.basketId": pd.NA,
                "transactionDetails.netAmount": "0",
                "transactionDetails.commissionAmount": "0",
                "transactionDetails.commissionAmountCurrency": "USD",
                "transactionDetails.commissionAmountType": pd.NA,
                "__raw_order_status__": "1",
                "__symbol__": "ZC",
                "__security_id__": "2502111205808332076",
                "INVESTMENTDECISIONMAKER": pd.NA,
                "COUNTERPARTYID": "JPM",
                "__option_type_mleg__": "",
                "_order.buySell": "SELL",
                "sourceKey": "s3://test.steeleye.co/ingress/raw/order-feed-tt-fix-controller/20240307/007b4d45c7a1f6d6efea28d606246f80f93c1ccf6f410e0ddc75a95f63523896_9687.fix",
                "orderIdentifiers.internalOrderIdCode": "1709764327342",
                "__market_segment_id__": pd.NA,
                "transactionDetails.buySellIndicator": "SELL",
                "__final_derived_side__": "2",
                "_orderState.timestamps.orderStatusUpdated": "2024-03-07T18:33:17.000000Z",
                "_order.timestamps.orderStatusUpdated": "2024-03-07T18:33:17.000000Z",
                "timestamps.orderReceived": "2024-03-07T18:33:17.000000Z",
                "_orderState.executionDetails.orderStatus": "PARF",
                "instrumentDetails.instrument": {
                    "sourceKey": "cmeInstruments.XCBT.FCAXSX.20240405.020040",
                    "instrumentClassificationEMIRAssetClass": "CO",
                    "&id": "XCBT|ZCZ4|20241213|USD",
                    "cfiGroup": "Commodities futures",
                    "cfiCategory": "Futures",
                    "commoditiesOrEmissionAllowanceDerivativeInd": True,
                    "instrumentFullName": "XCBT - ZC - Dec-24 - Future",
                    "&key": "VenueDirectInstrument:XCBT|ZCZ4|20241213|USD:1712336495",
                    "notionalCurrency1": "USD",
                    "instrumentClassificationEMIRProductType": "FUT",
                    "instrumentClassificationEMIRContractType": "FU",
                    "cfiAttribute1": "Agriculture",
                    "cfiAttribute2": "Not Applicable/Undefined",
                    "cfiAttribute3": "Standardized",
                    "cfiAttribute4": "Not Applicable/Undefined",
                    "instrumentClassification": "FCAXSX",
                    "ext.venueName": "Chicago Board Of Trade",
                    "ext.mifirEligible": False,
                    "ext.exchangeSymbolLocal": "487769",
                    "ext.emirEligible": True,
                    "ext.bestExAssetClassSub": "Futures and options admitted to trading on a trading venue",
                    "ext.exchangeSymbol": "ZCZ4",
                    "ext.quantityNotation": "UNIT",
                    "ext.bestExAssetClassMain": "Commodities derivatives and emission allowances Derivatives",
                    "ext.alternativeInstrumentIdentifier": "XCBTZCFF2024-12-13 00:00:00",
                    "ext.instrumentUniqueIdentifier": "XCBT|ZCZ4|20241213|USD",
                    "ext.priceNotation": "MONE",
                    "ext.exchangeSymbolRoot": "ZC",
                    "venue.tradingVenue": "XCBT",
                    "venue.financialInstrumentShortName": "ZCZ4",
                    "commodityAndEmissionAllowances.quantityUnitOfMeasure": "BUSHELS",
                    "derivative.expiryDate": "2024-12-13",
                    "derivative.priceDisplayFactor": "1",
                    "derivative.deliveryType": "OPTL",
                    "derivative.isUserDefinedSpread": False,
                    "derivative.priceMultiplier": "5000.000000000",
                },
                "marketIdentifiers.instrument": pd.NA,
            },
            {
                "transactionDetails.ultimateVenue": pd.NA,
                "executionDetails.tradingCapacity": "AOTC",
                "executionDetails.buySellIndicator": "SELL",
                "_orderState.buySell": "SELL",
                "_orderState.timestamps.tradingDateTime": "2024-03-07T18:26:13.000000Z",
                "orderIdentifiers.orderIdCode": "d8c27379-dd4f-4f58-a92f-687628f5b241|487769",
                "_orderState.id": "d8c27379-dd4f-4f58-a92f-687628f5b241|487769",
                "_order.transactionDetails.quantity": 0,
                "_orderState.transactionDetails.quantity": 1.0,
                "_orderState.orderIdentifiers.transactionRefNo": "jM2wcTDfA70cxEUM-XCME-1|9348|d8c27379-dd4f-4f58-a92f",
                "_orderState.transactionDetails.tradingDateTime": "2024-03-07T18:26:13.000000Z",
                "timestamps.orderSubmitted": "2024-03-07T18:26:13.000000Z",
                "date": "2024-03-07",
                "transactionDetails.settlementDate": pd.NA,
                "__expiry_date__": "2024-12-13",
                "transactionDetails.positionEffect": pd.NA,
                "executionDetails.orderType": "Limit",
                "__asset_class__": "future",
                "orderClass": pd.NA,
                "EXECUTION_WITHIN_FIRM": "NORE",
                "executionDetails.passiveOnlyIndicator": pd.NA,
                "transactionDetails.tradingCapacity": "AOTC",
                "EXECUTINGENTITYID": "lei:549300YCOYUK6X2LTB82",
                "transactionDetails.priceCurrency": "USD",
                "__last_px__": 467.75,
                "__price__": -18.5,
                "__option_strike_price__": pd.NA,
                "_orderState.transactionDetails.priceAverage": 467.8504098361,
                "_orderState.__meta_parent__": "d8c27379-dd4f-4f58-a92f-687628f5b241|487769:SELL:NEWO",
                "executionDetails.outgoingOrderAddlInfo": "2502111205808332076|ZC|<EMAIL>",
                "__id_single_stock__": "2|d8c27379-dd4f-4f58-a92f-687628f5b241",
                "__side_trade_report_security_id__": "2|2502111205808332076",
                "__id_individual_leg_of_mleg__": "2|d8c27379-dd4f-4f58-a92f-687628f5b241|M|487769",
                "__id_mleg__": "2|d8c27379-dd4f-4f58-a92f-687628f5b241|M",
                "_order.priceFormingData.tradedQuantity": 0,
                "_order.transactionDetails.cumulativeQuantity": 0,
                "_order.transactionDetails.priceAverage": 0,
                "_order.__meta_model__": "Order",
                "_orderState.__meta_model__": "OrderState",
                "_orderState.sourceIndex": "3",
                "_order.sourceIndex": "3",
                "__cash__": "CASH",
                "dataSourceName": "TT",
                "__newo_in_file_col__": False,
                "_order.executionDetails.orderStatus": "NEWO",
                "__static_text_m__": "M",
                "transactionDetails.recordType": "Market Side",
                "transactionDetails.settlementAmountCurrency": "USD",
                "transactionDetails.quantityNotation": "UNIT",
                "_orderState.priceFormingData.remainingQuantity": 491.0,
                "__limit_price__": pd.NA,
                "executionDetails.passiveAggressiveIndicator": pd.NA,
                "__option_type__": pd.NA,
                "_order.id": "d8c27379-dd4f-4f58-a92f-687628f5b241|487769",
                "__isin__": pd.NA,
                "orderIdentifiers.tradingVenueTransactionIdCode": "OSNALTNA017sTaI1f-00",
                "priceFormingData.initialQuantity": 502.0,
                "orderIdentifiers.aggregatedOrderId": "d8c27379-dd4f-4f58-a92f-687628f5b241:104:2",
                "_orderState.reportDetails.transactionRefNo": "jM2wcTDfA70cxEUM-XCME-1|9348|d8c27379-dd4f-4f58-a92f",
                "TRADERID": "JBrosterman",
                "CLIENTID": "CAX",
                "__underlying_symbol__": "ZC",
                "transactionDetails.settlementAmount": "0",
                "transactionDetails.priceNotation": "MONE",
                "_orderState.transactionDetails.cumulativeQuantity": 11.0,
                "_orderState.priceFormingData.tradedQuantity": 1.0,
                "_order.priceFormingData.remainingQuantity": 491.0,
                "__derivative_price_multiplier__": pd.NA,
                "transactionDetails.basketId": pd.NA,
                "transactionDetails.netAmount": "0",
                "transactionDetails.commissionAmount": "0",
                "transactionDetails.commissionAmountCurrency": "USD",
                "transactionDetails.commissionAmountType": pd.NA,
                "__raw_order_status__": "1",
                "__symbol__": "ZC",
                "__security_id__": "2502111205808332076",
                "INVESTMENTDECISIONMAKER": pd.NA,
                "COUNTERPARTYID": "JPM",
                "__option_type_mleg__": "",
                "_order.buySell": "SELL",
                "sourceKey": "s3://test.steeleye.co/ingress/raw/order-feed-tt-fix-controller/20240307/009cb1581369adb93aa031a9851dbe7b23d623f71e4ffbeaccc994ef61035820_9348.fix",
                "orderIdentifiers.internalOrderIdCode": "1709764327342",
                "__market_segment_id__": pd.NA,
                "transactionDetails.buySellIndicator": "SELL",
                "__final_derived_side__": "2",
                "_orderState.timestamps.orderStatusUpdated": "2024-03-07T18:26:13.000000Z",
                "_order.timestamps.orderStatusUpdated": "2024-03-07T18:26:13.000000Z",
                "timestamps.orderReceived": "2024-03-07T18:26:13.000000Z",
                "_orderState.executionDetails.orderStatus": "PARF",
                "instrumentDetails.instrument": {
                    "sourceKey": "cmeInstruments.XCBT.FCAXSX.20240405.020040",
                    "instrumentClassificationEMIRAssetClass": "CO",
                    "&id": "XCBT|ZCZ4|20241213|USD",
                    "cfiGroup": "Commodities futures",
                    "cfiCategory": "Futures",
                    "commoditiesOrEmissionAllowanceDerivativeInd": True,
                    "instrumentFullName": "XCBT - ZC - Dec-24 - Future",
                    "&key": "VenueDirectInstrument:XCBT|ZCZ4|20241213|USD:1712336495",
                    "notionalCurrency1": "USD",
                    "instrumentClassificationEMIRProductType": "FUT",
                    "instrumentClassificationEMIRContractType": "FU",
                    "cfiAttribute1": "Agriculture",
                    "cfiAttribute2": "Not Applicable/Undefined",
                    "cfiAttribute3": "Standardized",
                    "cfiAttribute4": "Not Applicable/Undefined",
                    "instrumentClassification": "FCAXSX",
                    "ext.venueName": "Chicago Board Of Trade",
                    "ext.mifirEligible": False,
                    "ext.exchangeSymbolLocal": "487769",
                    "ext.emirEligible": True,
                    "ext.bestExAssetClassSub": "Futures and options admitted to trading on a trading venue",
                    "ext.exchangeSymbol": "ZCZ4",
                    "ext.quantityNotation": "UNIT",
                    "ext.bestExAssetClassMain": "Commodities derivatives and emission allowances Derivatives",
                    "ext.alternativeInstrumentIdentifier": "XCBTZCFF2024-12-13 00:00:00",
                    "ext.instrumentUniqueIdentifier": "XCBT|ZCZ4|20241213|USD",
                    "ext.priceNotation": "MONE",
                    "ext.exchangeSymbolRoot": "ZC",
                    "venue.tradingVenue": "XCBT",
                    "venue.financialInstrumentShortName": "ZCZ4",
                    "commodityAndEmissionAllowances.quantityUnitOfMeasure": "BUSHELS",
                    "derivative.expiryDate": "2024-12-13",
                    "derivative.priceDisplayFactor": "1",
                    "derivative.deliveryType": "OPTL",
                    "derivative.isUserDefinedSpread": False,
                    "derivative.priceMultiplier": "5000.000000000",
                },
                "marketIdentifiers.instrument": pd.NA,
            },
            {
                "transactionDetails.ultimateVenue": pd.NA,
                "executionDetails.tradingCapacity": "AOTC",
                "executionDetails.buySellIndicator": "SELL",
                "_orderState.buySell": "SELL",
                "_orderState.timestamps.tradingDateTime": "2024-03-07T18:25:12.000000Z",
                "orderIdentifiers.orderIdCode": "d8c27379-dd4f-4f58-a92f-687628f5b241|487769",
                "_orderState.id": "d8c27379-dd4f-4f58-a92f-687628f5b241|487769",
                "_order.transactionDetails.quantity": 0,
                "_orderState.transactionDetails.quantity": 1.0,
                "_orderState.orderIdentifiers.transactionRefNo": "jM2wcTDfA70cxEOR-XCME-1|9291|d8c27379-dd4f-4f58-a92f",
                "_orderState.transactionDetails.tradingDateTime": "2024-03-07T18:25:12.000000Z",
                "timestamps.orderSubmitted": "2024-03-07T18:25:12.000000Z",
                "date": "2024-03-07",
                "transactionDetails.settlementDate": pd.NA,
                "__expiry_date__": "2024-12-13",
                "transactionDetails.positionEffect": pd.NA,
                "executionDetails.orderType": "Limit",
                "__asset_class__": "future",
                "orderClass": pd.NA,
                "EXECUTION_WITHIN_FIRM": "NORE",
                "executionDetails.passiveOnlyIndicator": pd.NA,
                "transactionDetails.tradingCapacity": "AOTC",
                "EXECUTINGENTITYID": "lei:549300YCOYUK6X2LTB82",
                "transactionDetails.priceCurrency": "USD",
                "__last_px__": 467.75,
                "__price__": -18.5,
                "__option_strike_price__": pd.NA,
                "_orderState.transactionDetails.priceAverage": 467.8616504854,
                "_orderState.__meta_parent__": "d8c27379-dd4f-4f58-a92f-687628f5b241|487769:SELL:NEWO",
                "executionDetails.outgoingOrderAddlInfo": "2502111205808332076|ZC|<EMAIL>",
                "__id_single_stock__": "2|d8c27379-dd4f-4f58-a92f-687628f5b241",
                "__side_trade_report_security_id__": "2|2502111205808332076",
                "__id_individual_leg_of_mleg__": "2|d8c27379-dd4f-4f58-a92f-687628f5b241|M|487769",
                "__id_mleg__": "2|d8c27379-dd4f-4f58-a92f-687628f5b241|M",
                "_order.priceFormingData.tradedQuantity": 0,
                "_order.transactionDetails.cumulativeQuantity": 0,
                "_order.transactionDetails.priceAverage": 0,
                "_order.__meta_model__": "Order",
                "_orderState.__meta_model__": "OrderState",
                "_orderState.sourceIndex": "4",
                "_order.sourceIndex": "4",
                "__cash__": "CASH",
                "dataSourceName": "TT",
                "__newo_in_file_col__": False,
                "_order.executionDetails.orderStatus": "NEWO",
                "__static_text_m__": "M",
                "transactionDetails.recordType": "Market Side",
                "transactionDetails.settlementAmountCurrency": "USD",
                "transactionDetails.quantityNotation": "UNIT",
                "_orderState.priceFormingData.remainingQuantity": 490.0,
                "__limit_price__": pd.NA,
                "executionDetails.passiveAggressiveIndicator": pd.NA,
                "__option_type__": pd.NA,
                "_order.id": "d8c27379-dd4f-4f58-a92f-687628f5b241|487769",
                "__isin__": pd.NA,
                "orderIdentifiers.tradingVenueTransactionIdCode": "OSNALTNA017sTaI1f-00",
                "priceFormingData.initialQuantity": 502.0,
                "orderIdentifiers.aggregatedOrderId": "d8c27379-dd4f-4f58-a92f-687628f5b241:85:2",
                "_orderState.reportDetails.transactionRefNo": "jM2wcTDfA70cxEOR-XCME-1|9291|d8c27379-dd4f-4f58-a92f",
                "TRADERID": "JBrosterman",
                "CLIENTID": "CAX",
                "__underlying_symbol__": "ZC",
                "transactionDetails.settlementAmount": "0",
                "transactionDetails.priceNotation": "MONE",
                "_orderState.transactionDetails.cumulativeQuantity": 12.0,
                "_orderState.priceFormingData.tradedQuantity": 1.0,
                "_order.priceFormingData.remainingQuantity": 490.0,
                "__derivative_price_multiplier__": pd.NA,
                "transactionDetails.basketId": pd.NA,
                "transactionDetails.netAmount": "0",
                "transactionDetails.commissionAmount": "0",
                "transactionDetails.commissionAmountCurrency": "USD",
                "transactionDetails.commissionAmountType": pd.NA,
                "__raw_order_status__": "1",
                "__symbol__": "ZC",
                "__security_id__": "2502111205808332076",
                "INVESTMENTDECISIONMAKER": pd.NA,
                "COUNTERPARTYID": "JPM",
                "__option_type_mleg__": "",
                "_order.buySell": "SELL",
                "sourceKey": "s3://test.steeleye.co/ingress/raw/order-feed-tt-fix-controller/20240307/019c3df6d6e937f0cd4ba82fa65e5eee6bbba5546c871da9021547c4119d100c_9291.fix",
                "orderIdentifiers.internalOrderIdCode": "1709764327342",
                "__market_segment_id__": pd.NA,
                "transactionDetails.buySellIndicator": "SELL",
                "__final_derived_side__": "2",
                "_orderState.timestamps.orderStatusUpdated": "2024-03-07T18:25:12.000000Z",
                "_order.timestamps.orderStatusUpdated": "2024-03-07T18:25:12.000000Z",
                "timestamps.orderReceived": "2024-03-07T18:25:12.000000Z",
                "_orderState.executionDetails.orderStatus": "PARF",
                "instrumentDetails.instrument": {
                    "sourceKey": "cmeInstruments.XCBT.FCAXSX.20240405.020040",
                    "instrumentClassificationEMIRAssetClass": "CO",
                    "&id": "XCBT|ZCZ4|20241213|USD",
                    "cfiGroup": "Commodities futures",
                    "cfiCategory": "Futures",
                    "commoditiesOrEmissionAllowanceDerivativeInd": True,
                    "instrumentFullName": "XCBT - ZC - Dec-24 - Future",
                    "&key": "VenueDirectInstrument:XCBT|ZCZ4|20241213|USD:1712336495",
                    "notionalCurrency1": "USD",
                    "instrumentClassificationEMIRProductType": "FUT",
                    "instrumentClassificationEMIRContractType": "FU",
                    "cfiAttribute1": "Agriculture",
                    "cfiAttribute2": "Not Applicable/Undefined",
                    "cfiAttribute3": "Standardized",
                    "cfiAttribute4": "Not Applicable/Undefined",
                    "instrumentClassification": "FCAXSX",
                    "ext.venueName": "Chicago Board Of Trade",
                    "ext.mifirEligible": False,
                    "ext.exchangeSymbolLocal": "487769",
                    "ext.emirEligible": True,
                    "ext.bestExAssetClassSub": "Futures and options admitted to trading on a trading venue",
                    "ext.exchangeSymbol": "ZCZ4",
                    "ext.quantityNotation": "UNIT",
                    "ext.bestExAssetClassMain": "Commodities derivatives and emission allowances Derivatives",
                    "ext.alternativeInstrumentIdentifier": "XCBTZCFF2024-12-13 00:00:00",
                    "ext.instrumentUniqueIdentifier": "XCBT|ZCZ4|20241213|USD",
                    "ext.priceNotation": "MONE",
                    "ext.exchangeSymbolRoot": "ZC",
                    "venue.tradingVenue": "XCBT",
                    "venue.financialInstrumentShortName": "ZCZ4",
                    "commodityAndEmissionAllowances.quantityUnitOfMeasure": "BUSHELS",
                    "derivative.expiryDate": "2024-12-13",
                    "derivative.priceDisplayFactor": "1",
                    "derivative.deliveryType": "OPTL",
                    "derivative.isUserDefinedSpread": False,
                    "derivative.priceMultiplier": "5000.000000000",
                },
                "marketIdentifiers.instrument": pd.NA,
            },
            {
                "transactionDetails.ultimateVenue": pd.NA,
                "executionDetails.tradingCapacity": "AOTC",
                "executionDetails.buySellIndicator": "SELL",
                "_orderState.buySell": "SELL",
                "_orderState.timestamps.tradingDateTime": "2024-03-07T17:29:46.000000Z",
                "orderIdentifiers.orderIdCode": "a5b03ff3-bb69-4d72-8959-0f6e2cff50ef|487769",
                "_orderState.id": "a5b03ff3-bb69-4d72-8959-0f6e2cff50ef|487769",
                "_order.transactionDetails.quantity": 0,
                "_orderState.transactionDetails.quantity": 10.0,
                "_orderState.orderIdentifiers.transactionRefNo": "jMdOcTDfA40cwoLW-XCME|8631|a5b03ff3-bb69-4d72-8959-0",
                "_orderState.transactionDetails.tradingDateTime": "2024-03-07T17:29:46.000000Z",
                "timestamps.orderSubmitted": "2024-03-07T17:29:46.000000Z",
                "date": "2024-03-07",
                "transactionDetails.settlementDate": pd.NA,
                "__expiry_date__": "2024-12-13",
                "transactionDetails.positionEffect": pd.NA,
                "executionDetails.orderType": "Limit",
                "__asset_class__": "future",
                "orderClass": pd.NA,
                "EXECUTION_WITHIN_FIRM": "NORE",
                "executionDetails.passiveOnlyIndicator": pd.NA,
                "transactionDetails.tradingCapacity": "AOTC",
                "EXECUTINGENTITYID": "lei:549300YCOYUK6X2LTB82",
                "transactionDetails.priceCurrency": "USD",
                "__last_px__": 466.25,
                "__price__": 465.0,
                "__option_strike_price__": pd.NA,
                "_orderState.transactionDetails.priceAverage": 466.25,
                "_orderState.__meta_parent__": "a5b03ff3-bb69-4d72-8959-0f6e2cff50ef|487769:SELL:NEWO",
                "executionDetails.outgoingOrderAddlInfo": "2502111205808332076|ZC|<EMAIL>",
                "__id_single_stock__": "2|a5b03ff3-bb69-4d72-8959-0f6e2cff50ef",
                "__side_trade_report_security_id__": "2|2502111205808332076",
                "__id_individual_leg_of_mleg__": "2|a5b03ff3-bb69-4d72-8959-0f6e2cff50ef|M|487769",
                "__id_mleg__": "2|a5b03ff3-bb69-4d72-8959-0f6e2cff50ef|M",
                "_order.priceFormingData.tradedQuantity": 0,
                "_order.transactionDetails.cumulativeQuantity": 0,
                "_order.transactionDetails.priceAverage": 0,
                "_order.__meta_model__": "Order",
                "_orderState.__meta_model__": "OrderState",
                "_orderState.sourceIndex": "21",
                "_order.sourceIndex": "21",
                "__cash__": "CASH",
                "dataSourceName": "TT",
                "__newo_in_file_col__": False,
                "_order.executionDetails.orderStatus": "NEWO",
                "__static_text_m__": "M",
                "transactionDetails.recordType": "Market Side",
                "transactionDetails.settlementAmountCurrency": "USD",
                "transactionDetails.quantityNotation": "UNIT",
                "_orderState.priceFormingData.remainingQuantity": 24.0,
                "__limit_price__": 465.0,
                "executionDetails.passiveAggressiveIndicator": pd.NA,
                "__option_type__": pd.NA,
                "_order.id": "a5b03ff3-bb69-4d72-8959-0f6e2cff50ef|487769",
                "__isin__": pd.NA,
                "orderIdentifiers.tradingVenueTransactionIdCode": "OSNALTNA017sTMwqI-00",
                "priceFormingData.initialQuantity": 50.0,
                "orderIdentifiers.aggregatedOrderId": "a5b03ff3-bb69-4d72-8959-0f6e2cff50ef:9",
                "_orderState.reportDetails.transactionRefNo": "jMdOcTDfA40cwoLW-XCME|8631|a5b03ff3-bb69-4d72-8959-0",
                "TRADERID": "JBrosterman",
                "CLIENTID": "CAX",
                "__underlying_symbol__": "ZC",
                "transactionDetails.settlementAmount": "0",
                "transactionDetails.priceNotation": "MONE",
                "_orderState.transactionDetails.cumulativeQuantity": 26.0,
                "_orderState.priceFormingData.tradedQuantity": 10.0,
                "_order.priceFormingData.remainingQuantity": 24.0,
                "__derivative_price_multiplier__": pd.NA,
                "transactionDetails.basketId": pd.NA,
                "transactionDetails.netAmount": "0",
                "transactionDetails.commissionAmount": "0",
                "transactionDetails.commissionAmountCurrency": "USD",
                "transactionDetails.commissionAmountType": pd.NA,
                "__raw_order_status__": "1",
                "__symbol__": "ZC",
                "__security_id__": "2502111205808332076",
                "INVESTMENTDECISIONMAKER": pd.NA,
                "COUNTERPARTYID": "JPM",
                "__option_type_mleg__": "",
                "_order.buySell": "SELL",
                "sourceKey": "s3://test.steeleye.co/ingress/raw/order-feed-tt-fix-controller/20240307/0a80821810a455c45ceb60ae31b60dc66e7a8ec8af8f649830a41c73c042db0a_8631.fix",
                "orderIdentifiers.internalOrderIdCode": "1709764327340",
                "__market_segment_id__": pd.NA,
                "transactionDetails.buySellIndicator": "SELL",
                "__final_derived_side__": "2",
                "_orderState.timestamps.orderStatusUpdated": "2024-03-07T17:29:46.000000Z",
                "_order.timestamps.orderStatusUpdated": "2024-03-07T17:29:46.000000Z",
                "timestamps.orderReceived": "2024-03-07T17:29:46.000000Z",
                "_orderState.executionDetails.orderStatus": "PARF",
                "instrumentDetails.instrument": {
                    "sourceKey": "cmeInstruments.XCBT.FCAXSX.20240405.020040",
                    "instrumentClassificationEMIRAssetClass": "CO",
                    "&id": "XCBT|ZCZ4|20241213|USD",
                    "cfiGroup": "Commodities futures",
                    "cfiCategory": "Futures",
                    "commoditiesOrEmissionAllowanceDerivativeInd": True,
                    "instrumentFullName": "XCBT - ZC - Dec-24 - Future",
                    "&key": "VenueDirectInstrument:XCBT|ZCZ4|20241213|USD:1712336495",
                    "notionalCurrency1": "USD",
                    "instrumentClassificationEMIRProductType": "FUT",
                    "instrumentClassificationEMIRContractType": "FU",
                    "cfiAttribute1": "Agriculture",
                    "cfiAttribute2": "Not Applicable/Undefined",
                    "cfiAttribute3": "Standardized",
                    "cfiAttribute4": "Not Applicable/Undefined",
                    "instrumentClassification": "FCAXSX",
                    "ext.venueName": "Chicago Board Of Trade",
                    "ext.mifirEligible": False,
                    "ext.exchangeSymbolLocal": "487769",
                    "ext.emirEligible": True,
                    "ext.bestExAssetClassSub": "Futures and options admitted to trading on a trading venue",
                    "ext.exchangeSymbol": "ZCZ4",
                    "ext.quantityNotation": "UNIT",
                    "ext.bestExAssetClassMain": "Commodities derivatives and emission allowances Derivatives",
                    "ext.alternativeInstrumentIdentifier": "XCBTZCFF2024-12-13 00:00:00",
                    "ext.instrumentUniqueIdentifier": "XCBT|ZCZ4|20241213|USD",
                    "ext.priceNotation": "MONE",
                    "ext.exchangeSymbolRoot": "ZC",
                    "venue.tradingVenue": "XCBT",
                    "venue.financialInstrumentShortName": "ZCZ4",
                    "commodityAndEmissionAllowances.quantityUnitOfMeasure": "BUSHELS",
                    "derivative.expiryDate": "2024-12-13",
                    "derivative.priceDisplayFactor": "1",
                    "derivative.deliveryType": "OPTL",
                    "derivative.isUserDefinedSpread": False,
                    "derivative.priceMultiplier": "5000.000000000",
                },
                "marketIdentifiers.instrument": pd.NA,
            },
        ]
    )


@pytest.fixture()
def expected_flat_average_df():
    return pd.DataFrame(
        [
            {
                "transactionDetails.ultimateVenue": pd.NA,
                "executionDetails.tradingCapacity": "AOTC",
                "executionDetails.buySellIndicator": "SELL",
                "_orderState.buySell": "SELL",
                "_orderState.timestamps.tradingDateTime": "2024-03-07T17:29:46.000000Z",
                "orderIdentifiers.orderIdCode": "a5b03ff3-bb69-4d72-8959-0f6e2cff50ef|487769",
                "_orderState.id": "a5b03ff3-bb69-4d72-8959-0f6e2cff50ef|487769",
                "_order.transactionDetails.quantity": 0,
                "_orderState.transactionDetails.quantity": 16.0,
                "_orderState.orderIdentifiers.transactionRefNo": "jMdOcTDfA40cwoLT-XCME|8628|a5b03ff3-bb69-4d72-8959-0",
                "_orderState.transactionDetails.tradingDateTime": "2024-03-07T17:29:46.000000Z",
                "timestamps.orderSubmitted": "2024-03-07T17:29:46.000000Z",
                "date": "2024-03-07",
                "transactionDetails.settlementDate": pd.NA,
                "__expiry_date__": "2024-12-13",
                "transactionDetails.positionEffect": pd.NA,
                "executionDetails.orderType": "Limit",
                "__asset_class__": "future",
                "orderClass": pd.NA,
                "EXECUTION_WITHIN_FIRM": "NORE",
                "executionDetails.passiveOnlyIndicator": pd.NA,
                "transactionDetails.tradingCapacity": "AOTC",
                "EXECUTINGENTITYID": "lei:549300YCOYUK6X2LTB82",
                "transactionDetails.priceCurrency": "USD",
                "__last_px__": 466.25,
                "__price__": 465.0,
                "__option_strike_price__": pd.NA,
                "_orderState.transactionDetails.priceAverage": 466.25,
                "_orderState.__meta_parent__": "a5b03ff3-bb69-4d72-8959-0f6e2cff50ef|487769:SELL:NEWO",
                "executionDetails.outgoingOrderAddlInfo": "2502111205808332076|ZC|<EMAIL>",
                "__id_single_stock__": "2|a5b03ff3-bb69-4d72-8959-0f6e2cff50ef",
                "__side_trade_report_security_id__": "2|2502111205808332076",
                "__id_individual_leg_of_mleg__": "2|a5b03ff3-bb69-4d72-8959-0f6e2cff50ef|M|487769",
                "__id_mleg__": "2|a5b03ff3-bb69-4d72-8959-0f6e2cff50ef|M",
                "_order.priceFormingData.tradedQuantity": 0,
                "_order.transactionDetails.cumulativeQuantity": 0,
                "_order.transactionDetails.priceAverage": 0,
                "_order.__meta_model__": "Order",
                "_orderState.__meta_model__": "OrderState",
                "_orderState.sourceIndex": "0",
                "_order.sourceIndex": "0",
                "__cash__": "CASH",
                "dataSourceName": "TT",
                "__newo_in_file_col__": False,
                "_order.executionDetails.orderStatus": "NEWO",
                "__static_text_m__": "M",
                "transactionDetails.recordType": "Market Side",
                "transactionDetails.settlementAmountCurrency": "USD",
                "transactionDetails.quantityNotation": "UNIT",
                "_orderState.priceFormingData.remainingQuantity": 34.0,
                "__limit_price__": 465.0,
                "executionDetails.passiveAggressiveIndicator": pd.NA,
                "__option_type__": pd.NA,
                "_order.id": "a5b03ff3-bb69-4d72-8959-0f6e2cff50ef|487769",
                "__isin__": pd.NA,
                "orderIdentifiers.tradingVenueTransactionIdCode": "OSNALTNA017sTMwqI-00",
                "priceFormingData.initialQuantity": 50.0,
                "orderIdentifiers.aggregatedOrderId": "a5b03ff3-bb69-4d72-8959-0f6e2cff50ef:6",
                "_orderState.reportDetails.transactionRefNo": "jMdOcTDfA40cwoLT-XCME|8628|a5b03ff3-bb69-4d72-8959-0",
                "TRADERID": "JBrosterman",
                "CLIENTID": "CAX",
                "__underlying_symbol__": "ZC",
                "transactionDetails.settlementAmount": "0",
                "transactionDetails.priceNotation": "MONE",
                "_orderState.transactionDetails.cumulativeQuantity": 16.0,
                "_orderState.priceFormingData.tradedQuantity": 26.0,
                "_order.priceFormingData.remainingQuantity": 34.0,
                "__derivative_price_multiplier__": pd.NA,
                "transactionDetails.basketId": pd.NA,
                "transactionDetails.netAmount": "0",
                "transactionDetails.commissionAmount": "0",
                "transactionDetails.commissionAmountCurrency": "USD",
                "transactionDetails.commissionAmountType": pd.NA,
                "__raw_order_status__": "1",
                "__symbol__": "ZC",
                "__security_id__": "2502111205808332076",
                "INVESTMENTDECISIONMAKER": pd.NA,
                "COUNTERPARTYID": "JPM",
                "__option_type_mleg__": "",
                "_order.buySell": "SELL",
                "sourceKey": "s3://test.steeleye.co/ingress/raw/order-feed-tt-fix-controller/20240307/00053a9d3bfbb90a081075c1a2abf7e4065e0c8bb63bf8ca7957ad0ec33849e6_8628.fix",
                "orderIdentifiers.internalOrderIdCode": "1709764327340",
                "__market_segment_id__": pd.NA,
                "transactionDetails.buySellIndicator": "SELL",
                "__final_derived_side__": "2",
                "_orderState.timestamps.orderStatusUpdated": "2024-03-07T17:29:46.000000Z",
                "_order.timestamps.orderStatusUpdated": "2024-03-07T17:29:46.000000Z",
                "timestamps.orderReceived": "2024-03-07T17:29:46.000000Z",
                "_orderState.executionDetails.orderStatus": "PARF",
                "instrumentDetails.instrument": {
                    "sourceKey": "cmeInstruments.XCBT.FCAXSX.20240405.020040",
                    "instrumentClassificationEMIRAssetClass": "CO",
                    "&id": "XCBT|ZCZ4|20241213|USD",
                    "cfiGroup": "Commodities futures",
                    "cfiCategory": "Futures",
                    "commoditiesOrEmissionAllowanceDerivativeInd": True,
                    "instrumentFullName": "XCBT - ZC - Dec-24 - Future",
                    "&key": "VenueDirectInstrument:XCBT|ZCZ4|20241213|USD:1712336495",
                    "notionalCurrency1": "USD",
                    "instrumentClassificationEMIRProductType": "FUT",
                    "instrumentClassificationEMIRContractType": "FU",
                    "cfiAttribute1": "Agriculture",
                    "cfiAttribute2": "Not Applicable/Undefined",
                    "cfiAttribute3": "Standardized",
                    "cfiAttribute4": "Not Applicable/Undefined",
                    "instrumentClassification": "FCAXSX",
                    "ext.venueName": "Chicago Board Of Trade",
                    "ext.mifirEligible": False,
                    "ext.exchangeSymbolLocal": "487769",
                    "ext.emirEligible": True,
                    "ext.bestExAssetClassSub": "Futures and options admitted to trading on a trading venue",
                    "ext.exchangeSymbol": "ZCZ4",
                    "ext.quantityNotation": "UNIT",
                    "ext.bestExAssetClassMain": "Commodities derivatives and emission allowances Derivatives",
                    "ext.alternativeInstrumentIdentifier": "XCBTZCFF2024-12-13 00:00:00",
                    "ext.instrumentUniqueIdentifier": "XCBT|ZCZ4|20241213|USD",
                    "ext.priceNotation": "MONE",
                    "ext.exchangeSymbolRoot": "ZC",
                    "venue.tradingVenue": "XCBT",
                    "venue.financialInstrumentShortName": "ZCZ4",
                    "commodityAndEmissionAllowances.quantityUnitOfMeasure": "BUSHELS",
                    "derivative.expiryDate": "2024-12-13",
                    "derivative.priceDisplayFactor": "1",
                    "derivative.deliveryType": "OPTL",
                    "derivative.isUserDefinedSpread": False,
                    "derivative.priceMultiplier": "5000.000000000",
                },
                "marketIdentifiers.instrument": pd.NA,
                "__order_id__": "a5b03ff3-bb69-4d72-8959-0f6e2cff50ef|487769",
            },
            {
                "transactionDetails.ultimateVenue": pd.NA,
                "executionDetails.tradingCapacity": "AOTC",
                "executionDetails.buySellIndicator": "BUYI",
                "_orderState.buySell": "BUYI",
                "_orderState.timestamps.tradingDateTime": "2024-03-07T18:21:54.000000Z",
                "orderIdentifiers.orderIdCode": "d8c27379-dd4f-4f58-a92f-687628f5b241|347006",
                "_orderState.id": "d8c27379-dd4f-4f58-a92f-687628f5b241|347006",
                "_order.transactionDetails.quantity": 0,
                "_orderState.transactionDetails.quantity": 100.0,
                "_orderState.orderIdentifiers.transactionRefNo": "jM2wcTDfA70cxEwj-XCME-0|9096|d8c27379-dd4f-4f58-a92f",
                "_orderState.transactionDetails.tradingDateTime": "2024-03-07T18:21:54.000000Z",
                "timestamps.orderSubmitted": "2024-03-07T18:21:54.000000Z",
                "date": "2024-03-07",
                "transactionDetails.settlementDate": pd.NA,
                "__expiry_date__": "2024-07-12",
                "transactionDetails.positionEffect": pd.NA,
                "executionDetails.orderType": "Limit",
                "__asset_class__": "future",
                "orderClass": pd.NA,
                "EXECUTION_WITHIN_FIRM": "NORE",
                "executionDetails.passiveOnlyIndicator": pd.NA,
                "transactionDetails.tradingCapacity": "AOTC",
                "EXECUTINGENTITYID": "lei:549300YCOYUK6X2LTB82",
                "transactionDetails.priceCurrency": "USD",
                "__last_px__": 448.5,
                "__price__": -18.5,
                "__option_strike_price__": pd.NA,
                "_orderState.transactionDetails.priceAverage": 448.2890625,
                "_orderState.__meta_parent__": "d8c27379-dd4f-4f58-a92f-687628f5b241|347006:BUYI:NEWO",
                "executionDetails.outgoingOrderAddlInfo": "16169186788407864635|ZC|<EMAIL>",
                "__id_single_stock__": "1|d8c27379-dd4f-4f58-a92f-687628f5b241",
                "__side_trade_report_security_id__": "1|16169186788407864635",
                "__id_individual_leg_of_mleg__": "1|d8c27379-dd4f-4f58-a92f-687628f5b241|M|347006",
                "__id_mleg__": "1|d8c27379-dd4f-4f58-a92f-687628f5b241|M",
                "_order.priceFormingData.tradedQuantity": 0,
                "_order.transactionDetails.cumulativeQuantity": 0,
                "_order.transactionDetails.priceAverage": 0,
                "_order.__meta_model__": "Order",
                "_orderState.__meta_model__": "OrderState",
                "_orderState.sourceIndex": "1",
                "_order.sourceIndex": "1",
                "__cash__": "CASH",
                "dataSourceName": "TT",
                "__newo_in_file_col__": False,
                "_order.executionDetails.orderStatus": "NEWO",
                "__static_text_m__": "M",
                "transactionDetails.recordType": "Market Side",
                "transactionDetails.settlementAmountCurrency": "USD",
                "transactionDetails.quantityNotation": "UNIT",
                "_orderState.priceFormingData.remainingQuantity": 402.0,
                "__limit_price__": pd.NA,
                "executionDetails.passiveAggressiveIndicator": pd.NA,
                "__option_type__": pd.NA,
                "_order.id": "d8c27379-dd4f-4f58-a92f-687628f5b241|347006",
                "__isin__": pd.NA,
                "orderIdentifiers.tradingVenueTransactionIdCode": "OSNALTNA017sTaI1f-00",
                "priceFormingData.initialQuantity": 502.0,
                "orderIdentifiers.aggregatedOrderId": "d8c27379-dd4f-4f58-a92f-687628f5b241:21:1",
                "_orderState.reportDetails.transactionRefNo": "jM2wcTDfA70cxEwj-XCME-0|9096|d8c27379-dd4f-4f58-a92f",
                "TRADERID": "JBrosterman",
                "CLIENTID": "CAX",
                "__underlying_symbol__": "ZC",
                "transactionDetails.settlementAmount": "0",
                "transactionDetails.priceNotation": "MONE",
                "_orderState.transactionDetails.cumulativeQuantity": 100.0,
                "_orderState.priceFormingData.tradedQuantity": 100.0,
                "_order.priceFormingData.remainingQuantity": 402.0,
                "__derivative_price_multiplier__": pd.NA,
                "transactionDetails.basketId": pd.NA,
                "transactionDetails.netAmount": "0",
                "transactionDetails.commissionAmount": "0",
                "transactionDetails.commissionAmountCurrency": "USD",
                "transactionDetails.commissionAmountType": pd.NA,
                "__raw_order_status__": "1",
                "__symbol__": "ZC",
                "__security_id__": "16169186788407864635",
                "INVESTMENTDECISIONMAKER": pd.NA,
                "COUNTERPARTYID": "JPM",
                "__option_type_mleg__": "",
                "_order.buySell": "BUYI",
                "sourceKey": "s3://test.steeleye.co/ingress/raw/order-feed-tt-fix-controller/20240307/00681c47836b651f4b0bcfc782594364d40ec1a4ef84b95db7a9b14895606ffc_9096.fix",
                "orderIdentifiers.internalOrderIdCode": "1709764327342",
                "__market_segment_id__": pd.NA,
                "transactionDetails.buySellIndicator": "BUYI",
                "__final_derived_side__": "1",
                "_orderState.timestamps.orderStatusUpdated": "2024-03-07T18:21:54.000000Z",
                "_order.timestamps.orderStatusUpdated": "2024-03-07T18:21:54.000000Z",
                "timestamps.orderReceived": "2024-03-07T18:21:54.000000Z",
                "_orderState.executionDetails.orderStatus": "PARF",
                "instrumentDetails.instrument": {
                    "sourceKey": "cmeInstruments.XCBT.FCAXSX.20240405.020040",
                    "instrumentClassificationEMIRAssetClass": "CO",
                    "&id": "XCBT|ZCN4|20240712|USD",
                    "cfiGroup": "Commodities futures",
                    "cfiCategory": "Futures",
                    "commoditiesOrEmissionAllowanceDerivativeInd": True,
                    "instrumentFullName": "XCBT - ZC - Jul-24 - Future",
                    "&key": "VenueDirectInstrument:XCBT|ZCN4|20240712|USD:1712336495",
                    "notionalCurrency1": "USD",
                    "instrumentClassificationEMIRProductType": "FUT",
                    "instrumentClassificationEMIRContractType": "FU",
                    "cfiAttribute1": "Agriculture",
                    "cfiAttribute2": "Not Applicable/Undefined",
                    "cfiAttribute3": "Standardized",
                    "cfiAttribute4": "Not Applicable/Undefined",
                    "instrumentClassification": "FCAXSX",
                    "ext.venueName": "Chicago Board Of Trade",
                    "ext.mifirEligible": False,
                    "ext.exchangeSymbolLocal": "347006",
                    "ext.emirEligible": True,
                    "ext.bestExAssetClassSub": "Futures and options admitted to trading on a trading venue",
                    "ext.exchangeSymbol": "ZCN4",
                    "ext.quantityNotation": "UNIT",
                    "ext.bestExAssetClassMain": "Commodities derivatives and emission allowances Derivatives",
                    "ext.alternativeInstrumentIdentifier": "XCBTZCFF2024-07-12 00:00:00",
                    "ext.instrumentUniqueIdentifier": "XCBT|ZCN4|20240712|USD",
                    "ext.priceNotation": "MONE",
                    "ext.exchangeSymbolRoot": "ZC",
                    "venue.tradingVenue": "XCBT",
                    "venue.financialInstrumentShortName": "ZCN4",
                    "commodityAndEmissionAllowances.quantityUnitOfMeasure": "BUSHELS",
                    "derivative.expiryDate": "2024-07-12",
                    "derivative.priceDisplayFactor": "1",
                    "derivative.deliveryType": "OPTL",
                    "derivative.isUserDefinedSpread": False,
                    "derivative.priceMultiplier": "5000.000000000",
                },
                "marketIdentifiers.instrument": pd.NA,
                "__order_id__": "d8c27379-dd4f-4f58-a92f-687628f5b241|347006",
            },
            {
                "transactionDetails.ultimateVenue": pd.NA,
                "executionDetails.tradingCapacity": "AOTC",
                "executionDetails.buySellIndicator": "SELL",
                "_orderState.buySell": "SELL",
                "_orderState.timestamps.tradingDateTime": "2024-03-07T18:25:12.000000Z",
                "orderIdentifiers.orderIdCode": "d8c27379-dd4f-4f58-a92f-687628f5b241|487769",
                "_orderState.id": "d8c27379-dd4f-4f58-a92f-687628f5b241|487769",
                "_order.transactionDetails.quantity": 0,
                "_orderState.transactionDetails.quantity": 1.0,
                "_orderState.orderIdentifiers.transactionRefNo": "jM2wcTDfA70cxEOR-XCME-1|9291|d8c27379-dd4f-4f58-a92f",
                "_orderState.transactionDetails.tradingDateTime": "2024-03-07T18:25:12.000000Z",
                "timestamps.orderSubmitted": "2024-03-07T18:25:12.000000Z",
                "date": "2024-03-07",
                "transactionDetails.settlementDate": pd.NA,
                "__expiry_date__": "2024-12-13",
                "transactionDetails.positionEffect": pd.NA,
                "executionDetails.orderType": "Limit",
                "__asset_class__": "future",
                "orderClass": pd.NA,
                "EXECUTION_WITHIN_FIRM": "NORE",
                "executionDetails.passiveOnlyIndicator": pd.NA,
                "transactionDetails.tradingCapacity": "AOTC",
                "EXECUTINGENTITYID": "lei:549300YCOYUK6X2LTB82",
                "transactionDetails.priceCurrency": "USD",
                "__last_px__": 467.9583333333333,
                "__price__": -18.5,
                "__option_strike_price__": pd.NA,
                "_orderState.transactionDetails.priceAverage": 467.8616504854,
                "_orderState.__meta_parent__": "d8c27379-dd4f-4f58-a92f-687628f5b241|487769:SELL:NEWO",
                "executionDetails.outgoingOrderAddlInfo": "2502111205808332076|ZC|<EMAIL>",
                "__id_single_stock__": "2|d8c27379-dd4f-4f58-a92f-687628f5b241",
                "__side_trade_report_security_id__": "2|2502111205808332076",
                "__id_individual_leg_of_mleg__": "2|d8c27379-dd4f-4f58-a92f-687628f5b241|M|487769",
                "__id_mleg__": "2|d8c27379-dd4f-4f58-a92f-687628f5b241|M",
                "_order.priceFormingData.tradedQuantity": 0,
                "_order.transactionDetails.cumulativeQuantity": 0,
                "_order.transactionDetails.priceAverage": 0,
                "_order.__meta_model__": "Order",
                "_orderState.__meta_model__": "OrderState",
                "_orderState.sourceIndex": "4",
                "_order.sourceIndex": "4",
                "__cash__": "CASH",
                "dataSourceName": "TT",
                "__newo_in_file_col__": False,
                "_order.executionDetails.orderStatus": "NEWO",
                "__static_text_m__": "M",
                "transactionDetails.recordType": "Market Side",
                "transactionDetails.settlementAmountCurrency": "USD",
                "transactionDetails.quantityNotation": "UNIT",
                "_orderState.priceFormingData.remainingQuantity": 490.0,
                "__limit_price__": pd.NA,
                "executionDetails.passiveAggressiveIndicator": pd.NA,
                "__option_type__": pd.NA,
                "_order.id": "d8c27379-dd4f-4f58-a92f-687628f5b241|487769",
                "__isin__": pd.NA,
                "orderIdentifiers.tradingVenueTransactionIdCode": "OSNALTNA017sTaI1f-00",
                "priceFormingData.initialQuantity": 502.0,
                "orderIdentifiers.aggregatedOrderId": "d8c27379-dd4f-4f58-a92f-687628f5b241:85:2",
                "_orderState.reportDetails.transactionRefNo": "jM2wcTDfA70cxEOR-XCME-1|9291|d8c27379-dd4f-4f58-a92f",
                "TRADERID": "JBrosterman",
                "CLIENTID": "CAX",
                "__underlying_symbol__": "ZC",
                "transactionDetails.settlementAmount": "0",
                "transactionDetails.priceNotation": "MONE",
                "_orderState.transactionDetails.cumulativeQuantity": 12.0,
                "_orderState.priceFormingData.tradedQuantity": 12.0,
                "_order.priceFormingData.remainingQuantity": 490.0,
                "__derivative_price_multiplier__": pd.NA,
                "transactionDetails.basketId": pd.NA,
                "transactionDetails.netAmount": "0",
                "transactionDetails.commissionAmount": "0",
                "transactionDetails.commissionAmountCurrency": "USD",
                "transactionDetails.commissionAmountType": pd.NA,
                "__raw_order_status__": "1",
                "__symbol__": "ZC",
                "__security_id__": "2502111205808332076",
                "INVESTMENTDECISIONMAKER": pd.NA,
                "COUNTERPARTYID": "JPM",
                "__option_type_mleg__": "",
                "_order.buySell": "SELL",
                "sourceKey": "s3://test.steeleye.co/ingress/raw/order-feed-tt-fix-controller/20240307/019c3df6d6e937f0cd4ba82fa65e5eee6bbba5546c871da9021547c4119d100c_9291.fix",
                "orderIdentifiers.internalOrderIdCode": "1709764327342",
                "__market_segment_id__": pd.NA,
                "transactionDetails.buySellIndicator": "SELL",
                "__final_derived_side__": "2",
                "_orderState.timestamps.orderStatusUpdated": "2024-03-07T18:25:12.000000Z",
                "_order.timestamps.orderStatusUpdated": "2024-03-07T18:25:12.000000Z",
                "timestamps.orderReceived": "2024-03-07T18:25:12.000000Z",
                "_orderState.executionDetails.orderStatus": "PARF",
                "instrumentDetails.instrument": {
                    "sourceKey": "cmeInstruments.XCBT.FCAXSX.20240405.020040",
                    "instrumentClassificationEMIRAssetClass": "CO",
                    "&id": "XCBT|ZCZ4|20241213|USD",
                    "cfiGroup": "Commodities futures",
                    "cfiCategory": "Futures",
                    "commoditiesOrEmissionAllowanceDerivativeInd": True,
                    "instrumentFullName": "XCBT - ZC - Dec-24 - Future",
                    "&key": "VenueDirectInstrument:XCBT|ZCZ4|20241213|USD:1712336495",
                    "notionalCurrency1": "USD",
                    "instrumentClassificationEMIRProductType": "FUT",
                    "instrumentClassificationEMIRContractType": "FU",
                    "cfiAttribute1": "Agriculture",
                    "cfiAttribute2": "Not Applicable/Undefined",
                    "cfiAttribute3": "Standardized",
                    "cfiAttribute4": "Not Applicable/Undefined",
                    "instrumentClassification": "FCAXSX",
                    "ext.venueName": "Chicago Board Of Trade",
                    "ext.mifirEligible": False,
                    "ext.exchangeSymbolLocal": "487769",
                    "ext.emirEligible": True,
                    "ext.bestExAssetClassSub": "Futures and options admitted to trading on a trading venue",
                    "ext.exchangeSymbol": "ZCZ4",
                    "ext.quantityNotation": "UNIT",
                    "ext.bestExAssetClassMain": "Commodities derivatives and emission allowances Derivatives",
                    "ext.alternativeInstrumentIdentifier": "XCBTZCFF2024-12-13 00:00:00",
                    "ext.instrumentUniqueIdentifier": "XCBT|ZCZ4|20241213|USD",
                    "ext.priceNotation": "MONE",
                    "ext.exchangeSymbolRoot": "ZC",
                    "venue.tradingVenue": "XCBT",
                    "venue.financialInstrumentShortName": "ZCZ4",
                    "commodityAndEmissionAllowances.quantityUnitOfMeasure": "BUSHELS",
                    "derivative.expiryDate": "2024-12-13",
                    "derivative.priceDisplayFactor": "1",
                    "derivative.deliveryType": "OPTL",
                    "derivative.isUserDefinedSpread": False,
                    "derivative.priceMultiplier": "5000.000000000",
                },
                "marketIdentifiers.instrument": pd.NA,
                "__order_id__": "d8c27379-dd4f-4f58-a92f-687628f5b241|487769",
            },
        ]
    )
