��C/      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKB��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�ORDERID��	TRADEDATE��ORDERAVERAGEPRICE��ORDEREXECQTY��ORDERTARGETQTY��	ACCOUNTID��ALLOCATIONBROKERREASON��ALLOCATIONCLEARINGBROKER��ALLOCATIONEXECUTINGBROKER��INVESTMENTDECISIONMAKER��ORIGINALORDERID��TRADEALLOCID��__ORDER_STATUS__��
BROKERCODE��
BROKERNAME��CRDINTERNALSECURITYID��CURRENCYCODE��EXCHANGECODE��IPO��ISIN��MANAGERLIMIT��NETTRADEIND��ORDERCREATEDATE��ORDEREXECUTINGBROKER��ORDERINSTRUCTION��ORDERLASTUPDATEDATE��ORDERMANAGERID��ORDERREASON��ORDERRELEASEDATE��ORDERSTATUS��
ORDERTRADERID��SECURITYNAME��SECURITYTYPE��SIDE��SPECIALINST��TICKER��TIMEZONENAME��TIMEZONEREGIONNAME��EXECDECISIONMAKER��MIC��
PLACEBROKERID��PLACECREATEDATE��	PLACEDATE��PLACEID��
__FILE_TYPE__��__SOURCE_FILE__��FILLDATE��FILLQTY��	FILLPRICE��FILLCREATEDATE��ALGOSTRATEGY��PLACEMENTID��FIXEXECUTIONID��ALLOCATIONDIRECTEDBROKER��ALLOCATIONDIRECTEDBROKERTYPE��FROMCURRENCYCODE��PLACEFIXCLIENTORDERID��PLACEREASEON��	STOPPRICE��TIMEINFORCE��TOCURRENCYCODE��ORDERNETAMOUNT��
FILLAMOUNT��
LASTMARKET��__ACCOUNT_IDS__��	QUANT_IND�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(h�numpy.core.numeric��_frombuffer���(�(                                           �h�i8�����R�(K�<�NNNJ����J����K t�bK���C�t�R�hh�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�
1570246491�h��
1570246990�h��
1570251074�et�b�_dtype�h��StringDtype���)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�2023-03-06 00:00:00.000�h��2023-03-07 00:00:00.000�h�h�et�bh�h�)��ubhq(��       �MbX�X@�MbX�X@tSVqA@tSVqA@��A<؞T@    �CA    �CA    �j�@    �j�@     ��@    �CA    �CA    �j�@    �j�@     ��@      �      �      �      �      �      �      �      �      �      ��h�f8�����R�(KhvNNNJ����J����K t�bKK��hyt�R�h�)��}�(h�hhK ��h��R�(KK��h��]�(�ALABAMA�h��CGF_CEE�h��
IUF_CONDOR�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�pandas._libs.missing��NA���h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�SOGEPAFI�hҌJPMALGL�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�RNL0823�h݌ROB3771�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�
1570246491�h�
1570246990�h�
1570251074�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�26792764�h�26792766�h��26790682�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�NEWO��FILL�j  j   j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�SOGEPAFI�j  �JPMALGL�j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�0Societe Generale Paris - Bonds & OTC derivatives�j  �JP Morgan Algo Global�j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�59268321�j!  �10797420�j"  �10794964�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�EUR�j-  j-  j-  �USD�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�EMUO�j8  �XET�j9  �NYS�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�N�jD  jD  jD  jD  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�DE0001102333�jN  �DE0006231004�jO  �US1266501006�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�P�jc  �A�jd  jd  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�2023-03-06 16:27:44.313�jn  �2023-03-07 11:24:27.503�jo  �2023-03-06 10:52:48.397�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�SOGEPAFI�jz  �JPMALGL�j{  j{  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�2023-03-06 23:32:42.480�j�  �2023-03-07 23:32:52.190�j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�RCA2003�j�  �ROB6593�j�  �IBC3084�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�
F_2_ADJPFO�j�  �	EQ_TARGET�j�  �EQ_MDL�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�2023-03-06 16:37:08.173�j�  �2023-03-07 11:25:03.040�j�  �2023-03-06 12:07:23.697�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�ACCT�j�  j�  j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�RCA5003�j�  �GEN_TRD_AUTO�j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�*1.750 BUNDESREPUB. DEUTSCHLAND 15-FEB-2024�j�  �Infineon Technologies AG�j�  �CVS Health Corp�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�FIXB�j�  �COM�j�  j�  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�BUYL�j�  j�  j�  �SELLL�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�DBR�j�  �IFX�j�  �CVS�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�Europe/Brussels�j	  �
Europe/Berlin�j
  �America/New_York�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�Belgium�j  �Germany�j  �United States Eastern Time�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�RCA5003�j!  �GEN_TRD_AUTO�j"  j"  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h��XETR�j,  �XNYS�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�SOGEPAFI�j7  �JPMALGL�j8  j8  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�2023-03-06 16:45:51.430�jB  �2023-03-07 16:01:53.300�jC  �2023-03-06 12:07:23.737�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�2023-03-06 16:45:51.383�jN  �2023-03-07 11:25:03.280�jO  �2023-03-06 06:07:27.423�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�
1571056207�jZ  �
1571201956�j[  �
1570936606�et�bh�h�)��ubhhK ��h��R�(KKK��h!�]�(�order�jd  jd  jd  jd  �IEXD�je  je  �AQEA�je  h��Y��y�jD  �n�et�bh�)��}�(h�hhK ��h��R�(KK��h��]�(�ROBECO_CRIMS_20230307�jr  jr  jr  jr  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�et�bh�h�)��ubhq(�P       �       �       �       �       �       d       d       d       d       d       �h�i8�����R�(KhvNNNJ����J����K t�bKK��hyt�R�h�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�et�bh�h�)��ube]�(h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h'h(h)hThUet�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h+at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h,at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h0at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h1at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h9at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h:at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h<at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h=at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h?at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h@at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hBat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hCat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hDat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hEat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hFat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hGat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hHat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hIat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hJat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hKat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hLat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hMat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hNat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hOat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hPat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hQhdhfet�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hRat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hSat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hVat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hWat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hXat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hYat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hZat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h[at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h\at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h]at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h^at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h_at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h`at�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�haat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hbhcet�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�heat�bhhNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���K KK��R�u}�(j  h�j  j  KKK��R�u}�(j  h�j  hq(�(                            /       0       �h�i8�����R�(KhvNNNJ����J����K t�bK��hyt�R�u}�(j  h�j  j  KKK��R�u}�(j  h�j  j  KKK��R�u}�(j  h�j  j  KKK��R�u}�(j  h�j  j  KK	K��R�u}�(j  h�j  j  K	K
K��R�u}�(j  h�j  j  K
KK��R�u}�(j  h�j  j  KKK��R�u}�(j  h�j  j  KK
K��R�u}�(j  j  j  j  K
KK��R�u}�(j  j  j  j  KKK��R�u}�(j  j  j  j  KKK��R�u}�(j  j&  j  j  KKK��R�u}�(j  j1  j  j  KKK��R�u}�(j  j=  j  j  KKK��R�u}�(j  jG  j  j  KKK��R�u}�(j  jS  j  j  KKK��R�u}�(j  j\  j  j  KKK��R�u}�(j  jg  j  j  KKK��R�u}�(j  js  j  j  KKK��R�u}�(j  j~  j  j  KKK��R�u}�(j  j�  j  j  KKK��R�u}�(j  j�  j  j  KKK��R�u}�(j  j�  j  j  KKK��R�u}�(j  j�  j  j  KKK��R�u}�(j  j�  j  j  KKK��R�u}�(j  j�  j  j  KKK��R�u}�(j  j�  j  j  KK K��R�u}�(j  j�  j  j  K K!K��R�u}�(j  j�  j  j  K!K"K��R�u}�(j  j�  j  j  K"K#K��R�u}�(j  j�  j  j  K#K$K��R�u}�(j  j  j  j  K$K%K��R�u}�(j  j  j  j  K%K&K��R�u}�(j  j  j  j  K&K'K��R�u}�(j  j%  j  j  K'K(K��R�u}�(j  j0  j  j  K(K)K��R�u}�(j  j;  j  j  K)K*K��R�u}�(j  jG  j  j  K*K+K��R�u}�(j  jS  j  j  K+K,K��R�u}�(j  ja  j  hq(�       ,       ?       A       �j$  K��hyt�R�u}�(j  jk  j  j  K-K.K��R�u}�(j  ju  j  j  K.K/K��R�u}�(j  j~  j  j  K1K2K��R�u}�(j  j�  j  j  K2K3K��R�u}�(j  j�  j  j  K3K4K��R�u}�(j  j�  j  j  K4K5K��R�u}�(j  j�  j  j  K5K6K��R�u}�(j  j�  j  j  K6K7K��R�u}�(j  j�  j  j  K7K8K��R�u}�(j  j�  j  j  K8K9K��R�u}�(j  j�  j  j  K9K:K��R�u}�(j  j�  j  j  K:K;K��R�u}�(j  j�  j  j  K;K<K��R�u}�(j  j�  j  j  K<K=K��R�u}�(j  j�  j  j  K=K?K��R�u}�(j  j�  j  j  K@KAK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.