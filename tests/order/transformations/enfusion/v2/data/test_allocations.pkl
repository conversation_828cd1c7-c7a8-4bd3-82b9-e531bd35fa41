��N!      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK���h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�LENAME��TRANSACTIONTYPE��REPORTSTATUS��TRANSACTIONREF<PERSON>ENCENUMBER��!EXECUTIONENTI<PERSON>IDENTI<PERSON>CATIONCODE��"INVESTMENTFIRMCONVEREDBY2014/65/EU��BUYERIDENTIFICATIONCODETYPE��BUYERIDENTIFICATIONCODE��COUNTRYOFTHEBRANCHFORTHEBUYER��BUYERDECISIONMAKERCODETYPE��BUYERDECISIONMAKERCODE��SELLERIDENTIFICATIONCODETYPE��SELLERIDENTIFICATIONCODE��COUNTRYOFTHEBRANCHFORTHESELLER��SELLERDECISIONMAKERCODETYPE��SELLERDECISIONMAKERCODE��TRANSMISSIONOFORDERINDICATOR��TRADINGDATETIME��TRADINGCAPACITY��QUANTITYTYPE��QUANTITY��
PRICE-TYPE��PRICE��
PRICECURRENCY��VENUE��INSTRUMENTIDENTIFICATIONCODE��INSTRUMENTFULLNAME��INSTRUMENTCLASSIFICATION��NOTIONALCURRENCY1��NOTIONALCURRENCY2��PRICEMULTIPLIER��UNDERLYINGINSTRUMENTCODE��UNDERLYINGINDEXNAME��TERMOFUNDERLYINGINDEX-VALUE��
EXPIRYDATE��DELIVERYTYPE��INVESTMENTDECISIONWITHFIRM-TYPE��DCOUNTRYOFTHEBRANCHRESPONSIBL<PERSON>OR<PERSON><PERSON>ERSONMAKING<PERSON>EINVESTMENTDECISION��EXECUTIONWITHINFIRM-TYPE��@COUNTRYOFTHEBRANCHSUPERVISINGTHEPERSONRESPONSIBLEFORTHEEXECUTION��SHORTSELLINGINDICATOR��COMMODITYDERIVATIVEINDICATOR��'SECURITIESFINANCINGTRANSACTIONINDICATOR��TRANSACTIONTYPE.1��	ORDERDATE��ORDERID��ORDERLIMITPRICE��ORDERSTATUS��	ORDERSIDE��ORDERTIMEINFORCE��ORDERTOTALQUANTITY��	ORDERTYPE��
PARENTORDERID��<PERSON>ADER��PORTFOLIOMANAGER��ORDERREMAININGQUANTITY��ORDERCUMULATIVEQUANTITY��OPTIONTYPE.1��FUTUREEXPIRATIONDATE��	CURRENCY2��COMMISSIONS��COMMISSIONTYPE��FILLER/NETAMOUNT��EXCHANGEMICCODE��EXECUTIONORDERTYPE��PARENTORDERREMAININGQUANTITY��FUTUREBLOOMBERGROOT��LASTFILLTIME��COUNTERPARTY��
TRADECANCELED��SENDTOSTEELEYEELIGIBLE��ORDERTIMESTAMP��EXCHANGESHORTNAME��RIC��OSI��	EVENTTYPE��ORDERREFERENCE��
EXECUTIONTIME��ISIN��ORDEREXECUTIONDESTINATION��CUSIP��SEDOL��
EXECUTIONDATE��CURRENCY��EXECID��LASTPX��ORDERSTATUSTEXT��DESCRIPTION��TICKER��LASTQTY��BRANCHLOCATION��BUYER-DATEOFBIRTH��BUYER-FIRSTNAME(S)��BUYER-SURNAME(S)��BUYERDECISIONMAKER-DATEOFBIRTH��BUYERDECISIONMAKER-FIRSTNAME(S)��BUYERDECISIONMAKER-SURNAME(S)��BUYERDECISIONMAKERNPCODE��BUYERNPCODE��COMPLEXTRADECOMPONENTID��COUNTRYOFBRANCHMEMBERSHIP��#DERIVATIVENOTIONALINCREASE/DECREASE��EXECUTIONWITHINFIRM��EXECUTIONWITHINFIRMNPCODE��INSTRUCTIONS��INVESTMENTDECISIONWITHFIRM��"INVESTMENTDECISIONWITHINFIRMNPCODE��LIFECYCLEEVENT��MATURITYDATE��	NETAMOUNT��OPTIONCONTRACTBLOOMBERGROOTCODE��OPTIONEXERCISESTYLE��OPTIONEXPIRATIONDATE��OPTIONSTRIKE��
OPTIONTYPE��ORDERSTOPPRICE��OTCPOST-TRADEINDICATOR��QUANTITYCURRENCY��SELLER-DATEOFBIRTH��SELLER-FIRSTNAME(S)��SELLER-SURNAME(S)��SELLERDECISIONMAKER-DATEOFBIRTH�� SELLERDECISIONMAKER-FIRSTNAME(S)��SELLERDECISIONMAKER-SURNAME(S)��SELLERDECISIONMAKERNPCODE��SELLERNPCODE��SENDTOSTEELEYE��STRIKEPRICE��STRIKEPRICECURRENCY��STRIKEPRICETYPE��)TRADINGVENUETRANSACTIONIDENTIFICATIONCODE��-TRANSMITTINGFIRMIDENTIFICATIONCODEFORTHEBUYER��.TRANSMITTINGFIRMIDENTIFICATIONCODEFORTHESELLER��UP-FRONTPAYMENT��UP-FRONTPAYMENTCURRENCY��WAIVEINDICATOR��__record_type__��__order_status__��	PROGRAMID��BBYELLOWKEY�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(h�N�start�K �stop�K�step�Ku��R�e]�(hhK ��h��R�(KK�K��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�FIFTHDELTA Master Fund Ltd�h�hȌSell��
BuyToCover�hɌNEWT�h�hˌ	249581833��	249618392�ȟ2138002EE8DT72X2QN40�h�hΌtrue�h�hόLEI�h�hЌUBSX��2138002EE8DT72X2QN40�h�G�      �Unknown�G�      G�      �LEI�G�      G�      �2138002EE8DT72X2QN40�G�      �LEI�h�h֌2138002EE8DT72X2QN40��UBSD�h׌Unknown�G�      hٌLEI�G�      hڌ2138002EE8DT72X2QN40�G�      hیtrue�h�h܌2022-05-27T13:41:25.000000Z��2022-05-27T00:00:00.000000Z�h݌AOTC�h�hߌUNIT�h�h�G@�2     G@@�     G@�2     �MONE�h�h�G@ �	k��G@�.�q�jG@ �	k���USD�h�h�XOFF�h�h�US00835Q1031�G�      h�G�      �EMINI S&P JUN2�G�      G�      �FFICSX�G�      G�      �USD�G�      G�      �USD�G�      G�      G@I      G�      G�      �US78378X1072�G�      G�      �
S&P 500 INDEX�G�      G�      �3MNTH�G�      G�      �
2022-06-17�G�      G�      �CASH�G�      �CCPT�h�h�Unknown�h�h�CCPT�h�h��Unknown�h�h�SELL�G�      h�false�h�h�false�h�h�
2022-05-26�G�      h��17591573�h�h�G        G�      G        �Filled�G�      h��Sell�G�      h��GTC�G�      h�G@��     G�      G@��     �
Electronic�G�      h��17591572�G�      h��
Tyson Appadoo�G�      h��Niall O'Keeffe�G�      h�G        G�      G        �98000.00000000�G�      h�G�      G�      G�      G�      �
2022-06-17�G�      �USD�j   j   G?pbM���G?�      G?pbM����Commission (Cents Per Share)��Commission (Per Share)�j  G@��y����G@M�33333G@��y�����XNYS��XCME��XOSE��Market�G�      j  �
0.00000000�G�      j  �pandas._libs.missing��NA���j
  j
  �20220527-13:41:25.000�G�      j  �EMSX UBS LT��EMSX UBS FUT ALGO�j  �false�j  j  �true�j  j  �03:15:28.000�G�      j  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  �NKM24��1224�j  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  G�      G�      j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  �allocation_record��
market_record�j  �PARF��NEWO�j  �!PROG-202310051659191672474-665764�j
  j  G�      G�      �NKY 04/11/25 P36500 Index�et�b�numpy.core.numeric��_frombuffer���(��             �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      ��h�f8�����R�(K�<�NNNJ����J����K t�bKK���C�t�R�e]�(h
h}�(hhhK ��h��R�(KK���h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3h4h5h6h7h8h9h:h;h<h=h>h?h@hAhBhChDhEhFhGhHhIhJhKhLhMhNhOhQhRhShThUhVhWhXhYhZh[h\h]h^h_h`hahbhchdhehfhghhhihjhkhlhmhnhohphqhrhshthuhvhwhxhyh{h|h}hh�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hPhzh~h�h�h�et�bh�Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs�j  (�0                                                                      	       
                     
                                                                                                                                             !       "       #       $       %       &       '       (       )       *       ,       -       .       /       0       1       2       3       4       5       6       7       8       9       :       ;       <       =       >       ?       @       A       B       C       D       E       F       G       H       I       J       K       L       M       N       O       P       Q       R       S       T       V       W       X       Z       [       \       ]       ^       _       `       a       b       c       d       e       f       g       h       i       j       k       l       m       n       o       p       r       t       u       v       w       x       y       z       {       |       }       ~       �       �       �       �       �       �       �       �       �       �       �       �       �h�i8�����R�(Kj!  NNNJ����J����K t�bK���j$  t�R�u}�(jA  j&  jB  j  (�0       +       U       Y       q       s              �jF  K��j$  t�R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.