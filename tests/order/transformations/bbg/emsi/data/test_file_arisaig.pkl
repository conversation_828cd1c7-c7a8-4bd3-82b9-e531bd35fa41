��_U      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK;��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�Activity��Status (Tag 39)��Type��Receive Date Time��As Of Date Time (Tag 60)��
Side (Tag 54)��Quantity (Tag 53)��Fill Quantity (Tag 32)��Ticker (Tag 55)��Order Type (Tag 40)��TIF (Tag59)��Execution Price (Tag 31)��Average Price (Tag 6)��Limit Price (Tag 44)��Broker��Order ID��Route ID��Fill ID��Account (Tag 1)��Trader UUID��Instruction (Tag 58)��Product��Liquidity (Tag 851)��Last Capacity (Tag 29)��Last Market (Tag 30)��Transaction Reporting MIC��Price Currency��Trader Name��FIGI��Contract Expiration��ISIN��Local Exch Symbol��Last Market��Parsekey��
Security Name��__last_fill_receive_date_time__��__last_fill_quantity__��__last_fill_price__��__last_fill_filled_quantity__��__last_fill_fill_id__��__last_fill_limit_price__��__quantity_newo__��__order_status__��__external_order_received__��__internal_order_submitted__��__order_submitted__��__order_received__��__trading_date_time__��__order_status_updated__��Buyside LEI��PM Name��Tran Account��Order #��Full Exch Symbol��GTD Date (Tag 126)��
OCC_Symbol��Originator LEI��Stop Price (Tag 99)��
Strategy Name�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C�                                                                	       
                     
                                                                                    �t�bha�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h!�]�(�	New-Route��
Exec-Trade��Exec-Replace�h�h��	Exec-Done�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��Exec-Unmatched-Replace�h�h�h�et�b�_dtype�hw�StringDtype���)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�Sent��Part-filled��Replaced�h�h��Cancel�h��Filled�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�R�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�04/09/2024 11:10:36.702000��04/09/2024 11:45:23.535000��04/09/2024 13:04:34.068000��04/09/2024 13:09:14.925000��04/09/2024 17:05:51.534000��04/09/2024 18:13:37.060000��04/09/2024 17:08:38.868000��04/09/2024 17:11:56.811000��04/09/2024 11:10:25.812000��04/09/2024 11:45:23.539000��04/09/2024 13:04:41.574000��04/09/2024 13:09:20.278000��04/09/2024 17:05:57.060000��04/09/2024 18:13:37.064000��04/09/2024 17:08:52.959000��04/09/2024 17:12:31.431000��04/09/2024 08:57:21.380000��04/09/2024 12:36:01.164000��04/09/2024 16:48:45.388000��04/09/2024 08:58:52.722000��04/09/2024 09:32:37.612000��04/09/2024 10:52:02.098000��04/09/2024 00:21:30.394000��04/09/2024 18:16:38.765000��04/09/2024 21:30:01.628000�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�04/09/2024 11:10:36.701000��04/09/2024 11:45:23.000000��04/09/2024 13:04:33.000000��04/09/2024 13:09:14.000000��04/09/2024 17:05:51.000000��04/09/2024 18:13:36.000000��04/09/2024 17:08:38.841000��04/09/2024 17:11:56.000000��04/09/2024 11:10:25.809000�hΌ04/09/2024 13:04:41.000000��04/09/2024 13:09:20.000000��04/09/2024 17:05:56.000000�hҌ04/09/2024 17:08:52.958000��04/09/2024 17:12:31.000000��04/09/2024 08:57:21.375000��04/09/2024 12:35:59.000000��04/09/2024 16:48:45.269000��04/09/2024 08:58:52.719000��04/09/2024 09:32:37.470000��04/09/2024 10:52:01.967000��04/09/2024 00:21:30.000000��04/09/2024 18:16:38.762000��04/09/2024 21:30:01.000000�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�BUY�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�SELL�h�h�h�h�h�h�h�h�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�GOCOLORS IN Equity�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��COLG PK Equity�h�h��600298 C1 Equity�h�h��IH US Equity�h�h�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�LMT�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�DAY�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�1200.0�j  j  j  j  j  �1185.0�j  j  j  j  j  j  j  j  j  �1330.0�j  j  �29.0�j  j  �1.65�j  j  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�SPAK�j'  j'  j'  j'  j'  �INVB�j(  j'  j'  j'  j'  j'  j'  j(  j(  �CG�j)  j)  �CICF�j*  j*  �UBSW�j+  j+  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�O.EMS.55286.6118491.20240326�j5  j5  j5  j5  j5  j5  j5  �O.EMS.55286.6118498.20240326�j6  j6  j6  j6  j6  j6  j6  �O.EMS.55286.6118511.20240326�j7  j7  �O.EMS.55286.6118512.20240326�j8  j8  �O.EMS.55286.6118545.20240327�j9  j9  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(� R.EMS.55286.1712632236-277872655�jC  jC  jC  jC  jC  � R.EMS.55286.1712653718-277872668�jD  � R.EMS.55286.1712632225-277872671�jE  jE  jE  jE  jE  � R.EMS.55286.1712653732-277872672�jF  � R.EMS.55286.1712624241-277872650�jG  jG  � R.EMS.55286.1712624332-277872645�jH  jH  � R.EMS.55286.1712571198-277872650�� R.EMS.55286.1712657798-277872657�jJ  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�pandas._libs.missing��NA����"F.EMS.55286.1712632236-277872655.4�jV  jV  jV  jV  jV  �"F.EMS.55286.1712653718-277872668.3�jV  �"F.EMS.55286.1712632225-277872671.4�jV  jV  jV  jV  jV  �"F.EMS.55286.1712653732-277872672.3�jV  �"F.EMS.55286.1712624241-277872650.3�jV  jV  �"F.EMS.55286.1712624332-277872645.3�jV  �#F.EMS.55286.1712571198-277872650.39�jV  �"F.EMS.55286.1712657798-277872657.3�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�AAF��AAF��AAF��AAF��AAF��AAF��AAF��AAF��MFV3��MFV3��MFV3��MFV3��MFV3��MFV3��MFV3��MFV3��AIF��AIF��AIF��AAF��AAF��AAF��AAF��AAF��AAF�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�3423113�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(jV  jV  jV  jV  jV  jV  �blk�j�  jV  jV  jV  jV  jV  jV  j�  j�  jV  jV  jV  jV  jV  jV  jV  jV  jV  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�Equity�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  �Routed�jV  �Maker�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  �A�jV  jV  j�  jV  j�  jV  j�  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(jV  �NS�jV  jV  jV  jV  jV  �XNSE�jV  j�  jV  jV  jV  jV  jV  j�  jV  �0�jV  jV  �SH�jV  �MEMX�jV  �INCR�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(jV  jV  jV  jV  jV  jV  jV  �XOFF�jV  jV  jV  jV  jV  jV  jV  j�  jV  j�  jV  jV  jV  jV  jV  jV  jV  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�INR�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �PKR�j�  j�  �CNY�j�  j�  �USD�j�  j�  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�
STEPHANIE TER�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�BBG0080S62L6�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �BBG000GMSB99�j�  j�  �BBG00709HLR0�j�  j�  �BBG00X71MSB9�j�  j�  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�00/00/00�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�INE0BJS01011�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �PK0028001011�j  j  �CNE0000014G0�j  j  �US45175B1098�j  j  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  �COLG�j  j  �600298�j  j  �IH�j  j  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�NS�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j�  j�  j�  �SH�j  j  �MEMX�j  j  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�GOCOLORS IN Equity�j)  j)  j)  j)  j)  j)  j)  j)  j)  j)  j)  j)  j)  j)  j)  �COLG PK Equity�j*  j*  �600298 C1 Equity�j+  j+  �IH US Equity�j,  j,  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�GO FASHION INDIA�j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  j6  �COLGATE PALM PAK�j7  j7  �ANGEL YEAST CO-A�j8  j8  �
IHUMAN INC�j9  j9  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�04/09/2024 18:10:43.318000�jC  jC  jC  jC  jC  �04/09/2024 17:11:56.811000�jD  �04/09/2024 18:10:43.322000�jE  jE  jE  jE  jE  �04/09/2024 17:12:31.431000�jF  �04/09/2024 16:28:21.773000�jG  jG  �04/09/2024 14:54:31.341000�jH  jH  �04/09/2024 02:29:21.726000��04/09/2024 22:27:57.460000�jJ  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�%F.EMS.55286.1712632236-277872655.2655�jT  jT  jT  jT  jT  �"F.EMS.55286.1712653718-277872668.3�jU  �%F.EMS.55286.1712632225-277872671.3895�jV  jV  jV  jV  jV  �"F.EMS.55286.1712653732-277872672.3�jW  �$F.EMS.55286.1712624241-277872650.133�jX  jX  �%F.EMS.55286.1712624332-277872645.1339�jY  jY  �#F.EMS.55286.1712571198-277872650.72��#F.EMS.55286.1712657798-277872657.45�j[  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�6780.0�je  je  je  je  je  �29836.0�jf  �18220.0�jg  jg  jg  jg  jg  �80164.0�jh  �30000.0�ji  ji  �400000.0�jj  jj  jV  �200000.0�jk  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�NEWO��PARF��REME�jw  jw  �CAME�ju  �FILL�ju  jv  jw  jw  jw  jx  ju  jy  ju  jv  jx  ju  jy  jw  jv  ju  jv  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�04/09/2024 11:10:47.975000�jV  �04/09/2024 13:04:24.459000��04/09/2024 13:08:52.548000��04/09/2024 17:05:24.003000�jV  �04/09/2024 17:08:39.032000�jV  �04/09/2024 11:10:47.978000�jV  �04/09/2024 13:04:35.796000��04/09/2024 13:09:03.934000��04/09/2024 17:05:37.067000�jV  �04/09/2024 17:08:53.068000�jV  �04/09/2024 08:58:08.332000�jV  jV  �04/09/2024 08:58:55.176000�jV  �04/09/2024 10:51:53.498000�jV  �04/09/2024 18:18:08.007000�jV  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�04/09/2024 11:10:36.702000�jV  �04/09/2024 13:04:24.249000��04/09/2024 13:08:52.340000��04/09/2024 17:05:23.796000�jV  �04/09/2024 17:08:38.868000�jV  �04/09/2024 11:10:25.812000�jV  �04/09/2024 13:04:35.586000��04/09/2024 13:09:03.722000��04/09/2024 17:05:36.859000�jV  �04/09/2024 17:08:52.959000�jV  �04/09/2024 08:57:21.380000�jV  jV  �04/09/2024 08:58:52.722000�jV  �04/09/2024 10:51:53.228000�jV  �04/09/2024 18:16:38.765000�jV  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�04/09/2024 11:10:47.975000�j�  �04/09/2024 13:04:34.068000��04/09/2024 13:09:14.925000��04/09/2024 17:05:51.534000��04/09/2024 18:13:37.060000��04/09/2024 17:08:39.032000�j�  �04/09/2024 11:10:47.978000�j�  �04/09/2024 13:04:41.574000��04/09/2024 13:09:20.278000��04/09/2024 17:05:57.060000��04/09/2024 18:13:37.064000��04/09/2024 17:08:53.068000�j�  �04/09/2024 08:58:08.332000�j�  �04/09/2024 16:48:45.388000��04/09/2024 08:58:55.176000�j�  �04/09/2024 10:52:02.098000��04/09/2024 00:21:30.394000��04/09/2024 18:18:08.007000�j�  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�04/09/2024 11:10:36.719000�j�  �04/09/2024 13:04:34.068000��04/09/2024 13:09:14.925000��04/09/2024 17:05:51.534000��04/09/2024 18:13:37.060000��04/09/2024 17:08:38.886000�j�  �04/09/2024 11:10:25.850000�j�  �04/09/2024 13:04:41.574000��04/09/2024 13:09:20.278000��04/09/2024 17:05:57.060000��04/09/2024 18:13:37.064000��04/09/2024 17:08:52.974000�j�  �04/09/2024 08:57:21.422000�j�  �04/09/2024 16:48:45.388000��04/09/2024 08:58:52.744000�j�  �04/09/2024 10:52:02.098000��04/09/2024 00:21:30.394000��04/09/2024 18:16:38.805000�j�  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�04/09/2024 11:10:36.702000��04/09/2024 11:45:23.535000��04/09/2024 13:04:34.068000��04/09/2024 13:09:14.925000��04/09/2024 17:05:51.534000��04/09/2024 18:13:37.060000��04/09/2024 17:11:56.811000�j�  �04/09/2024 11:10:25.812000��04/09/2024 11:45:23.539000��04/09/2024 13:04:41.574000��04/09/2024 13:09:20.278000��04/09/2024 17:05:57.060000��04/09/2024 18:13:37.064000��04/09/2024 17:12:31.431000�j�  �04/09/2024 08:57:21.380000��04/09/2024 12:36:01.164000��04/09/2024 16:48:45.388000��04/09/2024 14:54:31.341000��04/09/2024 09:32:37.612000��04/09/2024 10:52:02.098000��04/09/2024 00:21:30.394000��04/09/2024 18:16:38.765000��04/09/2024 21:30:01.628000�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(�04/09/2024 11:10:36.702000�j  �04/09/2024 13:04:34.068000��04/09/2024 13:09:14.925000��04/09/2024 17:05:51.534000��04/09/2024 18:13:37.060000��04/09/2024 17:08:38.868000�j  �04/09/2024 11:10:25.812000�j
  �04/09/2024 13:04:41.574000��04/09/2024 13:09:20.278000��04/09/2024 17:05:57.060000��04/09/2024 18:13:37.064000��04/09/2024 17:08:52.959000�j  �04/09/2024 08:57:21.380000�j  �04/09/2024 16:48:45.388000��04/09/2024 08:58:52.722000�j  �04/09/2024 10:52:02.098000�jV  �04/09/2024 18:16:38.765000�j  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KK��h!�]�(jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  jV  et�bh�h�)��ubhhK ��h��R�(KKK��h�f8�����R�(KhoNNNJ����J����K t�b�B�       |�@     |�@    ���@    ���@    ���@     ��@     #�@     #�@     ��@     ��@    �=�@    @��@    ���@    p�@    @��@    @��@     L�@     L�@     Y�@     jA     jA    ��A     jA     jA     jA    ���@    ���@    ���@    ���@    ���@    ���@     #�@     #�@    ���@    ���@    ���@    ���@    ���@    ���@    @��@    @��@     L�@     L�@     L�@    ��A    ��A    ��A     jA     jA     jA�
O�T��@�
O�T��@�
O�T��@�
O�T��@�
O�T��@�
O�T��@     ��@     ��@�
O�T��@�
O�T��@�
O�T��@�
O�T��@�
O�T��@�
O�T��@     ��@     ��@�f��*Ȕ@�f��*Ȕ@�f��*Ȕ@�@J��=@�@J��=@�@J��=@�/�$�?+��ݓ��?+��ݓ��?     ��@     ��@     ��@     ��@     ��@     ��@     #�@     #�@    p�@    p�@    p�@    p�@    p�@    p�@    @��@    @��@      4@      4@      4@      �@      �@      �@     �O@      Y@      Y@     ��@     ��@     ��@     ��@     ��@     ��@     ��@     ��@     ��@     ��@     ��@     ��@     ��@     ��@     ��@     ��@     Ȕ@     Ȕ@     Ȕ@      =@      =@      =@ffffff�?ffffff�?ffffff�?      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      ��t�bhhK ��h��R�(KKK��h!�]�(jV  G@��     jV  jV  jV  jV  jV  G@�#     jV  GA�    jV  jV  jV  jV  jV  G@�@    jV  G@�Y     jV  jV  GA��    jV  G@�|     jV  G@��     jV  G@��H
�
jV  jV  jV  jV  jV  G@��     jV  G@��Yh��QjV  jV  jV  jV  jV  G@��     jV  G@��*���jV  jV  G@=��J@�jV  G?�AZ ��jV  G?�������jV  G@��33333G@��)ᰉ�G@����J�G@�����Y�G@��T�O
�jV  G@��     jV  G@��33333G@��
���EG@�����G@���ۋ�qG@��T�O
�jV  G@��     jV  G@��     G@��*��f�jV  G@=      G@=��!��G?����l�jV  G?��\(�et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h(at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h)at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h9at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h:at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h<at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h=at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h?at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h@at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hBat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hCat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hDat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hEat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hFat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hGat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hHat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hLat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hNat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hOat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hPat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hQat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hRat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hSat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hTat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hUat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hVat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hWat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hXat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hYat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hZat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h[at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h\at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h]at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h_at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h+hIhJhKhMh^et�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h,h0h1et�bhaNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hz�mgr_locs��builtins��slice���K KK��R�u}�(jW  h�jX  j[  KKK��R�u}�(jW  h�jX  j[  KKK��R�u}�(jW  h�jX  j[  KKK��R�u}�(jW  h�jX  j[  KKK��R�u}�(jW  h�jX  j[  KKK��R�u}�(jW  h�jX  j[  KK	K��R�u}�(jW  h�jX  j[  K	K
K��R�u}�(jW  j  jX  j[  K
KK��R�u}�(jW  j  jX  j[  K
KK��R�u}�(jW  j   jX  j[  KKK��R�u}�(jW  j.  jX  j[  KKK��R�u}�(jW  j<  jX  j[  KKK��R�u}�(jW  jM  jX  j[  KKK��R�u}�(jW  ja  jX  j[  KKK��R�u}�(jW  j�  jX  j[  KKK��R�u}�(jW  j�  jX  j[  KKK��R�u}�(jW  j�  jX  j[  KKK��R�u}�(jW  j�  jX  j[  KKK��R�u}�(jW  j�  jX  j[  KKK��R�u}�(jW  j�  jX  j[  KKK��R�u}�(jW  j�  jX  j[  KKK��R�u}�(jW  j�  jX  j[  KKK��R�u}�(jW  j�  jX  j[  KKK��R�u}�(jW  j�  jX  j[  KKK��R�u}�(jW  j�  jX  j[  KKK��R�u}�(jW  j�  jX  j[  KKK��R�u}�(jW  j
  jX  j[  KK K��R�u}�(jW  j  jX  j[  K K!K��R�u}�(jW  j"  jX  j[  K!K"K��R�u}�(jW  j/  jX  j[  K"K#K��R�u}�(jW  j<  jX  j[  K#K$K��R�u}�(jW  jM  jX  j[  K'K(K��R�u}�(jW  j^  jX  j[  K)K*K��R�u}�(jW  jn  jX  j[  K*K+K��R�u}�(jW  j|  jX  j[  K+K,K��R�u}�(jW  j�  jX  j[  K,K-K��R�u}�(jW  j�  jX  j[  K-K.K��R�u}�(jW  j�  jX  j[  K.K/K��R�u}�(jW  j�  jX  j[  K/K0K��R�u}�(jW  j   jX  j[  K0K1K��R�u}�(jW  j  jX  j[  K1K2K��R�u}�(jW  j#  jX  j[  K2K3K��R�u}�(jW  j,  jX  j[  K3K4K��R�u}�(jW  j5  jX  j[  K4K5K��R�u}�(jW  j>  jX  j[  K5K6K��R�u}�(jW  jG  jX  j[  K6K7K��R�u}�(jW  jP  jX  j[  K7K8K��R�u}�(jW  jY  jX  j[  K8K9K��R�u}�(jW  jb  jX  j[  K:K;K��R�u}�(jW  jm  jX  hhK ��h��R�(KK��hn�C0       $       %       &       (       9       �t�bu}�(jW  jw  jX  hhK ��h��R�(KK��hn�C                     �t�bueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.