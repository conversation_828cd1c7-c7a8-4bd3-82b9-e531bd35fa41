���7      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKa��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��(_orderState.executionDetails.orderStatus��#_order.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo�� executionDetails.routingStrategy��&executionDetails.shortSellingIndicator��executionDetails.stopPrice�� executionDetails.tradingCapacity��executionDetails.validityPeriod��	hierarchy��	_order.id��_orderState.id��_order.__meta_model__��_orderState.__meta_model__��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��orderIdentifiers.parentOrderId��!orderIdentifiers.orderRoutingCode�� priceFormingData.initialQuantity��!priceFormingData.modifiedQuantity��"_orderState.priceFormingData.price��"priceFormingData.remainingQuantity��+_orderState.priceFormingData.tradedQuantity��sourceIndex��	sourceKey��-_orderState.orderIdentifiers.transactionRefNo��*_orderState.reportDetails.transactionRefNo��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��&_orderState.timestamps.tradingDateTime��3tradersAlgosWaiversIndicators.shortSellingIndicator��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��$_orderState.transactionDetails.price��transactionDetails.priceAverage�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��'_orderState.transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��transactionDetails.recordType��executionDetails.limitPrice��"transactionDetails.tradingCapacity��._orderState.transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��currency_attribute��asset_class_attribute��underlying_symbol_attribute��/venue_financial_instrument_short_name_attribute��venue_attribute��isin_attribute��exchange_symbol_attribute��bbg_figi_id_attribute��expiry_date_attribute��option_type_attribute��option_strike_price_attribute��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��__fallback_buyer__��__fallback_seller__��__fallback_counterparty__��__fallback_client__��__fallback_buyer_dec_maker__��__fallback_seller_dec_maker__��__fallback_inv_dec_in_firm__��__fallback_trader__��__fallback_exec_within_firm__��__fallback_executing_entity__��marketIdentifiers��FIGI��Ticker (Tag 55)��ISIN��Last Market��__currency_code__��__instr_unique_identifier__��__newo_in_file__��#__link_instrument_venue_attribute__��__instrument_full_name__��__instrument_short_name__��__is_created_through_fallback__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C(                                    �t�bh��__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�pandas._libs.missing��NA���h�h�h�h�et�b�_dtype�h��StringDtype���)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�R.EMT.795.1696920330--179044260�h�h��R.EMT.795.1696920330--179044259�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�O.EMT.795.1468573.20231009�h�h�O.EMT.795.1468575.20231009�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�1534618��1534618��1534618��1534620��1534620�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�h��ASM�j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�XETA�j
  j
  �AQEU�j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�DE000A0WMPJ6�j  j  �NL0000334118�j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�BBG000BFT9L9�j,  j,  �BBG000F5L454�j-  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(j,  j,  j,  j-  j-  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�AIXA GR Equity�j@  j@  �
ASM NA Equity�jA  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(j  j  j  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(j
  j
  j
  j  j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h��DE000A0WMPJ6EURXETA�h�h��NL0000334118EURAQEU�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(�
AIXTRON SE�jh  jh  �ASM INTL NV�ji  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h��]�(j@  j@  j@  jA  jA  et�bh�h�)��ubhhK ��h��R�(KKK��h�f8�����R�(Kh�NNNJ����J����K t�b�Cx      �      �      �      �      �    ��A    ��A    ��@    ���@    ���@      �      �      �      �      ��t�bhhK ��h��R�(KKK��h��C(                                    �t�bhhK ��h��R�(KKHK��h!�]�(�2�j�  j�  �1�j�  j�  j�  j�  j�  j�  �EMSI-EOD�j�  j�  j�  j�  �
2023-10-10��
2023-10-10��
2023-10-10��
2023-10-10��
2023-10-10��SELL�j�  j�  �BUYI�j�  h��PARF��CAME�h�j�  �NEWO�j�  j�  j�  j�  �Market�j�  j�  j�  j�  � �j�  j�  j�  j�  j�  j�  j�  j�  j�  �AOTC�j�  j�  j�  j�  ]��DAVY�a]�j�  a]�j�  a]�j�  a]�j�  a�
Standalone�j�  j�  j�  j�  �Order�j�  j�  j�  j�  �
OrderState�j�  j�  j�  j�  GA��    h�h�G@�؀    h�h�G@?�
:�Azh�h�G@x�z0��Hh�h�K h�h�h�G@�    h�h�G@��     �dummy_test_path�j�  j�  j�  j�  �!R.EMT.795.1696920330--179044260.0��!F.EMT.795.1696920330--179044260.3��!R.EMT.795.1696920330--179044260.2��!R.EMT.795.1696920330--179044259.3��!F.EMT.795.1696920330--179044259.3�j�  j�  j�  j�  j�  �2023-10-10T01:45:30.662000Z��2023-10-10T01:45:30.662000Z��2023-10-10T10:36:22.438000Z��2023-10-10T01:45:30.670000Z��2023-10-10T01:45:30.670000Z�j�  �2023-10-10T01:45:35.308000Z�j�  j�  �2023-10-10T01:45:37.138000Z��2023-10-10T01:45:35.308000Z��2023-10-10T01:45:35.308000Z��2023-10-10T10:36:22.438000Z��2023-10-10T01:45:37.138000Z��2023-10-10T01:45:37.138000Z�h��2023-10-10T02:00:16.708000Z�h�h��2023-10-10T02:05:15.634000Z�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  h�G@?�
:�Azh�h�G@x�z0��Hh�G@@@     G@?�
:�A|h�G@x�����͌EUR�j�  j�  j�  j�  �MONE�j�  j�  j�  j�  h�G@�    h�h�G@��     j�  j�  j�  j�  j�  �UNIT�j�  j�  j�  j�  �Client Side��Market Side�j�  j�  j�  h�h�h�h�h�j�  j�  j�  j�  j�  h�j�  h�h�j�  �XOFF��XETA�j�  j�  �AQEU�j�  j�  j�  j�  j�  ]�(}�(�labelId�j  �path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�u}�(j�  �DE000A0WMPJ6EURXETA�j�  j�  j�  j�  u}�(j�  j,  j�  j�  j�  j�  ue]�(}�(j�  j  j�  j�  j�  j�  u}�(j�  �DE000A0WMPJ6EURXETA�j�  j�  j�  j�  u}�(j�  j,  j�  j�  j�  j�  ue]�(}�(j�  j  j�  j�  j�  j�  u}�(j�  �DE000A0WMPJ6EURXETA�j�  j�  j�  j�  u}�(j�  j,  j�  j�  j�  j�  ue]�(}�(j�  j  j�  j�  j�  j�  u}�(j�  �NL0000334118EURAQEU�j�  j�  j�  j�  u}�(j�  j-  j�  j�  j�  j�  ue]�(}�(j�  j  j�  j�  j�  j�  u}�(j�  �NL0000334118EURAQEU�j�  j�  j�  j�  u}�(j�  j-  j�  j�  j�  j�  uej�  j�  j�  j�  j�  �equity�j�  j�  j�  j�  G�      G�      G�      G�      G�      h�h�h�h�h�h�h�h�h�h�]�(}�(j�  �id:dnceu�j�  �buyer�j�  j�  �ARRAY���R�u}�(j�  �lei:635400haziniqwxbii49�j�  �reportDetails.executingEntity�j�  j�  u}�(j�  �lei:635400haziniqwxbii49�j�  �seller�j�  j�  u}�(j�  �id:dnceu�j�  �counterparty�j�  j�  u}�(j�  �id:stu mcquaid�j�  �:tradersAlgosWaiversIndicators.investmentDecisionWithinFirm�j�  j�  u}�(j�  �id:stu mcquaid�j�  �1tradersAlgosWaiversIndicators.executionWithinFirm�j�  j�  u}�(j�  �id:stu mcquaid�j�  �trader�j�  j�  ue]�(}�(j�  �id:dnceu�j�  j�  j�  j�  u}�(j�  �lei:635400haziniqwxbii49�j�  j�  j�  j�  u}�(j�  �lei:635400haziniqwxbii49�j�  j�  j�  j�  u}�(j�  �id:dnceu�j�  j�  j�  j�  u}�(j�  �id:who�j�  j�  j�  j�  u}�(j�  �id:stu mcquaid�j�  j�  j�  j�  u}�(j�  �id:stu mcquaid�j�  j�  j�  j�  ue]�(}�(j�  �id:dnceu�j�  j�  j�  j�  u}�(j�  �lei:635400haziniqwxbii49�j�  j�  j�  j�  u}�(j�  �lei:635400haziniqwxbii49�j�  j�  j�  j�  u}�(j�  �id:dnceu�j�  j�  j�  j�  u}�(j�  �id:what�j�  j�  j�  j�  u}�(j�  �id:stu mcquaid�j�  j�  j�  j�  u}�(j�  �id:stu mcquaid�j�  j�  j�  j�  ue]�(}�(j�  �lei:894500wota5040khgx73�j�  j�  j�  j�  u}�(j�  �lei:894500wota5040khgx73�j�  j�  j�  j�  u}�(j�  �id:dnceu�j�  j�  j�  j�  u}�(j�  �id:dnceu�j�  j�  j�  j�  u}�(j�  �id:when�j�  j�  j�  j�  u}�(j�  �id:stu mcquaid�j�  j�  j�  j�  u}�(j�  �id:stu mcquaid�j�  j�  j�  j�  ue]�(}�(j�  �lei:894500wota5040khgx73�j�  j�  j�  j�  u}�(j�  �lei:894500wota5040khgx73�j�  j�  j�  j�  u}�(j�  �id:dnceu�j�  j�  j�  j�  u}�(j�  �id:dnceu�j�  j�  j�  j�  u}�(j�  �id:where�j�  j�  j�  j�  u}�(j�  �id:stu mcquaid�j�  j�  j�  j�  u}�(j�  �id:stu mcquaid�j�  j�  j�  j�  ue�lei:635400haziniqwxbii49��lei:635400haziniqwxbii49��lei:635400haziniqwxbii49��lei:894500wota5040khgx73��lei:894500wota5040khgx73��id:dnceu��id:dnceu��id:dnceu��lei:894500wota5040khgx73��lei:894500wota5040khgx73��lei:635400haziniqwxbii49��lei:635400haziniqwxbii49��lei:635400haziniqwxbii49��id:dnceu��id:dnceu��id:dnceu��id:dnceu��id:dnceu��id:dnceu��id:dnceu�h�h�h�h�h�h�h�h�h�h��id:stu mcquaid��id:who��id:what��id:when��id:where��id:stu mcquaid��id:stu mcquaid��id:stu mcquaid��id:stu mcquaid��id:stu mcquaid�h�h�h�h�h��id:stu mcquaid��id:stu mcquaid��id:stu mcquaid��id:stu mcquaid��id:stu mcquaid��dnceu��dnceu��dnceu��894500wota5040khgx73��894500wota5040khgx73��635400haziniqwxbii49��635400haziniqwxbii49��635400haziniqwxbii49��dnceu��dnceu��dnceu��dnceu��dnceu��dnceu��dnceu�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��stu mcquaid��who��what��when��where��stu mcquaid��stu mcquaid��stu mcquaid��stu mcquaid��stu mcquaid��stu mcquaid��stu mcquaid��stu mcquaid��stu mcquaid��stu mcquaid��635400haziniqwxbii49��635400haziniqwxbii49��635400haziniqwxbii49��894500wota5040khgx73��894500wota5040khgx73�]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j  j  j  j  j	  e]�(j�  j�  j�  j  j  j  j  j  j  j  e]�(j�  j�  j�  j  j  j  j!  j#  j%  j'  e]�(j�  j�  j�  j*  j,  j.  j0  j2  j4  j6  ej�  j�  j�  j�  j�  �����j�  j�  j�  j�  j�  et�bhhK ��h��R�(KKK��h�b1�����R�(Kh"NNNJ����J����K t�b�C�t�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h9at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h:at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hKat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h\at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h^at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h_at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h`at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�haat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h{at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h|at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h}at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h~at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h0h=hdet�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bh�Nu��R�h
h}�(hhhK ��h��R�(KKH��h!�]�(h%h&h'h(h)h*h+h,h-h/h1h2h3h6h7h<h>h?h@hBhChDhEhFhGhHhIhJhLhMhNhOhPhQhRhShThUhVhWhXhYhZh[h]hbhchehfhghhhihjhkhlhmhnhohphqhrhshthuhvhwhxhyhzhh�h�et�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���K	K
K��R�u}�(jn  h�jo  jr  KKK��R�u}�(jn  h�jo  jr  KKK��R�u}�(jn  h�jo  jr  KKK��R�u}�(jn  h�jo  jr  KKK��R�u}�(jn  h�jo  jr  KKK��R�u}�(jn  h�jo  jr  KKK��R�u}�(jn  h�jo  jr  K&K'K��R�u}�(jn  h�jo  jr  K7K8K��R�u}�(jn  j  jo  jr  K9K:K��R�u}�(jn  j  jo  jr  K:K;K��R�u}�(jn  j  jo  jr  K;K<K��R�u}�(jn  j%  jo  jr  K<K=K��R�u}�(jn  j0  jo  jr  KVKWK��R�u}�(jn  j9  jo  jr  KWKXK��R�u}�(jn  jD  jo  jr  KXKYK��R�u}�(jn  jM  jo  jr  KYKZK��R�u}�(jn  jV  jo  jr  K[K\K��R�u}�(jn  ja  jo  jr  K^K_K��R�u}�(jn  jl  jo  jr  K_K`K��R�u}�(jn  jw  jo  hhK ��h��R�(KK��h�i8�����R�(Kh�NNNJ����J����K t�b�C              ?       �t�bu}�(jn  j�  jo  jr  KKK��R�u}�(jn  j�  jo  hhK ��h��R�(KKH��j�  �B@                                                                  
              
                                                                                     !       "       #       $       %       '       (       )       *       +       ,       -       .       /       0       1       2       3       4       5       6       8       =       >       @       A       B       C       D       E       F       G       H       I       J       K       L       M       N       O       P       Q       R       S       T       U       Z       \       ]       �t�bu}�(jn  j�  jo  jr  K`KaK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.