���+      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK4��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�Allocations��
AssetClass��	AssetType��BloombergID��CUSIP��Country��CreateDateTime��ID��ISIN��Industry��
IndustryGroup��Issuer��LastModifiedDateTime��Limit��
LocalCurrency��Note��OriginalOrderDateTime��OriginalOrderID��OriginalQuantity��PriceMultiplier��Quantity��Sector��SecurityName��SettlementDate��Side��Source��Status��SubIndustry��Symbol��SystemCurrency��
TotalQuantity��	TradeDate��Limit_x��ExpirationDate��UnderlyingSymbol��OccCode��LimitPrice_y��
Quantity_x��UnderlyingISIN��CreateDateTime_x��ID_y��DestinationDisplayName��ID_x��StrikePrice��LastModifiedDateTime_x��
Executions��ExecutionPrice��LimitPrice_x��Trader��Routes��
LimitPrice��CounterpartyCode�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(h�numpy.core.numeric��_frombuffer���(�                      �h�i8�����R�(K�<�NNNJ����J����K t�bK���C�t�R�hZ�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(X6  [{'AllocationBookType': 'Custodian', 'AllocationIdentifier': 'Order:272717|Route:NONE|Portfolio:238 Plan Associates LLC|Custodian:SSB', 'BaseAccountCurrency': 'USD', 'Custodian': 'SSB', 'ExecutedQuantity': 0, 'GrossAmount': 0, 'Manager': 'SMA', 'NetAmount': 0, 'Portfolio': '2', 'PortfolioName': '238 Plan Associates LLC', 'Price': 0, 'Quantity': 3082, 'TotalCommissions': 0, 'TotalFees': 0}, {'AllocationBookType': 'Custodian', 'AllocationIdentifier': 'Order:272717|Route:NONE|Portfolio:Cassini Partners, L.P.|Custodian:MSCO', 'BaseAccountCurrency': 'USD', 'Custodian': 'MSCO', 'ExecutedQuantity': 0, 'GrossAmount': 0, 'Manager': 'SMA', 'NetAmount': 0, 'Portfolio': '3', 'PortfolioName': 'Cassini Partners, L.P.', 'Price': 0, 'Quantity': 14094, 'TotalCommissions': 0, 'TotalFees': 0}, {'AllocationBookType': 'Strategy', 'AllocationIdentifier': 'Order:272717|Route:NONE|Portfolio:238 Plan Associates LLC|Strategy:TRADING-NONE', 'BaseAccountCurrency': 'USD', 'ExecutedQuantity': 0, 'GrossAmount': 0, 'Manager': 'SMA', 'NetAmount': 0, 'Portfolio': '2', 'PortfolioName': '238 Plan Associates LLC', 'Price': 0, 'Quantity': 3082, 'Strategy1': 'TRADING', 'TotalCommissions': 0, 'TotalFees': 0}, {'AllocationBookType': 'Strategy', 'AllocationIdentifier': 'Order:272717|Route:NONE|Portfolio:Cassini Partners, L.P.|Strategy:TRADING-NONE', 'BaseAccountCurrency': 'USD', 'ExecutedQuantity': 0, 'GrossAmount': 0, 'Manager': 'SMA', 'NetAmount': 0, 'Portfolio': '3', 'PortfolioName': 'Cassini Partners, L.P.', 'Price': 0, 'Quantity': 14094, 'Strategy1': 'TRADING', 'TotalCommissions': 0, 'TotalFees': 0}]�X8  [{'AllocationBookType': 'Custodian', 'AllocationIdentifier': 'Order:272718|Route:NONE|Portfolio:238 Plan Associates LLC|Custodian:SSB', 'BaseAccountCurrency': 'USD', 'Custodian': 'SSB', 'ExecutedQuantity': 0, 'GrossAmount': 0, 'Manager': 'SMA', 'NetAmount': 0, 'Portfolio': '2', 'PortfolioName': '238 Plan Associates LLC', 'Price': 0, 'Quantity': 11122, 'TotalCommissions': 0, 'TotalFees': 0}, {'AllocationBookType': 'Custodian', 'AllocationIdentifier': 'Order:272718|Route:NONE|Portfolio:Cassini Partners, L.P.|Custodian:MSCO', 'BaseAccountCurrency': 'USD', 'Custodian': 'MSCO', 'ExecutedQuantity': 0, 'GrossAmount': 0, 'Manager': 'SMA', 'NetAmount': 0, 'Portfolio': '3', 'PortfolioName': 'Cassini Partners, L.P.', 'Price': 0, 'Quantity': 40227, 'TotalCommissions': 0, 'TotalFees': 0}, {'AllocationBookType': 'Strategy', 'AllocationIdentifier': 'Order:272718|Route:NONE|Portfolio:238 Plan Associates LLC|Strategy:TRADING-NONE', 'BaseAccountCurrency': 'USD', 'ExecutedQuantity': 0, 'GrossAmount': 0, 'Manager': 'SMA', 'NetAmount': 0, 'Portfolio': '2', 'PortfolioName': '238 Plan Associates LLC', 'Price': 0, 'Quantity': 11122, 'Strategy1': 'TRADING', 'TotalCommissions': 0, 'TotalFees': 0}, {'AllocationBookType': 'Strategy', 'AllocationIdentifier': 'Order:272718|Route:NONE|Portfolio:Cassini Partners, L.P.|Strategy:TRADING-NONE', 'BaseAccountCurrency': 'USD', 'ExecutedQuantity': 0, 'GrossAmount': 0, 'Manager': 'SMA', 'NetAmount': 0, 'Portfolio': '3', 'PortfolioName': 'Cassini Partners, L.P.', 'Price': 0, 'Quantity': 40227, 'Strategy1': 'TRADING', 'TotalCommissions': 0, 'TotalFees': 0}]�et�b�_dtype�hr�StringDtype���)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�Equity�h�et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�Common Stock�h�et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�EIM IS��DCMS IS�et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�	Y2251M148��	Y2023T132�et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�IND�h�et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�INE066A01021��INE499A01024�et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�Automobiles��	Chemicals�et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�Automobiles & Components��	Materials�et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�
EICHER MOTORS��DCM SHRIRAM LTD�et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�INR�h�et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�#Take EIM to 3.5% and add it to DCMS�h�et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�2023-12-28T00:00:00.000Z�j  et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�272125��274212�et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�45000��165000�et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�Consumer Discretionary��	Materials�et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�
EICHER MOTORS��DCM SHRIRAM LTD�et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�
2024-01-11�j9  et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�Sell��Buy�et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�EzeIMS�jN  et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�New�jX  et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�Motorcycle Manufacturers��Diversified Chemicals�et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�EICHERMOT.EQ.NIN��DCMSHRIRAM.EQ.NIN�et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�USD�jx  et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�45000��165000�et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�
2024-01-10�j�  et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(�pandas._libs.missing��NA���j�  et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(j�  j�  et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(j�  j�  et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(j�  j�  et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(j�  j�  et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(j�  j�  et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(j�  j�  et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(j�  j�  et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(j�  j�  et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(j�  j�  et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(j�  j�  et�bh�h�)��ubht)��}�(hwhhK ��h��R�(KK��h~�]�(j�  j�  et�bh�h�)��ubhc(�`             �?      �?      �      �      �      �      �      �      �      �      �      ��h�f8�����R�(KhhNNNJ����J����K t�bKK��hkt�R�hhK ��h��R�(KKK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �nan��nan�j�  j�  et�bhhK ��h��R�(KKK��h!�]�(�MSCO�j�  et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h(at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h)at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h0at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h:at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h<at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h=at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h?at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h@at�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hBat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hCat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hDat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hEat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hFat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hGat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hHat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hKat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hLat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hMat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hNat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hOat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hRat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hUat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hVat�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h8hIhJhPhShTet�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h+h,h1h2h9hQhWet�bhZNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hXat�bhZNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hu�mgr_locs��builtins��slice���K KK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KK	K��R�u}�(j�  h�j�  j�  K	K
K��R�u}�(j�  h�j�  j�  K
KK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j'  j�  j�  KKK��R�u}�(j�  j2  j�  j�  KKK��R�u}�(j�  j<  j�  j�  KKK��R�u}�(j�  jG  j�  j�  KKK��R�u}�(j�  jQ  j�  j�  KKK��R�u}�(j�  j[  j�  j�  KKK��R�u}�(j�  jf  j�  j�  KKK��R�u}�(j�  jq  j�  j�  KKK��R�u}�(j�  j{  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KK K��R�u}�(j�  j�  j�  j�  K K!K��R�u}�(j�  j�  j�  j�  K!K"K��R�u}�(j�  j�  j�  j�  K"K#K��R�u}�(j�  j�  j�  j�  K#K$K��R�u}�(j�  j�  j�  j�  K&K'K��R�u}�(j�  j�  j�  j�  K'K(K��R�u}�(j�  j�  j�  j�  K(K)K��R�u}�(j�  j�  j�  j�  K)K*K��R�u}�(j�  j�  j�  j�  K*K+K��R�u}�(j�  j�  j�  j�  K-K.K��R�u}�(j�  j�  j�  j�  K0K1K��R�u}�(j�  j�  j�  j�  K1K2K��R�u}�(j�  j  j�  hc(�0              $       %       +       .       /       �hgK��hkt�R�u}�(j�  j	  j�  hc(�8                            
              ,       2       �h�i8�����R�(KhhNNNJ����J����K t�bK��hkt�R�u}�(j�  j  j�  j�  K3K4K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.