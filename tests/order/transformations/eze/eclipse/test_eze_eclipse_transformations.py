import os
from pathlib import Path

import pandas as pd
import pytest
from mock.mock import <PERSON><PERSON><PERSON>
from prefect import context
from swarm.task.auditor import Auditor

from swarm_tasks.order.transformations.eze.eclipse.eze_eclipse_transformations import (
    EzeEclipseOrderTransformations,
)
from swarm_tasks.order.transformations.eze.eclipse.static import DerivedCols
from swarm_tasks.order.transformations.order_transform_maps import (
    eze_eclipse_transform_map,
)
from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
SELLARONDA_TEST_FILES_DIR = TEST_FILES_DIR.joinpath("sellaronda_data")
SELLARONDA_TEST_FILE_PATH = SELLARONDA_TEST_FILES_DIR.joinpath(
    r"eze_eclipse_sellaronda_sample_source.pkl"
)
SELLARONDA_EXPECTED_FILE_PATH = SELLARONDA_TEST_FILES_DIR.joinpath(
    r"sellaronda_expected_result.pkl"
)
PREPROCESS_EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath(
    r"expected_preprocess_custom.pkl"
)
PREPROCESS_TEST_FILE_PATH = TEST_FILES_DIR.joinpath(r"preprocess_custom.pkl")
TEST_FILE_PATH = TEST_FILES_DIR.joinpath(r"eze_eclipse_sample_source.pkl")
ORDERS_EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath(r"expected_result.pkl")
TEST_FILE_PATH_NO_ROUTES = TEST_FILES_DIR.joinpath(
    r"eze_eclipse_sample_source_no_routes.pkl"
)
ORDERS_EXPECTED_FILE_PATH_NO_ROUTES = TEST_FILES_DIR.joinpath(
    r"expected_result_no_routes.pkl"
)


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


class TestEzeEclipseOrderTransformations:
    """
    Test Eze Eclipse Order Transformations
    """

    @staticmethod
    def mock_get_tenant_lei(mocker, test_df):
        mock_get_tenant_lei = mocker.patch.object(GetTenantLEI, "process")
        tenant_lei_df = pd.DataFrame(index=test_df.index)
        tenant_lei_df = tenant_lei_df.assign(**{"__TENANT_LEI__": "lei:sample_lei"})
        mock_get_tenant_lei.return_value = tenant_lei_df
        return mock_get_tenant_lei

    @pytest.mark.parametrize(
        "test_file_path, expected_file_path",
        [
            (TEST_FILE_PATH, ORDERS_EXPECTED_FILE_PATH),
            (TEST_FILE_PATH_NO_ROUTES, ORDERS_EXPECTED_FILE_PATH_NO_ROUTES),
        ],
    )
    def test_end_to_end_transformations(
        self,
        mocker: MagicMock,
        test_file_path: str,
        expected_file_path: str,
        auditor: Auditor,
    ):
        os.environ["SWARM_FILE_URL"] = str(test_file_path)

        source_frame = pd.read_pickle(test_file_path)
        expected_frame = pd.read_pickle(expected_file_path)

        self.mock_get_tenant_lei(mocker, source_frame)

        task = EzeEclipseOrderTransformations(
            source_frame=source_frame, logger=context.get("logger"), auditor=auditor
        )
        result = task.process()

        pd.testing.assert_frame_equal(
            result.drop(
                [
                    "sourceKey",
                    "marketIdentifiers",
                    "marketIdentifiers.parties",
                    "clientFileIdentifier",
                    "__AGGREGATED_PORTFOLIO_NAME__",
                    "__COUNTERPARTY__",
                ],
                axis=1,
            ).dropna(),
            expected_frame.drop(
                [
                    "sourceKey",
                    "marketIdentifiers",
                    "marketIdentifiers.parties",
                    "clientFileIdentifier",
                    "__AGGREGATED_PORTFOLIO_NAME__",
                ],
                axis=1,
            ).dropna(),
            check_dtype=False,
        )

    @pytest.mark.parametrize(
        "test_file_path, expected_file_path",
        [(PREPROCESS_TEST_FILE_PATH, PREPROCESS_EXPECTED_FILE_PATH)],
    )
    def test_pre_process_for_party_identifiers(
        self,
        test_file_path: str,
        expected_file_path: str,
        auditor: Auditor,
        mocker: MagicMock,
    ):
        source_frame = pd.read_pickle(test_file_path)
        expected_results = pd.read_pickle(expected_file_path)

        self.mock_get_tenant_lei(mocker, source_frame)

        task = EzeEclipseOrderTransformations(
            source_frame=source_frame, logger=context.get("logger"), auditor=auditor
        )
        task._pre_process_for_executions()
        task._pre_process_for_instrument_identifiers()
        task._pre_process_for_party_identifiers()

        # Select only the 6 columns to be tested by the implementation from ON-4327.
        # More column from preprocess can be asserted if needed
        short_pre_process_df = task.pre_process_df[
            [
                DerivedCols.AGGREGATED_DESTINATION_DISPLAY_NAME,
                DerivedCols.AGGREGATED_DESTINATION_DISPLAY_NAME_WITH_PREFIX,
                DerivedCols.AGGREGATED_TRADER,
                DerivedCols.AGGREGATED_TRADER_WITH_PREFIX,
                DerivedCols.AGGREGATED_INVESTMENT_DECISION_WITHIN_FIRM,
                DerivedCols.AGGREGATED_INVESTMENT_DECISION_WITHIN_FIRM_WITH_PREFIX,
            ]
        ]

        pd.testing.assert_frame_equal(short_pre_process_df, expected_results)

    def test_sellaronda_override(
        self,
        auditor: Auditor,
        mocker: MagicMock,
    ):
        source_frame = pd.read_pickle(SELLARONDA_TEST_FILE_PATH)
        expected_results = pd.read_pickle(SELLARONDA_EXPECTED_FILE_PATH)

        self.mock_get_tenant_lei(mocker, source_frame)

        task = eze_eclipse_transform_map.transformation(tenant="sellaronda")(
            source_frame=source_frame, logger=context.get("logger"), auditor=auditor
        )

        # Process the transformation
        task.process()

        short_pre_process_df = task.pre_process_df[[DerivedCols.COUNTERPARTY]]

        pd.testing.assert_frame_equal(short_pre_process_df, expected_results)
