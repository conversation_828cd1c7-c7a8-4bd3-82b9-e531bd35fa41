��       �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK;��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�date��timestamps.orderReceived��timestamps.orderSubmitted��timestamps.orderStatusUpdated��"transactionDetails.tradingDateTime��timestamps.tradingDateTime��	_order.id��_orderState.id��orderIdentifiers.orderIdCode��reportDetails.transactionRefNo��!orderIdentifiers.transactionRefNo��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo��_order.buySell��_orderState.buySell��!executionDetails.buySellIndicator��#transactionDetails.buySellIndicator��transactionDetails.recordType��transactionDetails.priceAverage��priceFormingData.price��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation�� priceFormingData.initialQuantity��"priceFormingData.remainingQuantity��priceFormingData.tradedQuantity��transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation�� transactionDetails.ultimateVenue��transactionDetails.venue��dataSourceName��	sourceKey��sourceIndex��_order.__meta_model__��_orderState.__meta_model__��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��__asset_class__��marketIdentifiers.instrument��asset_class_attribute��expiry_date_attribute��currency_attribute��venue_attribute��underlying_isin_attribute��isin_attribute��marketIdentifiers��INSTRUMENTNAME�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KKq��h�i8�����R�(K�<�NNNJ����J����K t�b�B�                                                                  	       
                     
                                                                                                                                             !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       3       4       5       6       7       8       9       :       ;       <       =       >       ?       @       A       B       C       D       E       F       G       H       I       J       K       L       M       N       O       P       Q       R       S       T       U       V       W       X       Y       Z       [       \       ]       ^       _       `       a       b       c       d       e       f       g       h       i       j       k       l       m       n       o       p       �t�bha�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KKq��h!�]�(�160935541829192��160935541829190��160935541829191��160935541829189��160935641829188��160286541633050��160286541626195��160286541626199��160286541626198��160286541626197��160286541626196��160286541626176��160286541626175��160286541626174��160286541626173��160286541626172��160860141814898��160860141814899��160860141819252��160860141819250��160860141819251��160850241814892��160850241814890��160850241814891��160850241814888��160850241814889��160850241814887��160995141882222��160995341882213��160995341882212��160995341882211��160995341882210��160995341882209��160995341882208��160995241882207��160995241882206��160995241882205��160995241882204��160995241882203��160995241882202��160577141710456��160577141710457��160577141710458��160577141710459��160577141710454��160577141710455��160735741744996��160735741744996��160735741744997��160735741744997��160735741744992��160735741744992��160735741744993��160735741744993��160735741744994��160735741744994��160735741744995��160735741744995��160735741744984��160735741744984��160735741744985��160735741744985��160735741744980��160735741744980��160735741744981��160735741744981��160735741744982��160735741744982��160735741744983��160735741744983��160735741744968��160735741744968��160735741744964��160735741744964��160735741744965��160735741744965��160735741744966��160735741744966��160735741744967��160735741744967��160735741744963��160735741744963��160763141744998��160762741744988��160762741744989��160762741744990��160762741744991��160762741744986��160762741744987��160735841744960��160735841744960��160735841744961��160735841744961��160735841744962��160735841744962��160735841744957��160735841744957��160735841744958��160735841744958��160735841744959��160735841744959��160735841744952��160735841744952��160735841744953��160735841744953��160735841744954��160735841744954��160735841744955��160735841744955��160735841744950��160735841744950��160735841744951��160735841744951�et�b�_dtype�hw�StringDtype���)��ubhy)��}�(h|hhK ��h��R�(KKq��h!�]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KKq��h!�]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KKq��h!�]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KKq��h!�]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KKq��h!�]�(�pandas._libs.missing��NA���j%  j%  j%  j%  �MARKET�j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  �OTC�j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j&  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j&  j&  j&  j&  j&  j&  j&  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  j'  et�bh�h�)��ubhy)��}�(h|hhK ��h��R�(KKq��h!�]�(j%  j%  j%  j%  j%  �OTC�j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  j1  et�bh�h�)��ubhhK ��h��R�(KKKq��h�f8�����R�(KhoNNNJ����J����K t�b�B         �      �      �      �      �     PW@     PW@     PW@     PW@     PW@     PW@     PW@     PW@     PW@     PW@     PW@     p�@     p�@     p�@     p�@     p�@     �j@     �j@     �j@     �j@     �j@     �j@      U@     �V@     �V@     �V@     �V@     �V@     �V@33333CY@33333CY@33333CY@33333CY@33333CY@33333CY@      j@      j@      j@      j@      j@      j@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@     pi@33333X@     �X@     �X@     �X@     �X@     �X@     �X@      f@      f@      f@      f@      f@      f@      f@      f@      f@      f@      f@      f@      f@      f@      f@      f@      f@      f@      f@      f@      f@      f@      f@      f@      �      �      �      �      �    �M]A    (@`A    ��ZA    �UA    �`A    �1`A    $`A    n�_A    ��]A    �``A    �``A    jTA    vVA    b�SA    b�PA    �oIA   ���A    ��xA   ���A   ��0�A   ����A   ��̆A            �xPA    ��KA    ��EA    �"RA    �ORA    t�QA    �XPA    ��KA    X�DA    p(RA    �RA    8RA    �vA    M�hA   ��qA    �tA    ��vA    1wA   ���A   ���A   @5�A   @5�A   �+��A   �+��A   �0��A   �0��A   ����A   ����A   @��A   @��A   ���A   ���A   @���A   @���A    p��A    p��A   ����A   ����A    #�A    #�A   @n�A   @n�A   ����A   ����A   �C��A   �C��A   �LD�A   �LD�A   @�'�A   @�'�A   @J�A   @J�A    7R�A    7R�A    �ORA    ��bA    t]A    X�`A    w�aA    �bA    h�bA    	mA    	mA    յpA    յpA   �2�qA   �2�qA   ��rA   ��rA   �)�rA   �)�rA    #�rA    #�rA   ��rA   ��rA    �mA    �mA    յpA    յpA   �2�qA   �2�qA   ��rA   ��rA   �)�rA   �)�rA    X&3A    (c?A     �A    pW_A    ��hA    0A    @}
A    Ȗ9A    ��HA    @*A    �&A     _A    �,A    P,A    �OA    �OA    �/&A    @}
A    �(A    �8A    WDA    <�]A    J�vA    £gA    �4A    �hBA    ��@A    ��>A     �$A    �15A    �t@A    �A     jA    ��A    ��%A    @5A    H�AA     L
A    �� A     XA    �f2A    ��fA    ΡWA    H�MA    ��0A     �$A    ��BA    ��BA    ��7A    ��7A     �A     �A    ��A    ��A    @kA    @kA    .3RA    .3RA    �[LA    �[LA    \�AA    \�AA     $A     $A    ��A    ��A    @&A    @&A    NK[A    NK[A    H�MA    H�MA     �$A     �$A    �f2A    �f2A    ��fA    ��fA    ΡWA    ΡWA    ��0A    ��0A    �O2A    ��
A    X,BA    ��2A    ��'A    ��
A     � A    .3RA    .3RA    ��BA    ��BA    ��7A    ��7A     �A     �A    ��A    ��A    @kA    @kA    �zA    �zA    42RA    42RA    ��BA    ��BA    ��7A    ��7A     �A     �A    ��A    ��A    X&3A    (c?A     �A    pW_A    ��hA    0A    @}
A    Ȗ9A    ��HA    @*A    �&A     _A    �,A    P,A    �OA    �OA    �/&A    @}
A    �(A    �8A    WDA    <�]A    J�vA    £gA    �4A    �hBA    ��@A    ��>A     �$A    �15A    �t@A    �A     jA    ��A    ��%A    @5A    H�AA     L
A    �� A     XA    �f2A    ��fA    ΡWA    H�MA    ��0A     �$A    ��BA    ��BA    ��7A    ��7A     �A     �A    ��A    ��A    @kA    @kA    .3RA    .3RA    �[LA    �[LA    \�AA    \�AA     $A     $A    ��A    ��A    @&A    @&A    NK[A    NK[A    H�MA    H�MA     �$A     �$A    �f2A    �f2A    ��fA    ��fA    ΡWA    ΡWA    ��0A    ��0A    �O2A    ��
A    X,BA    ��2A    ��'A    ��
A     � A    .3RA    .3RA    ��BA    ��BA    ��7A    ��7A     �A     �A    ��A    ��A    @kA    @kA    �zA    �zA    42RA    42RA    ��BA    ��BA    ��7A    ��7A     �A     �A    ��A    ��A�t�bhhK ��h��R�(KKKq��hn�B�                                                                  	       
                     
                                                                                                                                             !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       3       4       5       6       7       8       9       :       ;       <       =       >       ?       @       A       B       C       D       E       F       G       H       I       J       K       L       M       N       O       P       Q       R       S       T       U       V       W       X       Y       Z       [       \       ]       ^       _       `       a       b       c       d       e       f       g       h       i       j       k       l       m       n       o       p       �t�bhhK ��h��R�(KK.Kq��h!�]�(j%  j%  j%  j%  j%  �
2022-03-09��
2022-03-09��
2022-03-09��
2022-03-09��
2022-03-09��
2022-03-09��
2022-03-09��
2022-03-09��
2022-03-09��
2022-03-09��
2022-03-09��
2022-03-25��
2022-03-25��
2022-03-25��
2022-03-25��
2022-03-25��
2022-03-25��
2022-03-25��
2022-03-25��
2022-03-25��
2022-03-25��
2022-03-25��
2022-03-29��
2022-03-29��
2022-03-29��
2022-03-29��
2022-03-29��
2022-03-29��
2022-03-29��
2022-03-29��
2022-03-29��
2022-03-29��
2022-03-29��
2022-03-29��
2022-03-29��
2022-03-16��
2022-03-16��
2022-03-16��
2022-03-16��
2022-03-16��
2022-03-16��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21��
2022-03-21�j%  j%  j%  j%  j%  �2022-03-09T16:23:35.000000Z��2022-03-09T16:23:35.000000Z��2022-03-09T16:23:35.000000Z��2022-03-09T16:23:35.000000Z��2022-03-09T16:23:35.000000Z��2022-03-09T16:23:35.000000Z��2022-03-09T16:23:35.000000Z��2022-03-09T16:23:35.000000Z��2022-03-09T16:23:35.000000Z��2022-03-09T16:23:35.000000Z��2022-03-09T16:23:35.000000Z��2022-03-25T13:34:46.000000Z��2022-03-25T13:34:46.000000Z��2022-03-25T13:34:46.000000Z��2022-03-25T13:34:46.000000Z��2022-03-25T13:34:46.000000Z��2022-03-25T11:51:41.000000Z��2022-03-25T11:51:41.000000Z��2022-03-25T11:51:41.000000Z��2022-03-25T11:51:41.000000Z��2022-03-25T11:51:41.000000Z��2022-03-25T11:51:41.000000Z��2022-03-29T16:34:30.000000Z��2022-03-29T18:54:14.000000Z��2022-03-29T18:54:14.000000Z��2022-03-29T18:54:14.000000Z��2022-03-29T18:54:14.000000Z��2022-03-29T18:54:14.000000Z��2022-03-29T18:54:14.000000Z��2022-03-29T17:38:14.000000Z��2022-03-29T17:38:14.000000Z��2022-03-29T17:38:14.000000Z��2022-03-29T17:38:14.000000Z��2022-03-29T17:38:14.000000Z��2022-03-29T17:38:14.000000Z��2022-03-16T16:38:59.000000Z��2022-03-16T16:38:59.000000Z��2022-03-16T16:38:59.000000Z��2022-03-16T16:38:59.000000Z��2022-03-16T16:38:59.000000Z��2022-03-16T16:38:59.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T13:52:18.000000Z��2022-03-21T18:58:26.000000Z��2022-03-21T17:14:09.000000Z��2022-03-21T17:14:09.000000Z��2022-03-21T17:14:09.000000Z��2022-03-21T17:14:09.000000Z��2022-03-21T17:14:09.000000Z��2022-03-21T17:14:09.000000Z��2022-03-21T13:56:36.000000Z��2022-03-21T13:56:36.000000Z��2022-03-21T13:56:36.000000Z��2022-03-21T13:56:36.000000Z��2022-03-21T13:56:36.000000Z��2022-03-21T13:56:36.000000Z��2022-03-21T13:56:36.000000Z��2022-03-21T13:56:36.000000Z��2022-03-21T13:56:36.000000Z��2022-03-21T13:56:36.000000Z��2022-03-21T13:56:36.000000Z��2022-03-21T13:56:36.000000Z��2022-03-21T13:56:36.000000Z��2022-03-21T13:56:36.000000Z��2022-03-21T13:56:36.000000Z��2022-03-21T13:56:36.000000Z��2022-03-21T13:56:36.000000Z��2022-03-21T13:56:36.000000Z��2022-03-21T13:56:36.000000Z��2022-03-21T13:56:36.000000Z��2022-03-21T13:56:36.000000Z��2022-03-21T13:56:36.000000Z��2022-03-21T13:56:36.000000Z��2022-03-21T13:56:36.000000Z�j%  j%  j%  j%  j%  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j   j  j  j  j  j  j  j  j  j	  j
  j  j  j
  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j   j%  j%  j%  j%  j%  �2022-03-09T17:39:41.000000Z��2022-03-09T17:39:41.000000Z��2022-03-09T17:39:41.000000Z��2022-03-09T17:39:41.000000Z��2022-03-09T17:39:41.000000Z��2022-03-09T17:39:41.000000Z��2022-03-09T17:39:41.000000Z��2022-03-09T17:39:41.000000Z��2022-03-09T17:39:41.000000Z��2022-03-09T17:39:41.000000Z��2022-03-09T17:39:41.000000Z��2022-03-25T15:18:08.000000Z��2022-03-25T15:18:08.000000Z��2022-03-25T15:18:08.000000Z��2022-03-25T15:18:08.000000Z��2022-03-25T15:18:08.000000Z��2022-03-25T11:58:41.000000Z��2022-03-25T11:58:41.000000Z��2022-03-25T11:58:41.000000Z��2022-03-25T11:58:41.000000Z��2022-03-25T11:58:41.000000Z��2022-03-25T11:58:41.000000Z��2022-03-29T19:31:05.000000Z��2022-03-29T19:14:41.000000Z��2022-03-29T19:14:41.000000Z��2022-03-29T19:14:41.000000Z��2022-03-29T19:14:41.000000Z��2022-03-29T19:14:41.000000Z��2022-03-29T19:14:41.000000Z��2022-03-29T19:00:49.000000Z��2022-03-29T19:00:49.000000Z��2022-03-29T19:00:49.000000Z��2022-03-29T19:00:49.000000Z��2022-03-29T19:00:49.000000Z��2022-03-29T19:00:49.000000Z��2022-03-16T21:02:20.000000Z��2022-03-16T21:02:20.000000Z��2022-03-16T21:02:20.000000Z��2022-03-16T21:02:20.000000Z��2022-03-16T21:02:20.000000Z��2022-03-16T21:02:20.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T16:14:50.000000Z��2022-03-21T20:38:24.000000Z��2022-03-21T19:38:54.000000Z��2022-03-21T19:38:54.000000Z��2022-03-21T19:38:54.000000Z��2022-03-21T19:38:54.000000Z��2022-03-21T19:38:54.000000Z��2022-03-21T19:38:54.000000Z��2022-03-21T14:08:24.000000Z��2022-03-21T14:08:24.000000Z��2022-03-21T14:08:24.000000Z��2022-03-21T14:08:24.000000Z��2022-03-21T14:08:24.000000Z��2022-03-21T14:08:24.000000Z��2022-03-21T14:08:24.000000Z��2022-03-21T14:08:24.000000Z��2022-03-21T14:08:24.000000Z��2022-03-21T14:08:24.000000Z��2022-03-21T14:08:24.000000Z��2022-03-21T14:08:24.000000Z��2022-03-21T14:08:24.000000Z��2022-03-21T14:08:24.000000Z��2022-03-21T14:08:24.000000Z��2022-03-21T14:08:24.000000Z��2022-03-21T14:08:24.000000Z��2022-03-21T14:08:24.000000Z��2022-03-21T14:08:24.000000Z��2022-03-21T14:08:24.000000Z��2022-03-21T14:08:24.000000Z��2022-03-21T14:08:24.000000Z��2022-03-21T14:08:24.000000Z��2022-03-21T14:08:24.000000Z�j%  j%  j%  j%  j%  �2022-03-09T20:36:03.000000Z��2022-03-09T20:36:03.000000Z��2022-03-09T20:36:03.000000Z��2022-03-09T20:36:03.000000Z��2022-03-09T20:36:03.000000Z��2022-03-09T20:36:03.000000Z��2022-03-09T20:36:03.000000Z��2022-03-09T20:36:03.000000Z��2022-03-09T20:36:03.000000Z��2022-03-09T20:36:03.000000Z��2022-03-09T20:36:03.000000Z��2022-03-25T13:51:54.000000Z��2022-03-25T13:51:54.000000Z��2022-03-25T13:51:54.000000Z��2022-03-25T13:51:54.000000Z��2022-03-25T13:51:54.000000Z��2022-03-25T11:58:37.000000Z��2022-03-25T11:58:37.000000Z��2022-03-25T11:58:37.000000Z��2022-03-25T11:58:37.000000Z��2022-03-25T11:58:37.000000Z��2022-03-25T11:58:37.000000Z��2022-03-29T19:20:11.000000Z��2022-03-29T19:10:12.000000Z��2022-03-29T19:10:12.000000Z��2022-03-29T19:10:12.000000Z��2022-03-29T19:10:12.000000Z��2022-03-29T19:10:12.000000Z��2022-03-29T19:10:12.000000Z��2022-03-29T19:00:01.000000Z��2022-03-29T19:00:01.000000Z��2022-03-29T19:00:01.000000Z��2022-03-29T19:00:01.000000Z��2022-03-29T19:00:01.000000Z��2022-03-29T19:00:01.000000Z��2022-03-16T21:01:46.000000Z��2022-03-16T21:01:46.000000Z��2022-03-16T21:01:46.000000Z��2022-03-16T21:01:46.000000Z��2022-03-16T21:01:46.000000Z��2022-03-16T21:01:46.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T20:08:18.000000Z��2022-03-21T21:10:51.000000Z��2022-03-21T20:04:45.000000Z��2022-03-21T20:04:45.000000Z��2022-03-21T20:04:45.000000Z��2022-03-21T20:04:45.000000Z��2022-03-21T20:04:45.000000Z��2022-03-21T20:04:45.000000Z��2022-03-21T16:04:48.000000Z��2022-03-21T16:04:48.000000Z��2022-03-21T16:04:48.000000Z��2022-03-21T16:04:48.000000Z��2022-03-21T16:04:48.000000Z��2022-03-21T16:04:48.000000Z��2022-03-21T16:04:48.000000Z��2022-03-21T16:04:48.000000Z��2022-03-21T16:04:48.000000Z��2022-03-21T16:04:48.000000Z��2022-03-21T16:04:48.000000Z��2022-03-21T16:04:48.000000Z��2022-03-21T16:04:48.000000Z��2022-03-21T16:04:48.000000Z��2022-03-21T16:04:48.000000Z��2022-03-21T16:04:48.000000Z��2022-03-21T16:04:48.000000Z��2022-03-21T16:04:48.000000Z��2022-03-21T16:04:48.000000Z��2022-03-21T16:04:48.000000Z��2022-03-21T16:04:48.000000Z��2022-03-21T16:04:48.000000Z��2022-03-21T16:04:48.000000Z��2022-03-21T16:04:48.000000Z�j%  j%  j%  j%  j%  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �NEWO�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �FILL�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  e(j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  �2�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �1�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j%  j%  j%  j%  j%  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j%  j%  j%  j%  j%  �SELL�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �BUYI�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j%  j%  j%  j%  j%  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �
Allocation�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j%  j%  j%  j%  j%  G@WP     G@WP     G@WP     G@WP     G@WP     G@WP     G@WP     G@WP     G@WP     G@WP     G@WP     G@�p     G@�p     G@�p     G@�p     G@�p     G@j�     G@j�     G@j�     G@j�     G@j�     G@j�     G@U      G@V�     G@V�     G@V�     G@V�     G@V�     G@V�     G@YC33333G@YC33333G@YC33333G@YC33333G@YC33333G@YC33333G@j      G@j      G@j      G@j      G@j      G@j      G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@X33333G@X�     G@X�     G@X�     G@X�     G@X�     G@X�     G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      j%  j%  j%  j%  j%  G@WP     G@WP     G@WP     G@WP     G@WP     G@WP     G@WP     G@WP     G@WP     G@WP     G@WP     G@�p     G@�p     G@�p     G@�p     G@�p     G@j�     G@j�     G@j�     G@j�     G@j�     G@j�     G@U      G@V�     G@V�     G@V�     G@V�     G@V�     G@V�     G@YC33333G@YC33333G@YC33333G@YC33333G@YC33333G@YC33333G@j      G@j      G@j      G@j      G@j      G@j      G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@ip     G@X33333G@X�     G@X�     G@X�     G@X�     G@X�     G@X�     G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      G@f      j%  j%  j%  j%  j%  �USD�j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   �MONE�j  j  j  j  �PERC�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  e(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j%  j%  j%  j%  j%  GA`�    GA`�    GA`�    GA`�    GA`�    GA`�    GA`�    GA`�    GA`�    GA`�    GA`�    GAV�`    GAV�`    GAV�`    GAV�`    GAV�`    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA>��    GAS�    GAS�    GAS�    GAS�    GAS�    GAS�    GAS�    GAS�    GAS�    GAS�    GAS�    GAS�    GAwׄ    GAwׄ    GAwׄ    GAwׄ    GAwׄ    GAwׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GA�ׄ    GAV�`    GAc�    GAc�    GAc�    GAc�    GAc�    GAc�    GAs�    GAs�    GAs�    GAs�    GAs�    GAs�    GAs�    GAs�    GAs�    GAs�    GAs�    GAs�    GAs�    GAs�    GAs�    GAs�    GAs�    GAs�    GAs�    GAs�    GAs�    GAs�    GAs�    GAs�    j%  j%  j%  j%  j%  j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   �UNIT�j  j  j  j  �NOML�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �XXXX�j  j  j  j  �XOFF�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �Red Deer�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �r/Users/<USER>/code/swarm-tasks/tests/order/transformations/red_deer/data/integration_test_with_normalised_cols.pkl�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �Order�j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  �
OrderState�j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  j
  ]�(}�(�labelId��id:peregrine long only��path��seller��type��se_schema.static.market��IdentifierType����ARRAY���R�u}�(j
  �id:peregrine long only�j  �clientIdentifiers.client�j  j  ue]�(}�(j
  �id:impressa long only�j  j  j  j  u}�(j
  �id:impressa long only�j  j  j  j  ue]�(}�(j
  �id:clover unconstrained blended�j  j  j  j  u}�(j
  �id:clover unconstrained blended�j  j  j  j  ue]�(}�(j
  �id:crocus long only�j  j  j  j  u}�(j
  �id:crocus long only�j  j  j  j  ue]�(}�(j
  �id:fmap long only�j  j  j  j  u}�(j
  �id:fmap long only�j  j  j  j  ue]�(}�(j
  �id:emso�j  j  j  j  u}�(j
  �	id:pquinn�j  �1tradersAlgosWaiversIndicators.executionWithinFirm�j  j  �OBJECT���R�u}�(j
  �id:emso�j  j  j  j  u}�(j
  �	id:pquinn�j  �trader�j  j  ue]�(}�(j
  �id:iris�j  j  j  j  u}�(j
  �	id:pquinn�j  j4  j  j7  u}�(j
  �id:iris�j  j  j  j  u}�(j
  �	id:pquinn�j  j<  j  j  ue]�(}�(j
  �
id:saguaro�j  j  j  j  u}�(j
  �	id:pquinn�j  j4  j  j7  u}�(j
  �
id:saguaro�j  j  j  j  u}�(j
  �	id:pquinn�j  j<  j  j  ue]�(}�(j
  �id:cmap fund�j  j  j  j  u}�(j
  �	id:pquinn�j  j4  j  j7  u}�(j
  �id:cmap fund�j  j  j  j  u}�(j
  �	id:pquinn�j  j<  j  j  ue]�(}�(j
  �id:lavender�j  j  j  j  u}�(j
  �	id:pquinn�j  j4  j  j7  u}�(j
  �id:lavender�j  j  j  j  u}�(j
  �	id:pquinn�j  j<  j  j  ue]�(}�(j
  �id:lilac�j  j  j  j  u}�(j
  �	id:pquinn�j  j4  j  j7  u}�(j
  �id:lilac�j  j  j  j  u}�(j
  �	id:pquinn�j  j<  j  j  ue]�(}�(j
  �id:emso�j  j  j  j  u}�(j
  �	id:pquinn�j  j4  j  j7  u}�(j
  �id:emso�j  j  j  j  u}�(j
  �	id:pquinn�j  j<  j  j  ue]�(}�(j
  �
id:saguaro�j  j  j  j  u}�(j
  �	id:pquinn�j  j4  j  j7  u}�(j
  �
id:saguaro�j  j  j  j  u}�(j
  �	id:pquinn�j  j<  j  j  ue]�(}�(j
  �id:cmap fund�j  j  j  j  u}�(j
  �	id:pquinn�j  j4  j  j7  u}�(j
  �id:cmap fund�j  j  j  j  u}�(j
  �	id:pquinn�j  j<  j  j  ue]�(}�(j
  �id:lavender�j  j  j  j  u}�(j
  �	id:pquinn�j  j4  j  j7  u}�(j
  �id:lavender�j  j  j  j  u}�(j
  �	id:pquinn�j  j<  j  j  ue]�(}�(j
  �id:iris�j  j  j  j  u}�(j
  �	id:pquinn�j  j4  j  j7  u}�(j
  �id:iris�j  j  j  j  u}�(j
  �	id:pquinn�j  j<  j  j  ue]�(}�(j
  �
id:sei 40 act�j  j  j  j  u}�(j
  �id:gswallow�j  j4  j  j7  u}�(j
  �
id:sei 40 act�j  j  j  j  u}�(j
  �id:gswallow�j  j<  j  j  ue]�(}�(j
  �id:k2 longshort 40 act�j  j  j  j  u}�(j
  �id:gswallow�j  j4  j  j7  u}�(j
  �id:k2 longshort 40 act�j  j  j  j  u}�(j
  �id:gswallow�j  j<  j  j  ue]�(}�(j
  �id:k2 40 act�j  j  j  j  u}�(j
  �id:gswallow�j  j4  j  j7  u}�(j
  �id:k2 40 act�j  j  j  j  u}�(j
  �id:gswallow�j  j<  j  j  ue]�(}�(j
  �id:k2 em opps ucits�j  j  j  j  u}�(j
  �id:gswallow�j  j4  j  j7  u}�(j
  �id:k2 em opps ucits�j  j  j  j  u}�(j
  �id:gswallow�j  j<  j  j  ue]�(}�(j
  �id:k2 liquidity�j  j  j  j  u}�(j
  �id:gswallow�j  j4  j  j7  u}�(j
  �id:k2 liquidity�j  j  j  j  u}�(j
  �id:gswallow�j  j<  j  j  ue]�(}�(j
  �id:emso�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:emso�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:cmap fund�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:cmap fund�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �
id:saguaro�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �
id:saguaro�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lilac�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lilac�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lavender�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lavender�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:iris�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:iris�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:fmap long only�j  j  j  j  u}�(j
  �id:ofaltintrager�j  j4  j  j7  u}�(j
  �id:fmap long only�j  j  j  j  u}�(j
  �id:ofaltintrager�j  j<  j  j  ue]�(}�(j
  �id:emso�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:emso�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �
id:saguaro�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �
id:saguaro�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:cmap fund�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:cmap fund�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lavender�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lavender�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lilac�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lilac�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:iris�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:iris�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:emso�j  �buyer�j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:emso�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �
id:saguaro�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �
id:saguaro�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:cmap fund�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:cmap fund�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lavender�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lavender�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lilac�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lilac�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:iris�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:iris�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lavender�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lavender�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:cmap fund�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:cmap fund�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �
id:saguaro�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �
id:saguaro�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:emso�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:emso�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:iris�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:iris�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lilac�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lilac�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �
id:saguaro�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �
id:saguaro�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �
id:saguaro�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �
id:saguaro�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:emso�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:emso�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:emso�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:emso�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:iris�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:iris�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:iris�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:iris�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lilac�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lilac�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lilac�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lilac�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lavender�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lavender�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lavender�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lavender�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:cmap fund�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:cmap fund�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:cmap fund�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:cmap fund�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �
id:saguaro�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �
id:saguaro�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �
id:saguaro�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �
id:saguaro�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:emso�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:emso�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:emso�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:emso�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  uee(]�(}�(j
  �id:iris�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:iris�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:iris�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:iris�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lilac�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lilac�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lilac�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lilac�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lavender�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lavender�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lavender�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lavender�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:cmap fund�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:cmap fund�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:cmap fund�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:cmap fund�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:emso�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:emso�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:emso�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:emso�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lilac�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lilac�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lilac�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lilac�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lavender�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lavender�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lavender�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lavender�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:cmap fund�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:cmap fund�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:cmap fund�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:cmap fund�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �
id:saguaro�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �
id:saguaro�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �
id:saguaro�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �
id:saguaro�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:iris�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:iris�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:iris�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:iris�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:fmap long only�j  j<  j  j  u}�(j
  �id:ofaltintrager�j  j4  j  j7  u}�(j
  �id:fmap long only�j  j  j  j  u}�(j
  �id:ofaltintrager�j  j<  j  j  ue]�(}�(j
  �id:lavender�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lavender�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:cmap fund�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:cmap fund�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �
id:saguaro�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �
id:saguaro�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:emso�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:emso�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:iris�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:iris�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lilac�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lilac�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:cmap fund�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:cmap fund�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:cmap fund�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:cmap fund�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �
id:saguaro�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �
id:saguaro�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �
id:saguaro�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �
id:saguaro�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:emso�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:emso�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:emso�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:emso�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:iris�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:iris�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:iris�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:iris�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lilac�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lilac�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lilac�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lilac�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lavender�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lavender�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lavender�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lavender�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lavender�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lavender�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lavender�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lavender�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:cmap fund�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:cmap fund�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:cmap fund�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:cmap fund�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �
id:saguaro�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �
id:saguaro�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �
id:saguaro�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �
id:saguaro�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:emso�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:emso�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:emso�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:emso�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:iris�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:iris�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:iris�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:iris�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lilac�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lilac�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  ue]�(}�(j
  �id:lilac�j  j<  j  j  u}�(j
  �
id:ejayaweera�j  j4  j  j7  u}�(j
  �id:lilac�j  j  j  j  u}�(j
  �
id:ejayaweera�j  j<  j  j  uej%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  �id:emso��
id:saguaro��id:cmap fund��id:lavender��id:lilac��id:iris�j%  j%  j%  j%  j%  j%  �
id:saguaro��
id:saguaro��id:emso��id:emso��id:iris��id:iris��id:lilac��id:lilac��id:lavender��id:lavender��id:cmap fund��id:cmap fund��
id:saguaro��
id:saguaro��id:emso��id:emso��id:iris��id:iris��id:lilac��id:lilac��id:lavender��id:lavender��id:cmap fund��id:cmap fund��id:emso��id:emso��id:lilac��id:lilac��id:lavender��id:lavender��id:cmap fund��id:cmap fund��
id:saguaro��
id:saguaro��id:iris��id:iris��id:fmap long only�j%  j%  j%  j%  j%  j%  �id:cmap fund��id:cmap fund��
id:saguaro��
id:saguaro��id:emso��id:emso��id:iris��id:iris��id:lilac��id:lilac��id:lavender��id:lavender��id:lavender��id:lavender��id:cmap fund��id:cmap fund��
id:saguaro��
id:saguaro��id:emso��id:emso��id:iris��id:iris��id:lilac��id:lilac��id:peregrine long only��id:impressa long only��id:clover unconstrained blended��id:crocus long only��id:fmap long only��id:emso��id:iris��
id:saguaro��id:cmap fund��id:lavender��id:lilac��id:emso��
id:saguaro��id:cmap fund��id:lavender��id:iris��
id:sei 40 act��id:k2 longshort 40 act��id:k2 40 act��id:k2 em opps ucits��id:k2 liquidity��id:emso��id:cmap fund��
id:saguaro��id:lilac��id:lavender��id:iris��id:fmap long only��id:emso��
id:saguaro��id:cmap fund��id:lavender��id:lilac��id:iris�j%  j%  j%  j%  j%  j%  �id:lavender��id:cmap fund��
id:saguaro��id:emso��id:iris��id:lilac�j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  �id:lavender��id:cmap fund��
id:saguaro��id:emso��id:iris��id:lilac�j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  �	id:pquinn��	id:pquinn��	id:pquinn��	id:pquinn��	id:pquinn��	id:pquinn��	id:pquinn��	id:pquinn��	id:pquinn��	id:pquinn��	id:pquinn��id:gswallow��id:gswallow��id:gswallow��id:gswallow��id:gswallow��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��id:ofaltintrager��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��id:ofaltintrager��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��id:peregrine long only��id:impressa long only��id:clover unconstrained blended��id:crocus long only��id:fmap long only��id:emso��id:iris��
id:saguaro��id:cmap fund��id:lavender��id:lilac��id:emso��
id:saguaro��id:cmap fund��id:lavender��id:iris��
id:sei 40 act��id:k2 longshort 40 act��id:k2 40 act��id:k2 em opps ucits��id:k2 liquidity��id:emso��id:cmap fund��
id:saguaro��id:lilac��id:lavender��id:iris��id:fmap long only��id:emso��
id:saguaro��id:cmap fund��id:lavender��id:lilac��id:iris��id:emso��
id:saguaro��id:cmap fund��id:lavender��id:lilac��id:iris��id:lavender��id:cmap fund��
id:saguaro��id:emso��id:iris�e(�id:lilac��
id:saguaro��
id:saguaro��id:emso��id:emso��id:iris��id:iris��id:lilac��id:lilac��id:lavender��id:lavender��id:cmap fund��id:cmap fund��
id:saguaro��
id:saguaro��id:emso��id:emso��id:iris��id:iris��id:lilac��id:lilac��id:lavender��id:lavender��id:cmap fund��id:cmap fund��id:emso��id:emso��id:lilac��id:lilac��id:lavender��id:lavender��id:cmap fund��id:cmap fund��
id:saguaro��
id:saguaro��id:iris��id:iris��id:fmap long only��id:lavender��id:cmap fund��
id:saguaro��id:emso��id:iris��id:lilac��id:cmap fund��id:cmap fund��
id:saguaro��
id:saguaro��id:emso��id:emso��id:iris��id:iris��id:lilac��id:lilac��id:lavender��id:lavender��id:lavender��id:lavender��id:cmap fund��id:cmap fund��
id:saguaro��
id:saguaro��id:emso��id:emso��id:iris��id:iris��id:lilac��id:lilac�j%  j%  j%  j%  j%  �	id:pquinn��	id:pquinn��	id:pquinn��	id:pquinn��	id:pquinn��	id:pquinn��	id:pquinn��	id:pquinn��	id:pquinn��	id:pquinn��	id:pquinn��id:gswallow��id:gswallow��id:gswallow��id:gswallow��id:gswallow��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��YM      �
id:ejayaweera��
id:ejayaweera��id:ofaltintrager��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��id:ofaltintrager��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera��
id:ejayaweera�j%  j%  j%  j%  j%  �bond�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �cds single stock�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j%  j%  j%  j%  j%  ]�(}�(j
  �USP3579ECN50�j  �instrumentDetails.instrument�j  j7  u}�(j
  �USP3579ECN50USDXXXX�j  j�  j  j7  ue]�(}�(j
  j�  j  j�  j  j7  u}�(j
  �USP3579ECN50USDXXXX�j  j�  j  j7  ue]�(}�(j
  j�  j  j�  j  j7  u}�(j
  �USP3579ECN50USDXXXX�j  j�  j  j7  ue]�(}�(j
  j�  j  j�  j  j7  u}�(j
  �USP3579ECN50USDXXXX�j  j�  j  j7  ue]�(}�(j
  j�  j  j�  j  j7  u}�(j
  �USP3579ECN50USDXXXX�j  j�  j  j7  ue]�(}�(j
  j�  j  j�  j  j7  u}�(j
  �USP3579ECN50USDXXXX�j  j�  j  j7  ue]�(}�(j
  j�  j  j�  j  j7  u}�(j
  �USP3579ECN50USDXXXX�j  j�  j  j7  ue]�(}�(j
  j�  j  j�  j  j7  u}�(j
  �USP3579ECN50USDXXXX�j  j�  j  j7  ue]�(}�(j
  j�  j  j�  j  j7  u}�(j
  �USP3579ECN50USDXXXX�j  j�  j  j7  ue]�(}�(j
  j�  j  j�  j  j7  u}�(j
  �USP3579ECN50USDXXXX�j  j�  j  j7  ue]�(}�(j
  j�  j  j�  j  j7  u}�(j
  �USP3579ECN50USDXXXX�j  j�  j  j7  ue]�}�(j
  � XXXX|US900123AL40|CDS|2025-12-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US900123AL40|CDS|2025-12-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US900123AL40|CDS|2025-12-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US900123AL40|CDS|2025-12-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US900123AL40|CDS|2025-12-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US836205AR58|CDS|2023-03-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US836205AR58|CDS|2023-03-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US836205AR58|CDS|2023-03-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US836205AR58|CDS|2023-03-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US836205AR58|CDS|2023-03-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US836205AR58|CDS|2023-03-20�j  j�  j  j7  ua]�(}�(j
  �US195325DZ51�j  j�  j  j7  u}�(j
  �US195325DZ51USDXXXX�j  j�  j  j7  ue]�(}�(j
  �USL626A6AA24�j  j�  j  j7  u}�(j
  �USL626A6AA24USDXXXX�j  j�  j  j7  ue]�(}�(j
  j	  j  j�  j  j7  u}�(j
  �USL626A6AA24USDXXXX�j  j�  j  j7  ue]�(}�(j
  j	  j  j�  j  j7  u}�(j
  �USL626A6AA24USDXXXX�j  j�  j  j7  ue]�(}�(j
  j	  j  j�  j  j7  u}�(j
  �USL626A6AA24USDXXXX�j  j�  j  j7  ue]�(}�(j
  j	  j  j�  j  j7  u}�(j
  �USL626A6AA24USDXXXX�j  j�  j  j7  ue]�(}�(j
  j	  j  j�  j  j7  u}�(j
  �USL626A6AA24USDXXXX�j  j�  j  j7  ue]�(}�(j
  �US71647NBH17�j  j�  j  j7  u}�(j
  �US71647NBH17USDXXXX�j  j�  j  j7  ue]�(}�(j
  j,	  j  j�  j  j7  u}�(j
  �US71647NBH17USDXXXX�j  j�  j  j7  ue]�(}�(j
  j,	  j  j�  j  j7  u}�(j
  �US71647NBH17USDXXXX�j  j�  j  j7  ue]�(}�(j
  j,	  j  j�  j  j7  u}�(j
  �US71647NBH17USDXXXX�j  j�  j  j7  ue]�(}�(j
  j,	  j  j�  j  j7  u}�(j
  �US71647NBH17USDXXXX�j  j�  j  j7  ue]�(}�(j
  j,	  j  j�  j  j7  u}�(j
  �US71647NBH17USDXXXX�j  j�  j  j7  ue]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US105756BV13|CDS|2025-06-20�j  j�  j  j7  ua]�(}�(j
  j�  j  j�  j  j7  u}�(j
  �USP3579ECN50USDXXXX�j  j�  j  j7  ue]�(}�(j
  �US71654QCP54�j  j�  j  j7  u}�(j
  �US71654QCP54USDXXXX�j  j�  j  j7  ue]�(}�(j
  j�	  j  j�  j  j7  u}�(j
  �US71654QCP54USDXXXX�j  j�  j  j7  ue]�(}�(j
  j�	  j  j�  j  j7  u}�(j
  �US71654QCP54USDXXXX�j  j�  j  j7  ue]�(}�(j
  j�	  j  j�  j  j7  u}�(j
  �US71654QCP54USDXXXX�j  j�  j  j7  ue]�(}�(j
  j�	  j  j�  j  j7  u}�(j
  �US71654QCP54USDXXXX�j  j�  j  j7  ue]�(}�(j
  j�	  j  j�  j  j7  u}�(j
  �US71654QCP54USDXXXX�j  j�  j  j7  ue]�}�(j
  � XXXX|US195325BB02|CDS|2025-09-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US195325BB02|CDS|2025-09-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US195325BB02|CDS|2025-09-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US195325BB02|CDS|2025-09-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US195325BB02|CDS|2025-09-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US195325BB02|CDS|2025-09-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US195325BB02|CDS|2025-09-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US195325BB02|CDS|2025-09-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US195325BB02|CDS|2025-09-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US195325BB02|CDS|2025-09-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US195325BB02|CDS|2025-09-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US195325BB02|CDS|2025-09-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US195325BB02|CDS|2025-09-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US195325BB02|CDS|2025-09-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US195325BB02|CDS|2025-09-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US195325BB02|CDS|2025-09-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US195325BB02|CDS|2025-09-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US195325BB02|CDS|2025-09-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US195325BB02|CDS|2025-09-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US195325BB02|CDS|2025-09-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US195325BB02|CDS|2025-09-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US195325BB02|CDS|2025-09-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US195325BB02|CDS|2025-09-20�j  j�  j  j7  ua]�}�(j
  � XXXX|US195325BB02|CDS|2025-09-20�j  j�  j  j7  uaj%  j%  j%  j%  j%  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  �
2025-12-20��
2025-12-20��
2025-12-20��
2025-12-20��
2025-12-20��
2023-03-20��
2023-03-20��
2023-03-20��
2023-03-20��
2023-03-20��
2023-03-20�j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  �
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20��
2025-06-20�j%  j%  j%  j%  j%  j%  j%  �
2025-09-20��
2025-09-20��
2025-09-20��
2025-09-20��
2025-09-20��
2025-09-20��
2025-09-20��
2025-09-20��
2025-09-20��
2025-09-20��
2025-09-20��
2025-09-20��
2025-09-20��
2025-09-20��
2025-09-20��
2025-09-20��
2025-09-20��
2025-09-20��
2025-09-20��
2025-09-20��
2025-09-20��
2025-09-20��
2025-09-20��
2025-09-20�j%  j%  j%  j%  j%  j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  �US900123AL40�js
  js
  js
  js
  �US836205AR58�jt
  jt
  jt
  jt
  jt
  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  �US105756BV13�ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  ju
  j%  j%  j%  j%  j%  j%  j%  �US195325BB02�jv
  jv
  jv
  jv
  jv
  jv
  jv
  jv
  jv
  jv
  jv
  jv
  jv
  jv
  jv
  jv
  jv
  jv
  jv
  jv
  jv
  jv
  jv
  j%  j%  j%  j%  j%  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j	  e(j	  j	  j	  j	  j	  j	  j,	  j,	  j,	  j,	  j,	  j,	  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j�  j�	  j�	  j�	  j�	  j�	  j�	  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  j%  ]�(j  j  e]�(j  j  e]�(j!  j#  e]�(j&  j(  e]�(j+  j-  e]�(j0  j2  j8  j:  j�  j�  e]�(j>  j@  jB  jD  j�  j�  e]�(jG  jI  jK  jM  j�  j�  e]�(jP  jR  jT  jV  j�  j�  e]�(jY  j[  j]  j_  j�  j�  e]�(jb  jd  jf  jh  j�  j�  e]�(jk  jm  jo  jq  j�  j�  e]�(jt  jv  jx  jz  j�  j�  e]�(j}  j  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j	  e]�(j�  j�  j�  j�  j	  e]�(j�  j�  j�  j�  j	  e]�(j�  j�  j�  j�  j
	  e]�(j�  j�  j�  j  j
	  j	  e]�(j  j  j  j
  j	  j	  e]�(j
  j  j  j  j	  j	  e]�(j  j  j  j  j	  j	  e]�(j  j!  j#  j%  j	  j 	  e]�(j(  j*  j,  j.  j#	  j$	  e]�(j1  j3  j5  j7  j'	  j(	  e]�(j:  j=  j?  jA  j+	  j-	  e]�(jD  jF  jH  jJ  j0	  j1	  e]�(jM  jO  jQ  jS  j4	  j5	  e]�(jV  jX  jZ  j\  j8	  j9	  e]�(j_  ja  jc  je  j<	  j=	  e]�(jh  jj  jl  jn  j@	  jA	  e]�(jq  js  ju  jw  jD	  e]�(jz  j|  j~  j�  jG	  e]�(j�  j�  j�  j�  jJ	  e]�(j�  j�  j�  j�  jM	  e]�(j�  j�  j�  j�  jP	  e]�(j�  j�  j�  j�  jS	  e]�(j�  j�  j�  j�  jV	  e]�(j�  j�  j�  j�  jY	  e]�(j�  j�  j�  j�  j\	  e]�(j�  j�  j�  j�  j_	  e]�(j�  j�  j�  j�  jb	  e]�(j�  j�  j�  j�  je	  e]�(j�  j�  j�  j�  jh	  e]�(j�  j�  j�  j�  jk	  e]�(j�  j�  j�  j�  jn	  e]�(j�  j�  j�  j�  jq	  e]�(j  j  j  j  jt	  e]�(j
  j  j  j  jw	  e]�(j  j  j  j  jz	  e]�(j  j  j   j"  j}	  e]�(j%  j'  j)  j+  j�	  e]�(j.  j0  j2  j4  j�	  e]�(j7  j9  j;  j=  j�	  e]�(j@  jB  jD  jF  j�	  e]�(jI  jK  jM  jO  j�	  e]�(jR  jT  jV  jX  j�	  e]�(j[  j]  j_  ja  j�	  e]�(jd  jf  jh  jj  j�	  e]�(jm  jo  jq  js  j�	  e]�(jv  jx  jz  j|  j�	  e]�(j  j�  j�  j�  j�	  e]�(j�  j�  j�  j�  j�	  e]�(j�  j�  j�  j�  j�	  e]�(j�  j�  j�  j�  j�	  e]�(j�  j�  j�  j�  j�	  e]�(j�  j�  j�  j�  j�	  e]�(j�  j�  j�  j�  j�	  e]�(j�  j�  j�  j�  j�	  e]�(j�  j�  j�  j�  j�	  e]�(j�  j�  j�  j�  j�	  e]�(j�  j�  j�  j�  j�	  e]�(j�  j�  j�  j�  j�	  e]�(j�  j�  j�  j�  j�	  j�	  e]�(j�  j�  j�  j�  j�	  j�	  e]�(j�  j�  j  j  j�	  j�	  e]�(j  j  j
  j  j�	  j�	  e]�(j  j  j  j  j�	  j�	  e]�(j  j  j  j  j�	  j�	  e]�(j!  j#  j%  j'  j�	  j�	  e]�(j*  j,  j.  j0  j�	  e]�(j3  j5  j7  j9  j�	  e]�(j<  j>  j@  jB  j�	  e]�(jE  jG  jI  jK  j�	  e]�(jN  jP  jR  jT  j�	  e]�(jW  jY  j[  j]  j�	  e]�(j`  jb  jd  jf  j�	  e]�(ji  jk  jm  jo  j�	  e]�(jr  jt  jv  jx  j�	  e]�(j{  j}  j  j�  j�	  e]�(j�  j�  j�  j�  j�	  e]�(j�  j�  j�  j�  j 
  e]�(j�  j�  j�  j�  j
  e]�(j�  j�  j�  j�  j
  e]�(j�  j�  j�  j�  j	
  e]�(j�  j�  j�  j�  j
  e]�(j�  j�  j�  j�  j
  e]�(j�  j�  j�  j�  j
  e]�(j�  j�  j�  j�  j
  e]�(j�  j�  j�  j�  j
  e]�(j�  j�  j�  j�  j
  e]�(j�  j�  j�  j�  j
  e]�(j�  j�  j�  j�  j!
  e]�(j�  j�  j�  j�  j$
  eet�bhy)��}�(h|hhK ��h��R�(KKq��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(j%  j%  j%  j%  j%  �DOMREP 6 02/22/33�j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  �TURKEY 06/27�j�
  j�
  j�
  j�
  �
SOAF 06/27�j�
  j�
  j�
  j�
  j�
  �COLOM 3 1/4 04/22/32��MCBRAC 7 1/4 06/30/31 - REGS�j�
  j�
  j�
  j�
  j�
  �PETBRA 5.6 01/03/31�j�
  j�
  j�
  j�
  j�
  �BRAZIL 12/26�j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  �PEMEX 6 1/2 01/23/29 EXCH�j�
  j�
  j�
  j�
  j�
  �COLOM 12/26�j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  et�bh�h�)��ube]�(h
h}�(hhhK ��h��R�(KK��h!�]�h+at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h,at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hDat�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h9h?h@hAet�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hHat�bhaNu��R�h
h}�(hhhK ��h��R�(KK.��h!�]�(h%h&h'h(h)h*h0h1h3h4h5h6h7h8h:h;h<h=h>hBhChEhFhGhIhJhKhLhMhNhOhPhQhRhShThUhVhWhXhYhZh[h\h]h^et�bhaNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h_at�bhaNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hz�mgr_locs��builtins��slice���KKK��R�u}�(jj  h�jk  jn  KKK��R�u}�(jj  j  jk  jn  KK	K��R�u}�(jj  j
  jk  jn  K	K
K��R�u}�(jj  j  jk  jn  K
KK��R�u}�(jj  j  jk  jn  K
KK��R�u}�(jj  j*  jk  jn  KK K��R�u}�(jj  j6  jk  hhK ��h��R�(KK��h�i8�����R�(KhoNNNJ����J����K t�b�C                             �t�bu}�(jj  j@  jk  jn  K#K$K��R�u}�(jj  jF  jk  hhK ��h��R�(KK.��j�  �Bp                                                                                                                                                              !       "       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       3       4       5       6       7       8       9       �t�bu}�(jj  j�
  jk  jn  K:K;K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.