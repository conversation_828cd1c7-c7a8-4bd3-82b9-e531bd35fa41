import logging
import os
from pathlib import Path

import pandas as pd
from prefect import context
from swarm.task.auditor import Auditor

from swarm_tasks.order.transformations.pershing.ubp.pershing_order_transformation import (
    PershingUbpOrderTransformations,
)
from swarm_tasks.order.transformations.pershing.ubp.pershing_order_transformation import (
    SourceColumns,
)
from swarm_tasks.order.transformations.pershing.ubp.pershing_order_transformation import (
    TempColumns,
)

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")


class TestPershingUbpOrderTransformations:
    """
    Test suite for ExpersoftUbpOrderTransformations
    """

    def test_end_to_end_transformations(
        self,
        mocker,
        isin_mapping_table: pd.DataFrame,
        source_frame: pd.DataFrame,
        executing_entity_df: pd.DataFrame,
        expected_result: pd.DataFrame,
        auditor: Auditor,
        log: logging.Logger,
    ):
        """Runs an end-to-end test for ExpersoftUbpOrderTransformations using pickled data frames as the
        expected outputs.
        """
        os.environ["SWARM_FILE_URL"] = str(
            f"{TEST_FILES_DIR}pershing_ubp_source_data.pkl"
        )
        # mock values for ISIN mapping file from s3
        mock_df_from_s3 = mocker.patch.object(
            PershingUbpOrderTransformations,
            "_get_instrument_mapping_table_from_s3",
        )
        mock_df_from_s3.return_value = isin_mapping_table
        # Mock a dummy lei as GetTenantLei needs to connect to Elastic
        mock_exec_entity = mocker.patch.object(
            PershingUbpOrderTransformations, "_get_executing_entity"
        )
        mock_exec_entity.return_value = executing_entity_df

        task = PershingUbpOrderTransformations(
            source_frame=source_frame,
            logger=context.get("logger"),
            auditor=auditor,
        )

        result = task.process()

        assert not pd.testing.assert_frame_equal(
            left=result.drop(["sourceKey"], axis=1).astype("str"),
            right=expected_result.drop(["sourceKey"], axis=1),
        )

    def test_pre_process_initial_quantity_logic(
        self,
        source_frame: pd.DataFrame,
        auditor: Auditor,
    ):
        test_data = pd.DataFrame(
            {
                SourceColumns.CLT_MKT: [
                    "C",
                    "M",
                    "C",
                    "M",
                    "M",
                    "C",
                    pd.NA,
                    "C",
                    "M",
                    "M",
                    "C",
                    "M",
                    "M",
                ],
                SourceColumns.CMN_REF: [
                    "id1",
                    "id1",
                    "id2",
                    "id2",
                    "id2",
                    "id3",
                    "id3",
                    "id4",
                    pd.NA,
                    "id4",
                    "id5",
                    "id5",
                    "id5",
                ],
                SourceColumns.STK_QTY: [
                    500,
                    500,
                    200,
                    100,
                    200,
                    200,
                    200,
                    400,
                    200,
                    200,
                    600,
                    300,
                    pd.NA,
                ],
            }
        )

        task = PershingUbpOrderTransformations(
            source_frame=test_data,
            logger=context.get("logger"),
            auditor=auditor,
        )

        task._pre_process_temp_initial_quantity()

        expected = pd.DataFrame(
            {
                TempColumns.INITIAL_QUANTITY: [
                    pd.NA,
                    500.0,
                    pd.NA,
                    300.0,
                    300.0,
                    pd.NA,
                    pd.NA,
                    pd.NA,
                    pd.NA,
                    200.0,
                    pd.NA,
                    300.0,
                    pd.NA,
                ]
            }
        )

        assert not pd.testing.assert_frame_equal(
            left=task.pre_process_df, right=expected, check_dtype=False
        )
