import logging
from pathlib import Path

import pandas as pd
import pytest
from swarm.task.auditor import Auditor

from swarm_tasks.order.transformations.pershing.ubp.pershing_order_transformation import (
    SourceColumns,
)
from swarm_tasks.order.transformations.pershing.ubp.pershing_order_transformation import (
    TempColumns,
)

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
TEST_FILE_PATH = TEST_FILES_DIR.joinpath("pershing_ubp_source_data.pkl")
TEST_ISIN_MAPPING_FILE_PATH = TEST_FILES_DIR.joinpath(
    "pershing_ubp_isin_mapping_data.pkl"
)
EXPECTED_RESULT_PICKLE_PATH = TEST_FILES_DIR.joinpath("pershing_order_ubp_result.pkl")


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


@pytest.fixture()
def log():
    logger = logging.getLogger("dummy")
    logger.setLevel(logging.INFO)
    return logger


@pytest.fixture()
def source_frame() -> pd.DataFrame:
    df = pd.read_pickle(TEST_FILE_PATH).astype(str)
    df = df.set_index("__swarm_raw_index__")
    for col in [
        SourceColumns.PRICE,
        SourceColumns.MKT_CONS,
        SourceColumns.CLT_CONS,
    ]:
        df[col] = df[col].astype("float")
    return df


@pytest.fixture()
def isin_mapping_table() -> pd.DataFrame:
    df = pd.read_pickle(TEST_ISIN_MAPPING_FILE_PATH).astype(str)
    return df


@pytest.fixture()
def expected_result():
    df = pd.read_pickle(EXPECTED_RESULT_PICKLE_PATH).astype("str")
    return df


@pytest.fixture()
def executing_entity_df():
    source_frame = pd.read_pickle(TEST_FILE_PATH).astype(str)
    df = pd.DataFrame(
        data="lei:213800NWE7NNQGFMET63",
        index=source_frame["__swarm_raw_index__"],
        columns=[TempColumns.EXECUTING_ENTITY_WITH_LEI],
    )
    return df
