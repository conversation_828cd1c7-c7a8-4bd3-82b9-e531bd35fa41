import logging

import numpy as np
import pandas as pd
import pytest
from se_core_tasks.core.auditor import Auditor
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order.static import Venue

from swarm_tasks.order.transformations.metatrader.mt5.mt5_deals_transformations import (
    MT5DealsTransformations,
)
from swarm_tasks.order.transformations.metatrader.mt5.mt5_order_transformations import (
    MT5OrderTransformations,
)
from swarm_tasks.order.transformations.metatrader.mt5.static import (
    SourceInstrumentColumns,
)

TRANSFORMATION_CLASSES = (MT5OrderTransformations, MT5DealsTransformations)
logger_ = logging.getLogger(__name__)


class TestMT5SharedTransformations:
    @pytest.mark.parametrize("transformation_class", TRANSFORMATION_CLASSES)
    def test_transaction_details_price_currency(self, transformation_class):

        # C_QUOTED_CCY is the priority, then fallback to the other columns by order of priority
        source_df = pd.DataFrame(
            {
                SourceInstrumentColumns.C_QUOTED_CCY: [
                    "USc",
                    "USc",
                    pd.NA,
                    np.nan,
                    np.nan,
                ],
                SourceInstrumentColumns.M_PRICE_CURRENCY: [
                    pd.NA,
                    "GBp",
                    "USc",
                    pd.NA,
                    "DOGE",
                ],
                SourceInstrumentColumns.C_BASE_CCY: [pd.NA, "GBp", pd.NA, "USc", "EUR"],
            }
        )
        expected_result_df = pd.DataFrame(
            {
                OrderColumns.TRANSACTION_DETAILS_PRICE_CURRENCY: [
                    "USD",
                    "USD",
                    "USD",
                    "USD",
                    "EUR",
                ],
            }
        )
        transformation_instance = transformation_class(
            source_frame=source_df, auditor=Auditor(), logger=logger_
        )
        transformation_instance._transaction_details_price_currency()

        pd.testing.assert_frame_equal(
            transformation_instance.target_df, expected_result_df
        )

    @pytest.mark.parametrize("transformation_class", TRANSFORMATION_CLASSES)
    def test_transaction_details_quantity_currency(self, transformation_class):

        # quantityCurrency can only be populated with C_BASE_CCY
        # whenever M_ASSET is one of `fx` or `currency`.
        source_df = pd.DataFrame(
            {
                SourceInstrumentColumns.M_ASSET: [
                    "Fx",
                    "FX",
                    "currency",
                    np.nan,
                    "foo",
                ],
                SourceInstrumentColumns.C_BASE_CCY: ["USc", "USc", "USc", "USc", "USc"],
            }
        )
        expected_result_df = pd.DataFrame(
            {
                OrderColumns.TRANSACTION_DETAILS_QUANTITY_CURRENCY: [
                    "USD",
                    "USD",
                    "USD",
                    pd.NA,
                    pd.NA,
                ],
            }
        )
        transformation_instance = transformation_class(
            source_frame=source_df, auditor=Auditor(), logger=logger_
        )
        transformation_instance._transaction_details_quantity_currency()

        assert transformation_instance.target_df.equals(expected_result_df)

    @pytest.mark.parametrize("transformation_class", TRANSFORMATION_CLASSES)
    def test_transaction_details_venue(self, transformation_class):

        source_df = pd.DataFrame(
            {
                SourceInstrumentColumns.T_ID_PRIM_EXCH_MIC: [
                    "ABCD",
                    "ABCDEFG",
                    "AB",
                    "ooTC",
                    np.nan,
                    "OOTC",
                ],
            }
        )
        expected_result_df = pd.DataFrame(
            {
                OrderColumns.TRANSACTION_DETAILS_VENUE: [
                    "ABCD",  # valid MIC
                    Venue.XXXX,  # invalid MIC because it's not 4 characters long (it's longer)
                    Venue.XXXX,  # invalid MIC because it's not 4 characters long (it's shorter)
                    Venue.XXXX,  # case-insensitive OOTC MIC
                    Venue.XXXX,  # null MIC
                    Venue.XXXX,  # OOTC MIC
                ],
            }
        )
        transformation_instance = transformation_class(
            source_frame=source_df, auditor=Auditor(), logger=logger_
        )
        transformation_instance._transaction_details_ultimate_venue()
        transformation_instance._transaction_details_venue()

        assert transformation_instance.target_df.loc[
            :, [OrderColumns.TRANSACTION_DETAILS_VENUE]
        ].equals(expected_result_df)
