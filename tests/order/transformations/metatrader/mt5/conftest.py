from pathlib import Path

import pandas as pd
import pytest

from swarm_tasks.order.transformations.metatrader.mt5.static import SourceDealsColumns
from swarm_tasks.order.transformations.metatrader.mt5.static import SourceOrderColumns

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")


@pytest.fixture()
def source_orders_frame():
    df = pd.read_csv(f"{TEST_FILES_DIR}/sample_orders.csv", dtype="str")
    for col in [
        SourceOrderColumns.TIMESTAMP,
        SourceOrderColumns.VOLUME_INITIAL_EXT,
        SourceOrderColumns.CONTRACT_SIZE,
        SourceOrderColumns.VOLUME_INITIAL,
    ]:
        df.loc[:, col] = df.loc[:, col].astype(float)
    return df


@pytest.fixture()
def source_deals_frame():
    df = pd.read_csv(f"{TEST_FILES_DIR}/sample_deals.csv", dtype="str")
    df[SourceDealsColumns.VOLUME_EXT] = df[SourceDealsColumns.VOLUME_EXT].astype(float)
    for col in [
        SourceDealsColumns.VOLUME_EXT,
        SourceDealsColumns.VOLUME,
        SourceDealsColumns.CONTRACT_SIZE,
    ]:
        df.loc[:, col] = df.loc[:, col].astype(float)
    return df


@pytest.fixture()
def source_instrument_frame():
    df = pd.read_csv(f"{TEST_FILES_DIR}/InstrumentData.csv", dtype="str")
    return df


@pytest.fixture()
def expected_result_orders():
    df = pd.read_pickle(f"{TEST_FILES_DIR}/expected_result_orders.pkl")
    return df


@pytest.fixture()
def expected_result_orders_onefinancial():
    df = pd.read_pickle(f"{TEST_FILES_DIR}/expected_result_orders_onefinancial.pkl")
    return df


@pytest.fixture()
def expected_result_orders_oanda():
    df = pd.read_pickle(f"{TEST_FILES_DIR}/expected_result_orders_oanda.pkl")
    return df


@pytest.fixture()
def expected_result_deals():
    df = pd.read_pickle(f"{TEST_FILES_DIR}/expected_result_deals.pkl")
    return df


@pytest.fixture()
def expected_result_deals_onefinancial():
    df = pd.read_pickle(f"{TEST_FILES_DIR}/expected_result_deals_onefinancial.pkl")
    return df


@pytest.fixture()
def expected_result_deals_oanda():
    df = pd.read_pickle(f"{TEST_FILES_DIR}/expected_result_deals_oanda.pkl")
    return df
