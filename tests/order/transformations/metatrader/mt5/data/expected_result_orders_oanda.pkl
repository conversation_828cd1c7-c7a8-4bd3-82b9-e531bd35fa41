��:      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�buySell��dataSourceName��date��!executionDetails.buySellIndicator��executionDetails.limitPrice��executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo�� executionDetails.tradingCapacity��id��__meta_model__��"orderIdentifiers.aggregatedOrderId��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode�� priceFormingData.initialQuantity��"priceFormingData.remainingQuantity��sourceIndex��	sourceKey��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��timestamps.validityPeriod��#transactionDetails.buySellIndicator��transactionDetails.positionId�� transactionDetails.priceCurrency��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��transactionDetails.recordType��"transactionDetails.tradingCapacity�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers��__EXECUTING_ENTITY__��
__CLIENT__��__INVESTMENT_DEC_WITHIN_FIRM__��__TEMP_BUY_SELL_INDICATOR__��__TEMP_INST_ID_SYMBOL__��=__TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_CONTRACT_TYPE__��<__TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_PRODUCT_TYPE__��;__TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_ASSET_CLASS__��?__TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_BASE_PRODUCT__��F__TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_FURTHER_SUB_PRODUCT__��__TEMP_INST_ID_DELIVERY_TYPE__��!__TEMP_INST_ID_PRICE_MULTIPLIER__��$__TEMP_INST_ID_EXT_EXCHANGE_SYMBOL__��(__TEMP_INST_ID_INSTRUMENT_ID_CODE_TYPE__��__TEMP_INST_ID_PRICE_NOTATION__��*__TEMP_INST_ID_INSTRUMENT_CLASSIFICATION__��%__TEMP_INST_ID_INSTRUMENT_FULL_NAME__��)__TEMP_INST_ID_UNDERLYING_INSTRUMENT_ID__��&__TEMP_INST_ID_UNDERLYING_INDEX_NAME__��__TEMP_INST_ASSET_CLASS__��(__TEMP_INST_UNIQUE_ID_M_ISIN_POPULATED__��TEMP_ASSET_CLASS��(__TEMP_INST_UNIQUE_ID_T_ISIN_POPULATED__��*__TEMP_INST_UNIQUE_ID_ISIN_NOT_POPULATED__��#__TEMP_INST_UNIQUE_ID_CASH_EQUITY__��%__TEMP_INST_ID_INSTRUMENT_UNIQUE_ID__��$__TEMP_IS_CREATED_THROUGH_FALLBACK__��TEMP_ORDER_STATUS��ContractSize��TEMP_XXXX_VENUE��isSynthetic�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(hqN�start�K �stop�K�step�Ku��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�US30��US100��US100�et�b�_dtype�h~�StringDtype���)��ub�numpy.core.numeric��_frombuffer���(�       �h�b1�����R�(Kh"NNNJ����J����K t�bKK���C�t�R�h�(�`                               ��(\���?�������?�������?     ��@     @�@     @�@      �?      �?      �?�h�f8�����R�(K�<�NNNJ����J����K t�bKK��h�t�R�h�(�0                                                  �h�i8�����R�(Kh�NNNJ����J����K t�bKK��h�t�R�hhK ��h��R�(KKBK��h!�]�(�1��2�h��MetaTrader5 - Orders�h�h��
2022-02-14��
2022-02-14��
2022-02-14��BUYI��SELL�h��NEWO�h��CAME��Market�h�h��dModify Flags - 0, Action - Comment, Login - 4006656, Internal Client ID - 4006656, mt5_symbol - US30��eModify Flags - 0, Action - Comment, Login - 4006545, Internal Client ID - 4006656, mt5_symbol - US100��eModify Flags - 0, Action - Comment, Login - 4006545, Internal Client ID - 4006656, mt5_symbol - US100��DEAL�h�hŌ260242|260242|4006656��263222|263222|4006656��263222|263222|4006656��Order�hɌ
OrderState��260242��263222�ȟ260242-O��263222-O�h�h�h�hȌy/Users/<USER>/Desktop/steeleye/swarm-tasks/tests/order/transformations/metatrader/mt5/datamt5-ogm-deals-2022-02-14.csv�h�hό2022-02-14T13:57:10.597000Z��2022-02-14T21:28:37.145000Z��2022-02-14T21:28:37.145000Z�h�h�h�h�h�h�]�h�a]�h�a]�h�ah�h�h�h�h�ȟUSD�h�h֌pandas._libs.missing��NA���h�hٌUNIT�h�hڌClient Side�h�h�h�h�hŌXXXX�h�h�h�h�h�]�}�(�labelId��%US Wall St 30-equity shares-CASH/spot��path��instrumentDetails.instrument��type��se_elastic_schema.static.market��IdentifierType����OBJECT���R�ua]�}�(hߌUS Nas 100-indices-cfd�h�h�h�h�ua]�}�(hߌUS Nas 100-indices-cfd�h�h�h�h�ua]�(}�(hߌ
id:4006656�h�buyer�h�h�ARRAY���R�u}�(hߌid:ogm�h�reportDetails.executingEntity�h�h�u}�(hߌid:ogm�h�seller�h�h�u}�(hߌid:ogm�h�sellerDecisionMaker�h�h�u}�(hߌid:ogm�h�counterparty�h�h�u}�(hߌ
id:4006656�h�clientIdentifiers.client�h�h�u}�(hߌ
id:4006656�h�trader�h�h�ue]�(}�(hߌid:ogm�h�h�h�h�u}�(hߌid:ogm�h�buyerDecisionMaker�h�h�u}�(hߌid:ogm�h�h�h�h�u}�(hߌ
id:4006656�h�h�h�h�u}�(hߌid:ogm�h�j  h�h�u}�(hߌ
id:4006656�h�j  h�h�u}�(hߌ
id:4006656�h�j  h�h�ue]�(}�(hߌid:ogm�h�h�h�h�u}�(hߌid:ogm�h�j  h�h�u}�(hߌid:ogm�h�h�h�h�u}�(hߌ
id:4006656�h�h�h�h�u}�(hߌid:ogm�h�j  h�h�u}�(hߌ
id:4006656�h�j  h�h�u}�(hߌ
id:4006656�h�j  h�h�ue�id:ogm��id:ogm��id:ogm��
id:4006656��id:ogm��id:ogm��id:ogm��
id:4006656��
id:4006656��id:ogm��id:ogm��id:ogm�hٌid:ogm��id:ogm��id:ogm�h�h�h�h�h�h�h�hٌ
id:4006656��
id:4006656��
id:4006656��
id:4006656��
id:4006656��
id:4006656�]�(h�h�h�h�h�j   j  j  e]�(h�j
  j  j  j  j  j  j  e]�(h�j  j  j  j   j"  j$  j&  e�id:ogm�j@  j@  �
id:4006656��
id:4006656��
id:4006656�h�h�h�h�h�h��OT��CD�jE  hٌCFD�jF  �EQ�jG  jG  h�h�h�h�h�hٌCASH�jH  jH  �
US Wall St 30��
US Nas 100�jJ  �OTHR�jK  jK  �MONE�jL  jL  �JEIXCC�jM  jM  h�h�h�example_isi2��US6311011026�jO  �US30��US100�jQ  h�jF  jF  �XXXXUS2605661048USD��XXXXUS6311011026USDCFD��XXXXUS6311011026USDCFD�� �jU  jU  �XXXXexample_isi2USD��
XXXXUSDCFD��
XXXXUSDCFD��XXXXUS30��XXXXUS100CFD��XXXXUS100CFD��example_isi2USDXXXX��USDXXXX��USDXXXX�j\  jS  jT  G�      G�      h��XXXX�j_  j_  et�bh�(�          �h�b1�����R�(Kh"NNNJ����J����K t�bKK��h�t�R�e]�(h
h}�(hhhK ��h��R�(KK��h!�]�hUat�bhqNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hkat�bhqNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h)h3h4hmet�bhqNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h5h\et�bhqNu��R�h
h}�(hhhK ��h��R�(KKB��h!�]�(h%h&h'h(h*h+h,h-h.h/h0h1h2h6h7h8h9h:h;h<h=h>h?h@hAhBhChDhEhFhGhHhIhJhKhLhMhNhOhPhQhRhShThVhWhXhYhZh[h]h^h_h`hahbhchdhehfhghhhihjhlhnet�bhqNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hoat�bhqNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���K0K1K��R�u}�(j�  h�j�  j�  KFKGK��R�u}�(j�  h�j�  h�(�                             H       �h�i8�����R�(Kh�NNNJ����J����K t�bK��h�t�R�u}�(j�  h�j�  j�  KK^K'��R�u}�(j�  h�j�  h�(�                                                               	       
                     
                                                                                                                        !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       1       2       3       4       5       6       8       9       :       ;       <       =       >       ?       @       A       B       C       D       E       G       I       �j�  KB��h�t�R�u}�(j�  jh  j�  j�  KJKKK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.