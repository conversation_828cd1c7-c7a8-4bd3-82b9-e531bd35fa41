import logging
import os
from pathlib import Path
from unittest.mock import patch

import pandas as pd
import pytest
from mock import MagicMock
from prefect.engine.signals import SKIP
from swarm.task.auditor import Auditor

from swarm_tasks.io.read.aws import df_from_s3_csv
from swarm_tasks.order.transformations.metatrader.mt5.mt5_order_transformations import (
    MT5OrderTransformations,
)
from swarm_tasks.order.transformations.metatrader.mt5.oanda.mt5_oanda_order_transformations import (
    MT5OandaOrderTransformations,
)
from swarm_tasks.order.transformations.metatrader.mt5.onefinancial.mt5_onefinancial_order_transformations import (
    MT5OneFinancialOrderTransformations,
)
from swarm_tasks.order.transformations.metatrader.mt5.static import (
    SourceInstrumentColumns,
)
from swarm_tasks.order.transformations.metatrader.mt5.static import SourceOrderColumns
from swarm_tasks.order.transformations.metatrader.mt5.static import TempPartyIDs
from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI


SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data/")


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


@pytest.fixture()
def log():
    logger = logging.getLogger("dummy")
    logger.setLevel(logging.INFO)
    return logger


@pytest.fixture()
def mock_get_tenant_lei(mocker, expected_result_orders):
    mock_get_tenant_lei = mocker.patch.object(GetTenantLEI, "process")
    tenant_lei_df = pd.DataFrame(index=expected_result_orders.index)
    tenant_lei_df = tenant_lei_df.assign(
        **{TempPartyIDs.EXECUTING_ENTITY: "lei:sample_lei"}
    )
    mock_get_tenant_lei.return_value = tenant_lei_df
    return mock_get_tenant_lei


class TestMT5OrderTransformations:
    """
    Test for MT5OrderTransformations
    """

    @patch.object(df_from_s3_csv.DfFromS3Csv, "process")
    def test_end_to_end_transformations(
        self,
        mock_df_from_s3: MagicMock,
        mock_get_tenant_lei: MagicMock,
        source_orders_frame: pd.DataFrame,
        source_instrument_frame: pd.DataFrame,
        expected_result_orders: pd.DataFrame,
        auditor: Auditor,
        log: logging.Logger,
    ):
        os.environ["SWARM_FILE_URL"] = str(f"{TEST_FILES_DIR}sample_orders.csv")
        mock_df_from_s3.return_value = source_instrument_frame
        task_order_mt5 = MT5OrderTransformations(
            source_frame=source_orders_frame, auditor=auditor, logger=log
        )
        result_order_mt5 = task_order_mt5.process()

        pd.testing.assert_frame_equal(
            result_order_mt5.drop(["sourceKey"], axis=1).reindex(
                sorted(result_order_mt5.columns), axis=1
            ),
            expected_result_orders.drop(["sourceKey"], axis=1).reindex(
                sorted(expected_result_orders.columns), axis=1
            ),
            check_dtype=False,
        )

    @patch.object(df_from_s3_csv.DfFromS3Csv, "process")
    def test_end_to_end_transformations_onefinancial(
        self,
        mock_df_from_s3: MagicMock,
        mock_get_tenant_lei: MagicMock,
        source_orders_frame: pd.DataFrame,
        source_instrument_frame: pd.DataFrame,
        expected_result_orders_onefinancial: pd.DataFrame,
        auditor: Auditor,
        log: logging.Logger,
    ):
        os.environ["SWARM_FILE_URL"] = str(f"{TEST_FILES_DIR}sample_orders.csv")
        mock_df_from_s3.return_value = source_instrument_frame
        task_order_mt5_one_financial = MT5OneFinancialOrderTransformations(
            source_frame=source_orders_frame, auditor=auditor, logger=log
        )
        result_order_mt5 = task_order_mt5_one_financial.process()

        pd.testing.assert_frame_equal(
            result_order_mt5.drop(["sourceKey"], axis=1).reindex(
                sorted(result_order_mt5.columns), axis=1
            ),
            expected_result_orders_onefinancial.drop(["sourceKey"], axis=1).reindex(
                sorted(expected_result_orders_onefinancial.columns), axis=1
            ),
            check_dtype=False,
        )

    @patch.object(df_from_s3_csv.DfFromS3Csv, "process")
    def test_end_to_end_transformations_oanda(
        self,
        mock_df_from_s3: MagicMock,
        source_orders_frame: pd.DataFrame,
        source_instrument_frame: pd.DataFrame,
        expected_result_orders_oanda: pd.DataFrame,
        auditor: Auditor,
        log: logging.Logger,
    ):
        os.environ["SWARM_FILE_URL"] = str(
            f"{TEST_FILES_DIR}mt5-ogm-deals-2022-02-14.csv"
        )
        mock_df_from_s3.return_value = source_instrument_frame
        task_order_mt5_oanda = MT5OandaOrderTransformations(
            source_frame=source_orders_frame, auditor=auditor, logger=log
        )
        result_order_mt5 = task_order_mt5_oanda.process()

        pd.testing.assert_frame_equal(
            result_order_mt5.drop(["sourceKey"], axis=1).reindex(
                sorted(result_order_mt5.columns), axis=1
            ),
            expected_result_orders_oanda.drop(["sourceKey"], axis=1).reindex(
                sorted(expected_result_orders_oanda.columns), axis=1
            ),
            check_dtype=False,
        )

    @patch.object(df_from_s3_csv.DfFromS3Csv, "process")
    def test_skip_execution(
        self,
        mock_df_from_s3: MagicMock,
        source_orders_frame: pd.DataFrame,
        source_instrument_frame: pd.DataFrame,
        expected_result_orders: pd.DataFrame,
        auditor: Auditor,
        log: logging.Logger,
    ):
        os.environ["SWARM_FILE_URL"] = str(f"{TEST_FILES_DIR}sample_orders.csv")
        mock_df_from_s3.return_value = pd.DataFrame(index=source_orders_frame.index)
        task_order_mt5 = MT5OrderTransformations(
            source_frame=source_orders_frame, auditor=auditor, logger=log
        )
        with pytest.raises(SKIP) as e:
            task_order_mt5.process()

        assert e.type == SKIP

    def test_add_rows_for_order_states(
        self,
        source_orders_frame: pd.DataFrame,
        expected_result_orders: pd.DataFrame,
        auditor: Auditor,
        log: logging.Logger,
    ):
        task_order_mt5 = MT5OrderTransformations(
            source_frame=source_orders_frame, auditor=auditor, logger=log
        )
        task_order_mt5.source_frame = source_orders_frame
        task_order_mt5._add_rows_for_order_states()
        # input_frame had 2 rows and expected output should have more one row
        assert task_order_mt5.source_frame.shape[0] == 3

    @patch.object(df_from_s3_csv.DfFromS3Csv, "process")
    def test_some_instruments_missing(
        self,
        mock_df_from_s3: MagicMock,
        source_orders_frame: pd.DataFrame,
        source_instrument_frame: pd.DataFrame,
        expected_result_orders: pd.DataFrame,
        auditor: Auditor,
        log: logging.Logger,
    ):
        instrument_mask = (
            source_instrument_frame[SourceInstrumentColumns.C_MT5_ID] == "US30"
        )
        mock_df_from_s3.return_value = source_instrument_frame.loc[~instrument_mask]
        task_order_mt5 = MT5OrderTransformations(
            source_frame=source_orders_frame, auditor=auditor, logger=log
        )
        task_order_mt5.source_frame = source_orders_frame
        task_order_mt5._fetch_instrument_data_from_s3()
        source_frame_mask = source_orders_frame[SourceOrderColumns.SYMBOL] == "US30"
        expected_result = source_orders_frame.merge(
            source_instrument_frame.drop_duplicates(
                subset=[SourceInstrumentColumns.C_MT5_ID]
            ),
            how="left",
            left_on=SourceOrderColumns.SYMBOL,
            right_on=SourceInstrumentColumns.C_MT5_ID,
        )
        assert task_order_mt5.source_frame.equals(
            expected_result.loc[~source_frame_mask]
        )
