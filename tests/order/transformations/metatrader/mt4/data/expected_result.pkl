��o      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKM��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo�� executionDetails.tradingCapacity��	_order.id��_orderState.id��_order.__meta_model__��_orderState.__meta_model__��orderIdentifiers.orderIdCode��-_orderState.orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��priceFormingData.price��+_orderState.priceFormingData.tradedQuantity��*_orderState.reportDetails.transactionRefNo��sourceIndex��	sourceKey��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��timestamps.tradingDateTime��#transactionDetails.buySellIndicator��!transactionDetails.positionEffect��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��'_orderState.transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime��transactionDetails.trailId�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers��__EXECUTING_ENTITY__��
__CLIENT__��__INVESTMENT_DEC_WITHIN_FIRM__��$__TEMP_INST_ID_EXT_EXCHANGE_SYMBOL__��=__TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_CONTRACT_TYPE__��<__TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_PRODUCT_TYPE__��;__TEMP_INST_ID_INSTRUMENT_CLASSIFICATION_EMIR_ASSET_CLASS__��?__TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_BASE_PRODUCT__��F__TEMP_INST_ID_COMMODITY_AND_EMISSION_ALLOWANCES_FURTHER_SUB_PRODUCT__��__TEMP_INST_ID_DELIVERY_TYPE__��!__TEMP_INST_ID_PRICE_MULTIPLIER__��(__TEMP_INST_ID_INSTRUMENT_ID_CODE_TYPE__��__TEMP_INST_ID_PRICE_NOTATION__��*__TEMP_INST_ID_INSTRUMENT_CLASSIFICATION__��%__TEMP_INST_ID_INSTRUMENT_FULL_NAME__��)__TEMP_INST_ID_UNDERLYING_INSTRUMENT_ID__��&__TEMP_INST_ID_UNDERLYING_INDEX_NAME__��__TEMP_INST_ASSET_CLASS__��(__TEMP_INST_UNIQUE_ID_M_ISIN_POPULATED__��__TEMP_ASSET_CLASS__��(__TEMP_INST_UNIQUE_ID_T_ISIN_POPULATED__��*__TEMP_INST_UNIQUE_ID_ISIN_NOT_POPULATED__��%__TEMP_INST_ID_INSTRUMENT_UNIQUE_ID__��$__TEMP_IS_CREATED_THROUGH_FALLBACK__��&_orderState.__TEMP_PARENT_META_MODEL__�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(hsN�start�K �stop�K�step�Ku��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h!�]�(�USDJPY��BCHUSD��USDJPY��BCHUSD�et�b�_dtype�h��StringDtype���)��ub�numpy.core.numeric��_frombuffer���(�       �h�b1�����R�(Kh"NNNJ����J����K t�bKK���C�t�R�h�(�`             �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?�h�f8�����R�(K�<�NNNJ����J����K t�bKK��h�t�R�h�(�@                                                                �h�i8�����R�(Kh�NNNJ����J����K t�bKK��h�t�R�hhK ��h��R�(KKEK��h!�]�(�2��1�h�h�h�h�h�h��MetaTrader4 - Trades�h�h�h��
2022-01-12��
2022-01-12��
2022-01-12��
2022-01-12��SELL��BUYI�h�h��NEWO�h�h�h��FILL�h�h�h��Market�h�h�h��.Magic Number - 0 SL - 0 TP - 0 Margin Rate - 1��.Magic Number - 0 SL - 0 TP - 0 Margin Rate - 1��.Magic Number - 0 SL - 0 TP - 0 Margin Rate - 1��.Magic Number - 0 SL - 0 TP - 0 Margin Rate - 1��DEAL�h�h�hƌ1855352|Open��1851545|Open��
1855352|Close��
1851545|Close�h�h�h�hʌOrder�h�h�hˌ
OrderState�h�h�h�h�h�h�h�h�h�h�h�G@\�z�G�G@w"=p��
G@\�5?|�G@wz�G�h�h�h�hʌn/Users/<USER>/Desktop/steeleye/swarm-tasks/tests/order/transformations/metatrader/mt4/datasample_trades.csv�h�h�h͌2022-01-12T13:53:09.000000Z��2022-01-12T03:05:29.000000Z��2022-01-12T19:03:16.000000Z��2022-01-12T08:45:37.000000Z�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��Open�hҌClose�h�G@\�z�G�G@w"=p��
G@\�5?|�G@wz�G��JPY��USD�h�hՌMONE�h�h�h֌USD��pandas._libs.missing��NA���h�h�h֌UNIT�h�h�h�h�h�h�h�h�h�hь1855352��1851545�h�h݌XXXX�h�h�h�h�h�h�h�]�}�(�labelId��USD/JPY-CFD��path��instrumentDetails.instrument��type��se_elastic_schema.static.market��IdentifierType����OBJECT���R�ua]�}�(h�Bitcoin Cash-CFD�h�h�h�h�ua]�}�(h�USD/JPY-CFD�h�h�h�h�ua]�}�(h�Bitcoin Cash-CFD�h�h�h�h�ua]�(}�(h�
id:8002304�h�buyerDecisionMaker�h�h�ARRAY���R�u}�(h�
id:8002304�h�seller�h�h�u}�(h�
id:8002304�h�clientIdentifiers.client�h�h�ue]�(}�(h�
id:8002304�h�buyer�h�h�u}�(h�
id:8002304�h�sellerDecisionMaker�h�h�u}�(h�
id:8002304�h�j  h�h�ue]�(}�(h�
id:8002304�h�j  h�h�u}�(h�
id:8002304�h�j  h�h�u}�(h�
id:8002304�h�j  h�h�ue]�(}�(h�
id:8002304�h�h�h�h�u}�(h�
id:8002304�h�h�h�h�u}�(h�
id:8002304�h�j  h�h�ueh�h�h�h�hڌ
id:8002304��
id:8002304�hڌ
id:8002304�h�hڌ
id:8002304�h�h�h�hڌ
id:8002304�h�hڌ
id:8002304�hڌ
id:8002304��
id:8002304�h�h�h�h�h�h�h�h�hڌ
id:8002304��
id:8002304��
id:8002304��
id:8002304�h�h�h�h�]�(h�h�h�h�e]�(h�j  j  j	  e]�(h�j  j  j  e]�(h�j  j  j  eh�h�h�hڌ
id:8002304��
id:8002304��
id:8002304��
id:8002304�h�h�h�hڌCD�j-  j-  j-  �CFD�j.  j.  j.  �CU��CO�j/  j0  hڌIN�h�j1  h�h�h�hڌCASH�j2  j2  j2  �OTHR�j3  j3  j3  h�h�h�h֌JFTXCC��JTMXCC�j4  j5  h�h�h�h�h�h�h�h�h�h�h�h�j.  j.  j.  j.  �
XXXXJPYCFD��
XXXXUSDCFD��
XXXXJPYCFD��
XXXXUSDCFD��FX�� �j:  j;  �XXXXJPYFXCFD��
XXXXUSDCFD��XXXXJPYFXCFD��
XXXXUSDCFD��
XXXXUSDJPYCFD��
XXXXBCHUSDCFD��
XXXXUSDJPYCFD��
XXXXBCHUSDCFD�j@  jA  jB  jC  et�bhhK ��h��R�(KKK��h!�]�(h�h�h�h�et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h\at�bhsNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hpat�bhsNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h5h7hDet�bhsNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h9hcet�bhsNu��R�h
h}�(hhhK ��h��R�(KKE��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3h4h6h8h:h;h<h=h>h?h@hAhBhChEhFhGhHhIhJhKhLhMhNhOhPhQhRhShThUhVhWhXhYhZh[h]h^h_h`hahbhdhehfhghhhihjhkhlhmhnhoet�bhsNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hqat�bhsNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���K7K8K��R�u}�(j�  h�j�  j�  KKKLK��R�u}�(j�  h�j�  h�(�                            �h�K��h�t�R�u}�(j�  h�j�  j�  KKhK*��R�u}�(j�  h�j�  h�(�(                                                                      	       
                     
                                                                                                                 !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       3       4       5       6       8       9       :       ;       <       =       ?       @       A       B       C       D       E       F       G       H       I       J       �h�KE��h�t�R�u}�(j�  jG  j�  j�  KLKMK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.