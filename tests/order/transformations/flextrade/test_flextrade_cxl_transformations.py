import os
from pathlib import Path
from unittest.mock import MagicMock

import pandas as pd
import pytest
from prefect import context
from swarm.task.auditor import Auditor

from swarm_tasks.order.feed.flextrade.static import FlextradeTempColumns
from swarm_tasks.order.transformations.flextrade.flextrade_cxl_transformations import (
    FlextradeCxlTransformations,
)
from swarm_tasks.steeleye.generic.get_tenant_lei import GetTenantLEI

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data").joinpath("cxl")
TEST_FILE_PATH = TEST_FILES_DIR.joinpath("cxl_source_frame.pkl")
EXPECTED_RESULT_PICKLE_PATH = TEST_FILES_DIR.joinpath(
    "expected_result_flextrade_cxl_transformations.pkl"
)


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


@pytest.fixture()
def source_frame() -> pd.DataFrame:
    return pd.read_pickle(TEST_FILE_PATH)


class TestFlextradeCxlTransformations:
    """
    Test suite for FlextradeCxlTransformations
    """

    @staticmethod
    def mock_get_tenant_lei(mocker, source_deals_frame):
        mock_get_tenant_lei = mocker.patch.object(GetTenantLEI, "process")
        tenant_lei_df = pd.DataFrame(index=source_deals_frame.index)
        tenant_lei_df = tenant_lei_df.assign(
            **{FlextradeTempColumns.EXECUTING_ENTITY_WITH_LEI: "lei:sample_lei"}
        )
        mock_get_tenant_lei.return_value = tenant_lei_df
        return mock_get_tenant_lei

    def test_end_to_end_transformations(
        self, mocker: MagicMock, auditor: Auditor, source_frame: pd.DataFrame
    ):
        """Runs an end-to-end test for FlextradeCxlTransformations"""
        os.environ["SWARM_FILE_URL"] = str(TEST_FILE_PATH)
        self.mock_get_tenant_lei(mocker, source_frame)
        task = FlextradeCxlTransformations(
            source_frame=source_frame,
            logger=context.get("logger"),
            auditor=auditor,
        )
        result = task.process()
        expected = pd.read_pickle(EXPECTED_RESULT_PICKLE_PATH)
        assert not pd.testing.assert_frame_equal(
            left=result.drop(["sourceKey"], axis=1).sort_index(axis=1),
            right=expected.drop(["sourceKey"], axis=1).sort_index(axis=1),
            check_dtype=False,
        )
