��      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK2��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType�� executionDetails.tradingCapacity��	hierarchy��	_order.id��_orderState.id��_order.__meta_model__��_orderState.__meta_model__��orderIdentifiers.orderIdCode��!orderIdentifiers.transactionRefNo��priceFormingData.price��reportDetails.transactionRefNo��sourceIndex��	sourceKey��timestamps.tradingDateTime��timestamps.orderReceived��timestamps.orderSubmitted��timestamps.orderStatusUpdated��#transactionDetails.buySellIndicator��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��#transactionDetails.quantityCurrency��priceFormingData.tradedQuantity�� priceFormingData.initialQuantity��transactionDetails.quantity��#transactionDetails.quantityNotation��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C               �t�bhX�__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KKK��h!�]�(�1��2�hsht�Saxobank�hu�
2021-10-12��
2021-10-12��BUYI��SELL��NEWO�hz�FILL�h{�MARKET�h|�AOTC�h}�
Standalone�h~�
91********��
**********�hh��Order�h��
OrderState�h�hh�et�b�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�
9********0�h�et�b�_dtype�h��StringDtype���)��ubhhK ��h��R�(KKK��h�f8�����R�(KhfNNNJ����J����K t�b�C#��~j��?�|?5^��?�t�bh�)��}�(h�hhK ��h��R�(KK��h��]�(h�h�et�bh�h�)��ubhhK ��h��R�(KKK��he�C               �t�bhhK ��h��R�(KKK��h!�]�(�z/Users/<USER>/Github/se-prefect-dir/swarm-tasks/tests/order/transformations/saxobank/data/FXOptionTradesExecuted_test.pkl�h��2021-10-12T10:02:07.737000Z��2021-10-12T10:02:42.050000Z�h�h�h�h�h�h�hxhyet�bhhK ��h��R�(KKK��h��C#��~j��?�|?5^��?�t�bhhK ��h��R�(KKK��h!�]�(�JPY�hƌMONE�hǌEUR�h�et�bhhK ��h��R�(KKK��h��C0     j(A     j(A     j(A     j(A     j(A     j(A�t�bhhK ��h��R�(KKK��h!�]�(h�h�h}h}h�h��pandas._libs.missing��NA���h׌XOFF�h�]�(}�(�labelId��XXXXEURJPYOC2021-10-29��path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�u}�(hی"XXXXEURJPYOC2021-10-29113.********�h�h�h�h�ue]�(}�(hیXXXXEURJPYOC2021-10-13�h�h�h�h�u}�(hی"XXXXEURJPYOC2021-10-13112.********�h�h�h�h�ue]�(}�(hیid:101000zz1010ic1zzd09�h݌buyer�h�h�ARRAY���R�u}�(hیid:101000zz1010ic1zzd09�h݌reportDetails.executingEntity�h�h�u}�(hی
id:1111111�h݌seller�h�h�u}�(hی
id:1111111�h݌counterparty�h�h�u}�(hی
id:9142297�h݌trader�h�h�ue]�(}�(hی
id:1111111�h�h�h�h�u}�(hیid:101000zz1010ic1zzd09�h�h�h�h�u}�(hیid:101000zz1010ic1zzd09�h�h�h�h�u}�(hی
id:1111111�h�h�h�h�u}�(hی
id:9142297�h�h�h�h�ue�id:101000zz1010ic1zzd09��id:101000zz1010ic1zzd09��id:101000zz1010ic1zzd09��
id:1111111��
id:1111111��id:101000zz1010ic1zzd09��
id:1111111��
id:1111111�h�h�h�h�h�h�h�h�h�h׌
id:9142297��
id:9142297�et�bhhK ��h��R�(KKK��h!�]�(]�(h�h�h�h�h�h�h�e]�(h�h�j  j  j  j  j	  eet�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3et�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h8h9h:h;h<h=et�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h?h@hAet�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hBhChDet�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hEhFhGhHhIhJhKhLhMhNhOhPhQhRhShThUet�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hVat�bhXNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hp�mgr_locs��builtins��slice���K KK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KK K��R�u}�(j�  h�j�  j�  K K1K��R�u}�(j�  j  j�  j�  K1K2K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.