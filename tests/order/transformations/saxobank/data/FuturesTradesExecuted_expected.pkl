���'      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK2��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType�� executionDetails.tradingCapacity��	hierarchy��	_order.id��_orderState.id��_order.__meta_model__��_orderState.__meta_model__��orderIdentifiers.orderIdCode��!orderIdentifiers.transactionRefNo��priceFormingData.price��reportDetails.transactionRefNo��sourceIndex��	sourceKey��timestamps.tradingDateTime��timestamps.orderReceived��timestamps.orderSubmitted��timestamps.orderStatusUpdated��#transactionDetails.buySellIndicator��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��#transactionDetails.quantityCurrency��priceFormingData.tradedQuantity�� priceFormingData.initialQuantity��transactionDetails.quantity��#transactionDetails.quantityNotation��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK	��h�i8�����R�(K�<�NNNJ����J����K t�b�CH                                                                �t�bhX�__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KKK	��h!�]�(�1��2�hthththshthththshththththshththt�Saxobank�huhuhuhuhuhuhuhu�
2022-02-01��
2022-02-01��
2022-02-01��
2022-02-01��
2022-02-01��
2022-02-01��
2022-02-01��
2022-02-01��
2022-02-01��BUYI��SELL�h�h�h�hh�h�h��NEWO�h�h�h�h�h�h�h�h��FILL�h�h�h�h�h�h�h�h��MARKET�h�h�h�h�h�h�h�h��AOTC�h�h�h�h�h�h�h�h��
Standalone�h�h�h�h�h�h�h�h��
**********��
**********��
**********��
**********��
**********��
**********��
**********��
**********��
**********�h�h�h�h�h�h�h�h�h��Order�h�h�h�h�h�h�h�h��
OrderState�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�b�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK	��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�
**********��
**********��
5124540732��
5124591803��
5124542604��
5124391132��
5124541694��
5124393109��
5124396195�et�b�_dtype�h��StringDtype���)��ubhhK ��h��R�(KKK	��h�f8�����R�(KhfNNNJ����J����K t�b�CH33333�]@[B>�٬�?�y�):��?     P�@��4�8E�?�G�z�p@=,Ԛ��?�����#�@fffffa@�t�bh�)��}�(h�hhK ��h��R�(KK	��h��]�(h�h�h�h�h�h�h�h�h�et�bh�h�)��ubhhK ��h��R�(KKK	��he�CH                                                                �t�bhhK ��h��R�(KKK	��h!�]�(�y/Users/<USER>/Github/se-prefect-dir/swarm-tasks/tests/order/transformations/saxobank/data/FuturesTradesExecuted_test.pkl�h�h�h�h�h�h�h�h͌2022-02-01T08:13:13.590000Z��2022-02-01T09:07:05.713000Z��2022-02-01T09:07:49.537000Z��2022-02-01T09:47:48.767000Z��2022-02-01T09:09:20.730000Z��2022-02-01T07:51:41.113000Z��2022-02-01T09:08:37.367000Z��2022-02-01T07:54:22.953000Z��2022-02-01T07:58:40.553000Z�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�hh�h�h�h�hh�h�h�et�bhhK ��h��R�(KKK	��h��CH33333�]@[B>�٬�?�y�):��?     P�@��4�8E�?�G�z�p@=,Ԛ��?�����#�@fffffa@�t�bhhK ��h��R�(KKK	��h!�]�(�EUR��USD�h�h�h�h�h�h�h�MONE�h�h�h�h�h�h�h�h�pandas._libs.missing��NA���h�h�h�h�h�h�h�h�et�bhhK ��h��R�(KKK	��h��C�      @      �?       @      @       @      �?      �?      �?      �?      @      �?       @      @       @      �?      �?      �?      �?      @      �?       @      @       @      �?      �?      �?      �?�t�bhhK ��h��R�(KKK	��h!�]�(�UNIT�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h֌XEUR��XCME�h��IFUS�h��XNYM�h�h��XSIM��XOFF�h�h�h�h�h�h�h�h�]�(}�(�labelId��DE000F0VD0D8��path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�u}�(h��DE000F0VD0D8EURXEUR�j   j  j  j  u}�(h��XEURFEXDFF2022-12-16 00:00:00�j   j  j  j  u}�(h��XEURFEXDFF2022-12 00:00:00�j   j  j  j  ue]�(}�(h��XCMEADFF2022-03-14 00:00:00�j   j  j  j  u}�(h��XCMEADFF2022-03 00:00:00�j   j  j  j  ue]�(}�(h��XCMEBPFF2022-03-14 00:00:00�j   j  j  j  u}�(h��XCMEBPFF2022-03 00:00:00�j   j  j  j  ue]�(}�(h��IFUSCCFF2022-05-13 00:00:00�j   j  j  j  u}�(h��IFUSCCFF2022-05 00:00:00�j   j  j  j  ue]�(}�(h��XCMECDFF2022-03-15 00:00:00�j   j  j  j  u}�(h��XCMECDFF2022-03 00:00:00�j   j  j  j  ue]�(}�(h��XNYMHOFF2022-02-28 00:00:00�j   j  j  j  u}�(h��XNYMHOFF2022-02 00:00:00�j   j  j  j  ue]�(}�(h��XCMENEFF2022-03-14 00:00:00�j   j  j  j  u}�(h��XCMENEFF2022-03 00:00:00�j   j  j  j  ue]�(}�(h��XNYMPLFF2022-04-27 00:00:00�j   j  j  j  u}�(h��XNYMPLFF2022-04 00:00:00�j   j  j  j  ue]�(}�(h��XSIMSCOFF2022-03-31 00:00:00�j   j  j  j  u}�(h��XSIMSCOFF2022-03 00:00:00�j   j  j  j  ue]�(}�(h��lei:549300tl5406ic1xkd09�j   �buyer�j  j  �ARRAY���R�u}�(h��lei:549300tl5406ic1xkd09�j   �reportDetails.executingEntity�j  j  u}�(h��id:15326664�j   �seller�j  j=  u}�(h��id:15326664�j   �counterparty�j  j  u}�(h��id:16152148�j   �trader�j  j=  ue]�(}�(h��id:15326664�j   j:  j  j=  u}�(h��lei:549300tl5406ic1xkd09�j   j@  j  j  u}�(h��lei:549300tl5406ic1xkd09�j   jC  j  j=  u}�(h��id:15326664�j   jF  j  j  u}�(h��id:16152148�j   jI  j  j=  ue]�(}�(h��id:15326664�j   j:  j  j=  u}�(h��lei:549300tl5406ic1xkd09�j   j@  j  j  u}�(h��lei:549300tl5406ic1xkd09�j   jC  j  j=  u}�(h��id:15326664�j   jF  j  j  u}�(h��id:16152148�j   jI  j  j=  ue]�(}�(h��id:15326664�j   j:  j  j=  u}�(h��lei:549300tl5406ic1xkd09�j   j@  j  j  u}�(h��lei:549300tl5406ic1xkd09�j   jC  j  j=  u}�(h��id:15326664�j   jF  j  j  u}�(h��id:16152148�j   jI  j  j=  ue]�(}�(h��id:15326664�j   j:  j  j=  u}�(h��lei:549300tl5406ic1xkd09�j   j@  j  j  u}�(h��lei:549300tl5406ic1xkd09�j   jC  j  j=  u}�(h��id:15326664�j   jF  j  j  u}�(h��id:16152148�j   jI  j  j=  ue]�(}�(h��lei:549300tl5406ic1xkd09�j   j:  j  j=  u}�(h��lei:549300tl5406ic1xkd09�j   j@  j  j  u}�(h��id:15326664�j   jC  j  j=  u}�(h��id:15326664�j   jF  j  j  u}�(h��id:16152148�j   jI  j  j=  ue]�(}�(h��id:15326664�j   j:  j  j=  u}�(h��lei:549300tl5406ic1xkd09�j   j@  j  j  u}�(h��lei:549300tl5406ic1xkd09�j   jC  j  j=  u}�(h��id:15326664�j   jF  j  j  u}�(h��id:16152148�j   jI  j  j=  ue]�(}�(h��id:15326664�j   j:  j  j=  u}�(h��lei:549300tl5406ic1xkd09�j   j@  j  j  u}�(h��lei:549300tl5406ic1xkd09�j   jC  j  j=  u}�(h��id:15326664�j   jF  j  j  u}�(h��id:16152148�j   jI  j  j=  ue]�(}�(h��id:15326664�j   j:  j  j=  u}�(h��lei:549300tl5406ic1xkd09�j   j@  j  j  u}�(h��lei:549300tl5406ic1xkd09�j   jC  j  j=  u}�(h��id:15326664�j   jF  j  j  u}�(h��id:16152148�j   jI  j  j=  ue�lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��id:15326664��id:15326664��id:15326664��id:15326664��lei:549300tl5406ic1xkd09��id:15326664��id:15326664��id:15326664��id:15326664��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��id:15326664��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��lei:549300tl5406ic1xkd09��id:15326664��id:15326664��id:15326664��id:15326664��id:15326664��id:15326664��id:15326664��id:15326664��id:15326664�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�id:16152148��id:16152148��id:16152148��id:16152148��id:16152148��id:16152148��id:16152148��id:16152148��id:16152148�et�bhhK ��h��R�(KKK	��h!�]�(]�(h�j	  j  j
  j8  j>  jA  jD  jG  e]�(j  j  jK  jM  jO  jQ  jS  e]�(j  j  jV  jX  jZ  j\  j^  e]�(j  j  ja  jc  je  jg  ji  e]�(j  j!  jl  jn  jp  jr  jt  e]�(j$  j&  jw  jy  j{  j}  j  e]�(j)  j+  j�  j�  j�  j�  j�  e]�(j.  j0  j�  j�  j�  j�  j�  e]�(j3  j5  j�  j�  j�  j�  j�  eet�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3et�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h8h9h:h;h<h=et�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h?h@hAet�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hBhChDet�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hEhFhGhHhIhJhKhLhMhNhOhPhQhRhShThUet�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hVat�bhXNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hp�mgr_locs��builtins��slice���K KK��R�u}�(jJ  h�jK  jN  KKK��R�u}�(jJ  h�jK  jN  KKK��R�u}�(jJ  h�jK  jN  KKK��R�u}�(jJ  h�jK  jN  KKK��R�u}�(jJ  h�jK  jN  KKK��R�u}�(jJ  h�jK  jN  KKK��R�u}�(jJ  h�jK  jN  KKK��R�u}�(jJ  h�jK  jN  KK K��R�u}�(jJ  h�jK  jN  K K1K��R�u}�(jJ  j�  jK  jN  K1K2K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.