���-      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK2��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType�� executionDetails.tradingCapacity��	hierarchy��	_order.id��_orderState.id��_order.__meta_model__��_orderState.__meta_model__��orderIdentifiers.orderIdCode��!orderIdentifiers.transactionRefNo��priceFormingData.price��reportDetails.transactionRefNo��sourceIndex��	sourceKey��timestamps.tradingDateTime��timestamps.orderReceived��timestamps.orderSubmitted��timestamps.orderStatusUpdated��#transactionDetails.buySellIndicator��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��#transactionDetails.quantityCurrency��priceFormingData.tradedQuantity�� priceFormingData.initialQuantity��transactionDetails.quantity��#transactionDetails.quantityNotation��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK
��h�i8�����R�(K�<�NNNJ����J����K t�b�CP                                                                	       �t�bhX�__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KKK
��h!�]�(�1��2�hthththshshshshshshththththshshshshs�Saxobank�huhuhuhuhuhuhuhuhu�
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��BUYI��SELL�h�h�h�h�h�h�h�h��NEWO�h�h�h�h�h�h�h�h�h��FILL�h�h�h�h�h�h�h�h�h��MARKET�h�h�h�h�h�h�h�h�h��AOTC�h�h�h�h�h�h�h�h�h��
Standalone�h�h�h�h�h�h�h�h�h��
**********��
**********��
**********��
**********��
**********��
**********��
**********��
**********��
**********��
**********�h�h�h�h�h�h�h�h�h�h��Order�h�h�h�h�h�h�h�h�h��
OrderState�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�b�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK
��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�	100000001��	100000002��	100000003��	100000004��	100000005��	100000006��	100000007��	100000008��	100000009��	100000010�et�b�_dtype�h��StringDtype���)��ubhhK ��h��R�(KKK
��h�f8�����R�(KhfNNNJ����J����K t�b�CP)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?�t�bh�)��}�(h�hhK ��h��R�(KK
��h��]�(h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubhhK ��h��R�(KKK
��he�CP                                                                	       �t�bhhK ��h��R�(KKK
��h!�]�(�u/Users/<USER>/Github/se-prefect-dir/swarm-tasks/tests/order/transformations/saxobank/data/CFDTradesExecuted_test.pkl�h�h�h�h�h�h�h�h�hЌ2021-10-12T07:03:49.833000Z��2021-10-12T07:11:13.510000Z��2021-10-12T07:47:59.140000Z��2021-10-12T05:24:45.017000Z��2021-10-12T12:49:32.217000Z��2021-10-12T08:45:20.827000Z��2021-10-12T11:24:09.270000Z��2021-10-12T17:05:08.597000Z��2021-10-12T17:26:14.570000Z��2021-10-12T19:05:28.013000Z�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bhhK ��h��R�(KKK
��h��CP)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?)\���(�?�t�bhhK ��h��R�(KKK
��h!�]�(�EUR�h�USD�h�h�h�h�h�h�h�MONE�h�h�h�h�h�h�h�h�h�pandas._libs.missing��NA���h�h�h�h�h�h�h�h�h�et�bhhK ��h��R�(KKK
��h��C�      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?      �?�t�bhhK ��h��R�(KKK
��h!�]�(�UNIT�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�hڌXPAR�h��XNYS��XETR�h�h�h�h�h�h��XOFF�h�h�h�h�h�h�h�h�h�]�(}�(�labelId��XXXXEU0009658145CFD��path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�u}�(j   �XXXXEU0009658145EURCFD�j  j  j  j
  u}�(j   �XXXXEU50CFD�j  j  j  j
  u}�(j   �XXXXEU50EURCFD�j  j  j  j
  ue]�(}�(j   �XXXXEU0009658145CFD�j  j  j  j
  u}�(j   �XXXXEU0009658145EURCFD�j  j  j  j
  u}�(j   �XXXXEU50CFD�j  j  j  j
  u}�(j   �XXXXEU50EURCFD�j  j  j  j
  ue]�(}�(j   �XXXXUS78378X1072CFD�j  j  j  j
  u}�(j   �XXXXUS78378X1072USDCFD�j  j  j  j
  u}�(j   �XXXXUS500CFD�j  j  j  j
  u}�(j   �XXXXUS500USDCFD�j  j  j  j
  ue]�(}�(j   �XXXXDE0008469008CFD�j  j  j  j
  u}�(j   �XXXXDE0008469008EURCFD�j  j  j  j
  u}�(j   �XXXXGER40CFD�j  j  j  j
  u}�(j   �XXXXGER40EURCFD�j  j  j  j
  ue]�(}�(j   �XXXXDE0008469008CFD�j  j  j  j
  u}�(j   �XXXXDE0008469008EURCFD�j  j  j  j
  u}�(j   �XXXXGER40CFD�j  j  j  j
  u}�(j   �XXXXGER40EURCFD�j  j  j  j
  ue]�(}�(j   �XXXXUS78378X1072CFD�j  j  j  j
  u}�(j   �XXXXUS78378X1072USDCFD�j  j  j  j
  u}�(j   �XXXXUS500CFD�j  j  j  j
  u}�(j   �XXXXUS500USDCFD�j  j  j  j
  ue]�(}�(j   �XXXXUS78378X1072CFD�j  j  j  j
  u}�(j   �XXXXUS78378X1072USDCFD�j  j  j  j
  u}�(j   �XXXXUS500CFD�j  j  j  j
  u}�(j   �XXXXUS500USDCFD�j  j  j  j
  ue]�(}�(j   �XXXXUS78378X1072CFD�j  j  j  j
  u}�(j   �XXXXUS78378X1072USDCFD�j  j  j  j
  u}�(j   �XXXXUS500CFD�j  j  j  j
  u}�(j   �XXXXUS500USDCFD�j  j  j  j
  ue]�(}�(j   �XXXXUS78378X1072CFD�j  j  j  j
  u}�(j   �XXXXUS78378X1072USDCFD�j  j  j  j
  u}�(j   �XXXXUS500CFD�j  j  j  j
  u}�(j   �XXXXUS500USDCFD�j  j  j  j
  ue]�(}�(j   �XXXXUS78378X1072CFD�j  j  j  j
  u}�(j   �XXXXUS78378X1072USDCFD�j  j  j  j
  u}�(j   �XXXXUS500CFD�j  j  j  j
  u}�(j   �XXXXUS500USDCFD�j  j  j  j
  ue]�(}�(j   �id:666666tl1111ic1aad00�j  �buyer�j  j  �ARRAY���R�u}�(j   �id:666666tl1111ic1aad00�j  �reportDetails.executingEntity�j  j
  u}�(j   �id:12345678�j  �seller�j  jh  u}�(j   �id:12345678�j  �counterparty�j  j
  u}�(j   �
id:1111111�j  �trader�j  jh  ue]�(}�(j   �id:12345678�j  je  j  jh  u}�(j   �id:666666tl1111ic1aad01�j  jk  j  j
  u}�(j   �id:666666tl1111ic1aad01�j  jn  j  jh  u}�(j   �id:12345678�j  jq  j  j
  u}�(j   �
id:1111111�j  jt  j  jh  ue]�(}�(j   �id:12345678�j  je  j  jh  u}�(j   �id:666666tl1111ic1aad02�j  jk  j  j
  u}�(j   �id:666666tl1111ic1aad02�j  jn  j  jh  u}�(j   �id:12345678�j  jq  j  j
  u}�(j   �
id:1111111�j  jt  j  jh  ue]�(}�(j   �id:12345678�j  je  j  jh  u}�(j   �id:666666tl1111ic1aad03�j  jk  j  j
  u}�(j   �id:666666tl1111ic1aad03�j  jn  j  jh  u}�(j   �id:12345678�j  jq  j  j
  u}�(j   �
id:1111111�j  jt  j  jh  ue]�(}�(j   �id:12345678�j  je  j  jh  u}�(j   �id:666666tl1111ic1aad04�j  jk  j  j
  u}�(j   �id:666666tl1111ic1aad04�j  jn  j  jh  u}�(j   �id:12345678�j  jq  j  j
  u}�(j   �
id:1111111�j  jt  j  jh  ue]�(}�(j   �id:666666tl1111ic1aad05�j  je  j  jh  u}�(j   �id:666666tl1111ic1aad05�j  jk  j  j
  u}�(j   �id:12345678�j  jn  j  jh  u}�(j   �id:12345678�j  jq  j  j
  u}�(j   �
id:1111111�j  jt  j  jh  ue]�(}�(j   �id:666666tl1111ic1aad06�j  je  j  jh  u}�(j   �id:666666tl1111ic1aad06�j  jk  j  j
  u}�(j   �id:12345678�j  jn  j  jh  u}�(j   �id:12345678�j  jq  j  j
  u}�(j   �
id:1111111�j  jt  j  jh  ue]�(}�(j   �id:666666tl1111ic1aad07�j  je  j  jh  u}�(j   �id:666666tl1111ic1aad07�j  jk  j  j
  u}�(j   �id:12345678�j  jn  j  jh  u}�(j   �id:12345678�j  jq  j  j
  u}�(j   �
id:1111111�j  jt  j  jh  ue]�(}�(j   �id:666666tl1111ic1aad08�j  je  j  jh  u}�(j   �id:666666tl1111ic1aad08�j  jk  j  j
  u}�(j   �id:12345678�j  jn  j  jh  u}�(j   �id:12345678�j  jq  j  j
  u}�(j   �
id:1111111�j  jt  j  jh  ue]�(}�(j   �id:666666tl1111ic1aad09�j  je  j  jh  u}�(j   �id:666666tl1111ic1aad09�j  jk  j  j
  u}�(j   �id:12345678�j  jn  j  jh  u}�(j   �id:12345678�j  jq  j  j
  u}�(j   �
id:1111111�j  jt  j  jh  ue�id:666666tl1111ic1aad00��id:666666tl1111ic1aad01��id:666666tl1111ic1aad02��id:666666tl1111ic1aad03��id:666666tl1111ic1aad04��id:666666tl1111ic1aad05��id:666666tl1111ic1aad06��id:666666tl1111ic1aad07��id:666666tl1111ic1aad08��id:666666tl1111ic1aad09��id:666666tl1111ic1aad00��id:12345678��id:12345678��id:12345678��id:12345678��id:666666tl1111ic1aad05��id:666666tl1111ic1aad06��id:666666tl1111ic1aad07��id:666666tl1111ic1aad08��id:666666tl1111ic1aad09��id:12345678��id:666666tl1111ic1aad01��id:666666tl1111ic1aad02��id:666666tl1111ic1aad03��id:666666tl1111ic1aad04��id:12345678��id:12345678��id:12345678��id:12345678��id:12345678��id:12345678��id:12345678��id:12345678��id:12345678��id:12345678��id:12345678��id:12345678��id:12345678��id:12345678��id:12345678�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�
id:1111111��
id:1111111��
id:1111111��
id:1111111��
id:1111111��
id:1111111��
id:1111111��
id:1111111��
id:1111111��
id:1111111�et�bhhK ��h��R�(KKK
��h!�]�(]�(h�j  j
  j  jc  ji  jl  jo  jr  e]�(j  j  j  j  jv  jx  jz  j|  j~  e]�(j  j  j  j!  j�  j�  j�  j�  j�  e]�(j$  j&  j(  j*  j�  j�  j�  j�  j�  e]�(j-  j/  j1  j3  j�  j�  j�  j�  j�  e]�(j6  j8  j:  j<  j�  j�  j�  j�  j�  e]�(j?  jA  jC  jE  j�  j�  j�  j�  j�  e]�(jH  jJ  jL  jN  j�  j�  j�  j�  j�  e]�(jQ  jS  jU  jW  j�  j�  j�  j�  j�  e]�(jZ  j\  j^  j`  j�  j�  j�  j�  j�  eet�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3et�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h8h9h:h;h<h=et�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h?h@hAet�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hBhChDet�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hEhFhGhHhIhJhKhLhMhNhOhPhQhRhShThUet�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hVat�bhXNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hp�mgr_locs��builtins��slice���K KK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KK K��R�u}�(j�  h�j�  j�  K K1K��R�u}�(j�  j
  j�  j�  K1K2K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.