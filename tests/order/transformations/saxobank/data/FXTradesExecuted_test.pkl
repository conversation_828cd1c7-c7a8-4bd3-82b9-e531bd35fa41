��T(      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK@��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�
ReportingDate��InstrumentType��
CounterpartID��CounterpartName��
AccountNumber��PartnerAccountKey��AccountCurrency��InstrumentCode��InstrumentCurrency��TradedAmount��TradeNumber��OrderNumber��	TradeTime��	TradeDate��	ValueDate��BuySell��	SpotPrice��	SwapPrice��Price��QuotedValueInstrumentCurrency��CommissionInstrumentCurrency��RelatedTradeID��	TradeType��TradeAllocation��RootTradeID��ExternalOrderID��OriginalTradeID��
CorrectionLeg��FXType��LiquidityFactor��EODRate��
InstrumentUIC��ActualTradeDate��!ExecutingEntityIdentificationCode��InstrumentISINCode��InstrumentClassification��PriceMultiplier��SettlementStyle��
UserIdOnTrade��
EmployeeId��DecisionMakerUserId��Venue��TradeExecutionTime��OTCPostTradeIndicator��CommodityDerivativeIndicator��'SecuritiesFinancingTransactionIndicator��SaxoPortfolioManagerTriggered��CorrelationKey��InstrumentSubType��OriginatingTool��ShortSellingIndicator��ISINCode��
ExpiryDate��Strike��UnderlyingInstrumentISINCode��ISIN��UnderlyingInstrumentCode��ExchangeISOCode��ComplexTradeComponentId��
DirtyPrice��CallPut��TransmissionOfOrderIndicator��TermOfUnderlyingIndex��	NetAmount�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�CX                                                                	       
       �t�bhf�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h!�]�(�pandas._libs.missing��NA���h�h�h�h�h�h�h�h�h�h�et�b�_dtype�h|�StringDtype���)��ubh~)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h!�]�(�Fx Spot & Forwards�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h!�]�(�1000001�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h!�]�(�EURUSD�j  j  j  �AUDJPY��EURCAD�j  �GBPAUD�j  j  j  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h!�]�(�USD�j  j  j  �JPY��CAD�j  �AUD�j  j  j  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h!�]�(�
8000000000��
8000000001��
8000000002��
8000000003��
8000000004��
8000000005��
8000000006��
8000000007��
8000000008��
8000000009��
8000000010�et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h!�]�(�20211012 12:24:11��20211012 15:43:03��20211012 14:57:34��20211012 15:01:42��20211012 11:29:19��20211012 11:25:50��20211012 11:27:01��20211012 11:24:24��20211012 11:52:01��20211012 05:53:56��20211012 15:13:26�et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h!�]�(�Buy�jF  �Sell�jG  jF  jG  jG  jG  jF  jF  jF  et�bh�h�)��ubhhK ��h��R�(KKK��h�f8�����R�(KhtNNNJ����J����K t�b�B�       p�@     p�@     p��     p��     j�@     L��     ���     j��     @�@     L�@     ��@�HP|�?.�!��u�?('�UHy�?���?�y�?\���(�T@�"��?&ǝ��z�?�d��?{�/L�
�?��{��?c���&��?      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      ��t�bhhK ��h��R�(KK%K��h!�]�(�20211012�jY  jY  jY  jY  jY  jY  jY  jY  jY  jY  �name�jZ  jZ  jZ  jZ  jZ  jZ  jZ  jZ  jZ  jZ  �
111111INET�j[  j[  j[  j[  j[  j[  j[  j[  j[  j[  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �EUR�j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  �
7000000001��
7000000002��
7000000003��
7000000004��
7000000005��
7000000006��
7000000007��
7000000008��
7000000009��
7000000010��
7000000011��20211012�jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  �20211014�ji  ji  ji  ji  ji  ji  ji  ji  �20220214�jj  �1.15535��1.1538��1.15461��1.15467��83.69��1.43801��1.15497��1.84409��1.4401��1.15644��1.15466��0�jv  jv  jv  jv  jv  jv  jv  jv  �0.00359��0.00358��-1733.03��-1730.7��1731.92��1732.01��-4184500��43140.3��92397.6��92204.5��-11520.8��-34800.9��-23164.8�jv  jv  jv  jv  �-681��-7.48�jv  jv  jv  �-6.01��-6�jv  jv  jv  jv  jv  jv  jv  jv  jv  jv  jv  �Open Order Monitor��STP Service�j�  j�  j�  j�  j�  j�  j�  j�  j�  �No�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  jv  jv  jv  jv  jv  jv  jv  jv  jv  jv  jv  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �1�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �1.153�j�  j�  j�  �83.525��1.4375�j�  �1.84815�j�  �1.15627�j�  �21�j�  j�  j�  �2��13�j�  �22�j�  j�  j�  �20211012�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �JFTXFC�j�  j�  j�  j�  j�  j�  j�  j�  �JFRXFC�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �Cash settled�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �1000001�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �SAXO�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �FALSE�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �FALSE�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �FALSE�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �$283099Z9-5Z43-409Z-8854-Z743097Z0Z0Z��$05ZZ044Z-ZZ95-450Z-8ZZ2-85Z0Z1Z0Z7ZZ�j�  j�  �$0Z41084Z-Z238-42Z0-93Z0-Z574Z518ZZ29��$ZZ00Z187-8ZZZ-4109-Z0Z0-Z0Z2ZZ749901��$804ZZ17Z-ZZ8Z-4333-9ZZ9-103Z3Z03ZZ7Z��$23Z44458-Z30Z-43Z5-Z0ZZ-0Z9Z21594Z00��$Z15394ZZ-0374-4ZZ9-ZZZ0-50ZZ55ZZ0Z47��$0ZZZ859Z-1523-4Z08-8180-Z7ZZZZZ072ZZ��$Z05700ZZ-Z74Z-40Z2-Z270-Z04Z8Z924ZZ7��None�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �SaxoTrader Pro�j�  j�  j�  �SaxoTraderGO�j�  j�  j�  j�  j�  j�  et�bh~)��}�(h�hhK ��h��R�(KK��h!�]�(�Spot�j�  j�  j�  j�  j�  j�  j�  j�  �Forward�j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h!�]�(�549300TL5406IC1XKD09�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h!�]�(�
AAAQ7FV570ZZ1�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bh�h�)��ubh~)��}�(h�hhK ��h��R�(KK��h!�]�(�20211012 12:24:11.603��20211012 15:43:03.617��20211012 14:57:34.103��20211012 15:01:42.063��20211012 11:29:19.020��20211012 11:25:50.917��20211012 11:27:01.403��20211012 11:24:24.893��20211012 11:52:01.477��20211012 05:53:56.547��20211012 15:13:26.957�et�bh�h�)��ube]�(h
h}�(hhhK ��h��R�(KK��h!�]�hWat�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hXat�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hYat�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h[at�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h\at�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h]at�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h^at�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h_at�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�haat�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hbat�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hcat�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h,at�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h1at�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h.h7hZh`hdet�bhfNu��R�h
h}�(hhhK ��h��R�(KK%��h!�]�(h%h(h)h*h+h0h2h3h5h6h8h9h:h;h<h=h>h?h@hBhChDhEhHhIhJhKhLhMhNhPhQhRhShThUhVet�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hFat�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hGat�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hOat�bhfNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h�mgr_locs��builtins��slice���K2K3K��R�u}�(j�  h�j�  j�  K3K4K��R�u}�(j�  h�j�  j�  K4K5K��R�u}�(j�  h�j�  j�  K6K7K��R�u}�(j�  h�j�  j�  K7K8K��R�u}�(j�  h�j�  j�  K8K9K��R�u}�(j�  h�j�  j�  K9K:K��R�u}�(j�  h�j�  j�  K:K;K��R�u}�(j�  h�j�  j�  K<K=K��R�u}�(j�  h�j�  j�  K=K>K��R�u}�(j�  h�j�  j�  K>K?K��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  j
  j�  j�  KK	K��R�u}�(j�  j  j�  j�  K
KK��R�u}�(j�  j+  j�  j�  KK
K��R�u}�(j�  j?  j�  j�  KKK��R�u}�(j�  jL  j�  hhK ��h��R�(KK��hs�C(	              5       ;       ?       �t�bu}�(j�  jV  j�  hhK ��h��R�(KK%��hs�B(                                             
                                                                                                                        #       $       %       &       '       (       )       +       ,       -       .       /       0       1       �t�bu}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  K!K"K��R�u}�(j�  j�  j�  j�  K"K#K��R�u}�(j�  j�  j�  j�  K*K+K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.