���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK>��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�
ReportingDate��InstrumentType��
CounterpartID��CounterpartName��
AccountNumber��PartnerAccountKey��AccountCurrency��InstrumentCode��InstrumentCurrency��TradedAmount��TradeNumber��OrderNumber��	TradeTime��	TradeDate��	ValueDate��BuySell��Price��
OptionType��CallPut��Strike��CommissionInstrumentCurrency��Premium��PremiumCurrency��RelatedTradeID��TradeAllocation��RootTradeID��
ExpiryDate��	ExpiryCut��ExpiryValueDate��Barrier��	TradeType��OriginalTradeID��
CorrectionLeg��EODRate��
InstrumentUIC��ActualTradeDate��!ExecutingEntityIdentificationCode��InstrumentClassification��PriceMultiplier��SettlementStyle��
UserIdOnTrade��
EmployeeId��DecisionMakerUserId��Venue��TradeExecutionTime��OTCPostTradeIndicator��'SecuritiesFinancingTransactionIndicator��CorrelationKey��OriginatingTool��UnderlyingInstrumentCode��ISIN��
DirtyPrice��ISINCode��FXType��UnderlyingInstrumentISINCode��TermOfUnderlyingIndex��ComplexTradeComponentId��	NetAmount��ShortSellingIndicator��ExchangeISOCode��TransmissionOfOrderIndicator��InstrumentISINCode�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C               �t�bhd�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h!�]�(�pandas._libs.missing��NA���h�et�b�_dtype�hz�StringDtype���)��ubh|)��}�(hhhK ��h��R�(KK��h!�]�(h�h�et�bh�h�)��ubh|)��}�(hhhK ��h��R�(KK��h!�]�(h�h�et�bh�h�)��ubh|)��}�(hhhK ��h��R�(KK��h!�]�(h�h�et�bh�h�)��ubh|)��}�(hhhK ��h��R�(KK��h!�]�(h�h�et�bh�h�)��ubh|)��}�(hhhK ��h��R�(KK��h!�]�(h�h�et�bh�h�)��ubh|)��}�(hhhK ��h��R�(KK��h!�]�(h�h�et�bh�h�)��ubh|)��}�(hhhK ��h��R�(KK��h!�]�(h�h�et�bh�h�)��ubh|)��}�(hhhK ��h��R�(KK��h!�]�(h�h�et�bh�h�)��ubh|)��}�(hhhK ��h��R�(KK��h!�]�(h�h�et�bh�h�)��ubh|)��}�(hhhK ��h��R�(KK��h!�]�(h�h�et�bh�h�)��ubh|)��}�(hhhK ��h��R�(KK��h!�]�(�
Fx Options�h�et�bh�h�)��ubh|)��}�(hhhK ��h��R�(KK��h!�]�(�1111111�h�et�bh�h�)��ubh|)��}�(hhhK ��h��R�(KK��h!�]�(�USDJPY�j  et�bh�h�)��ubh|)��}�(hhhK ��h��R�(KK��h!�]�(�JPY�j  et�bh�h�)��ubh|)��}�(hhhK ��h��R�(KK��h!�]�(�
9000000000�j  et�bh�h�)��ubh|)��}�(hhhK ��h��R�(KK��h!�]�(�20211012 10:02:07��20211012 10:02:42�et�bh�h�)��ubh|)��}�(hhhK ��h��R�(KK��h!�]�(�Buy��Sell�et�bh�h�)��ubh|)��}�(hhhK ��h��R�(KK��h!�]�(�Call�j6  et�bh�h�)��ubhhK ��h��R�(KKK��h�f8�����R�(KhrNNNJ����J����K t�b�CP     j(A     j(�#��~j��?�|?5^��?33333S\@      \@      �      �      �      ��t�bhhK ��h��R�(KK#K��h!�]�(�20211012�jH  �name�jI  �
111111INET�jJ  G�      G�      �EUR�jK  �
9100000000��
9100000001��20211012�jN  �20211014�jO  �Vanilla Option�jP  �0�jQ  �-518400��986400��JPY�jT  jQ  �
9000000003��No�jV  jQ  jQ  �NY Cut�jW  �20211102��20211015��[0.0000, 0.0000]�jZ  �OpenAPI�j[  G�      G�      G�      G�      �
0.80594374��113.61��42�j^  �20211012�j_  �HFMAVC�j`  �1�ja  �Cash settled�jb  �9142297�jc  G�      G�      G�      G�      �SAXO�jd  G�      G�      �FALSE�je  �$Z026Z8ZZ-74ZZ-4Z95-Z796-86Z12359Z5Z1��$Z0Z900ZZ-86Z5-4ZZ3-Z9Z6-1Z3Z562020Z5��SaxoTrader Pro�jh  et�bh|)��}�(hhhK ��h��R�(KK��h!�]�(�20211029��20211013�et�bh�h�)��ubh|)��}�(hhhK ��h��R�(KK��h!�]�(�101000ZZ1010IC1ZZD09�j|  et�bh�h�)��ubh|)��}�(hhhK ��h��R�(KK��h!�]�(�20211012 10:02:07.737��20211012 10:02:42.050�et�bh�h�)��ube]�(h
h}�(hhhK ��h��R�(KK��h!�]�hVat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hWat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hYat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hZat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h[at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h\at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h]at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h_at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h`at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�haat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hbat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h&at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h,at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h1at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h.h5h8hXh^et�bhdNu��R�h
h}�(hhhK ��h��R�(KK#��h!�]�(h%h(h)h*h+h0h2h3h6h9h:h;h<h=h>h@hAhBhChDhEhFhGhHhJhKhLhMhNhOhPhRhShThUet�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h?at�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hIat�bhdNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hQat�bhdNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h}�mgr_locs��builtins��slice���K1K2K��R�u}�(jj  h�jk  jn  K2K3K��R�u}�(jj  h�jk  jn  K4K5K��R�u}�(jj  h�jk  jn  K5K6K��R�u}�(jj  h�jk  jn  K6K7K��R�u}�(jj  h�jk  jn  K7K8K��R�u}�(jj  h�jk  jn  K8K9K��R�u}�(jj  h�jk  jn  K:K;K��R�u}�(jj  h�jk  jn  K;K<K��R�u}�(jj  h�jk  jn  K<K=K��R�u}�(jj  h�jk  jn  K=K>K��R�u}�(jj  h�jk  jn  KKK��R�u}�(jj  h�jk  jn  KKK��R�u}�(jj  h�jk  jn  KKK��R�u}�(jj  j  jk  jn  KK	K��R�u}�(jj  j  jk  jn  K
KK��R�u}�(jj  j  jk  jn  KK
K��R�u}�(jj  j$  jk  jn  KKK��R�u}�(jj  j/  jk  jn  KKK��R�u}�(jj  j;  jk  hhK ��h��R�(KK��hq�C(	                     3       9       �t�bu}�(jj  jE  jk  hhK ��h��R�(KK#��hq�B                                             
                                                                                                          !       "       #       %       &       '       (       )       *       +       -       .       /       0       �t�bu}�(jj  jj  jk  jn  KKK��R�u}�(jj  ju  jk  jn  K$K%K��R�u}�(jj  j  jk  jn  K,K-K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.