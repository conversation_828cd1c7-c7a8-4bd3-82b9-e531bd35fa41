��?      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKB��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��executionDetails.limitPrice��executionDetails.orderType��(_orderState.executionDetails.orderStatus��#_order.executionDetails.orderStatus��&executionDetails.outgoingOrderAddlInfo�� executionDetails.routingStrategy��executionDetails.stopPrice�� executionDetails.tradingCapacity��	_order.id��_orderState.id��marketIdentifiers.instrument��underlying_symbol_attribute��expiry_date_attribute��currency_attribute��venue_attribute��asset_class_attribute��_order.__meta_model__��_orderState.__meta_model__��orderIdentifiers.orderIdCode��!orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��"_orderState.priceFormingData.price��._orderState.priceFormingData.remainingQuantity��)_order.priceFormingData.remainingQuantity��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��sourceIndex��	sourceKey��&_orderState.timestamps.tradingDateTime��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��#transactionDetails.buySellIndicator��transactionDetails.quantity��#transactionDetails.quantityCurrency��$_orderState.transactionDetails.price��transactionDetails.priceAverage�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��#transactionDetails.quantityNotation��"transactionDetails.tradingCapacity��._orderState.transactionDetails.tradingDateTime��transactionDetails.venue�� transactionDetails.ultimateVenue��__newo_in_file__��__expiry_date__��__is_created_through_fallback__��__best_ex_asset_class_main__��__asset_class__��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(hhN�start�K �stop�K�step�Ku��R�e]�(hhK ��h��R�(KKK��h�b1�����R�(Kh"NNNJ����J����K t�b�C�t�bhhK ��h��R�(KKK��h�f8�����R�(K�<�NNNJ����J����K t�b�C�      Y�      Y�      @      @    � @     ��@      @      @      @      @      @      @    � @     ��@                �t�bhhK ��h��R�(KKK��h�i8�����R�(Kh�NNNJ����J����K t�b�C                �t�bhhK ��h��R�(KK8K��h!�]�(�2��1�h�h��LME Select Fix�h��
2022-11-18�h��SELL��BUYI��Limit�h�G�      G�      �NEWO�h��House�h��Non Algorithmic�h��pandas._libs.missing��NA���h��MTCH�h��ON-NI-20221118-002066�h�h�h�]�(}�(�labelId��XLMENIUSDFF2022-12-21 00:00:00��path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�u}�(h��XLMENIUSDFF2022-12 00:00:00�h�h�h�h�ue]�(}�(h��XLMENIUSDFF2023-02-15 00:00:00�h�h�h�h�u}�(h��XLMENIUSDFF2023-02 00:00:00�h�h�h�h�ue�NI�h��20221221��20230215��USD�h��XLME�h��future�h��Order�h
OrderState�h�h�h�h�h�h�h��ON-NI-20221118-002066-8234635�hČ|s3://integration.uat.steeleye.co/sftp/lme_fix_icbc/196dd70f9d17b26b0c583b840de88f683281958a8327610ec03e9c94339be607_2353.fix�hŌ2022-11-18T19:02:10.732000Z�h�h�h�h�h�h�h�h�h�h�h�h�h��MONE�hǌUNIT�h�h�h�h�h�h�h�h�h���h�h��Other Instruments�h�h�h�]�(}�(h��id:535079723�h��seller�h�h��ARRAY���R�u}�(h��	id:100003�h��:tradersAlgosWaiversIndicators.investmentDecisionWithinFirm�h�h�u}�(h��	id:100003�h��1tradersAlgosWaiversIndicators.executionWithinFirm�h�h�u}�(h��id:535079723�h��clientIdentifiers.client�h�h�u}�(h��id:icbc�h��trader�h�h�ue]�(}�(h��id:535079723�h�h�h�h�u}�(h��	id:100003�h�h�h�h�u}�(h��	id:100003�h�h�h�h�u}�(h��id:535079723�h�h�h�h�u}�(h��id:icbc�h�h�h�h�ueh�h�h�h��id:535079723��id:535079723�h�h�h�h�h�h��	id:100003��	id:100003��	id:100003��	id:100003��id:535079723��id:535079723��id:icbc��id:icbc�]�(h�h�h�h�h�h�h�e]�(h�h�h�h�h�h�h�eet�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�hXat�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h*h>h?h@hAhKhMhNet�bhhNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hDat�bhhNu��R�h
h}�(hhhK ��h��R�(KK8��h!�]�(h%h&h'h(h)h+h,h-h.h/h0h1h2h3h4h5h6h7h8h9h:h;h<h=hBhChEhFhGhHhIhJhLhOhPhQhRhShThUhVhWhYhZh[h\h]h^h_h`hahbhchdhehfet�bhhNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hw�mgr_locs��builtins��slice���K3K4K��R�u}�(j!  h�j"  hhK ��h��R�(KK��h��C@                                   &       (       )       �t�bu}�(j!  h�j"  j%  KK K��R�u}�(j!  h�j"  hhK ��h��R�(KK8��h��B�                                                           	       
                     
                                                                                                          !       "       #       $       %       '       *       +       ,       -       .       /       0       1       2       4       5       6       7       8       9       :       ;       <       =       >       ?       @       A       �t�bueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.