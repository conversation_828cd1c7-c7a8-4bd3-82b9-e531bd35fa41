��      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKC��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo�� executionDetails.tradingCapacity��	hierarchy��	_order.id��_orderState.id�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��__fallback_buyer__��__fallback_seller__��__fallback_counterparty__��__fallback_client__��__fallback_buyer_dec_maker__��__fallback_seller_dec_maker__��__fallback_inv_dec_in_firm__��__fallback_trader__��__fallback_exec_within_firm__��__fallback_executing_entity__��marketIdentifiers��_order.__meta_model__��_orderState.__meta_model__��
orderClass��"orderIdentifiers.aggregatedOrderId��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��.orderIdentifiers.tradingVenueTransactionIdCode��!orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��priceFormingData.price��priceFormingData.tradedQuantity��reportDetails.transactionRefNo��sourceIndex��"transactionDetails.tradingDateTime��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��timestamps.tradingDateTime��transactionDetails.basketId��#transactionDetails.buySellIndicator��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��transactionDetails.quantity��#transactionDetails.quantityNotation��transactionDetails.recordType��"transactionDetails.tradingCapacity��__asset_type__��__currency__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KKI��h�i8�����R�(K�<�NNNJ����J����K t�b�BH                                                                  	       
                     
                                                                                                                                             !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       3       4       5       6       7       8       9       :       ;       <       =       >       ?       @       A       B       C       D       E       F       G       H       �t�bhiNu��R�e]�(hhK ��h��R�(KK8KI��h!�]�(�1�h��2�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��LME�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��
2022-05-19��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��
2022-05-20��BUYI�hόSELL�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�hЌNEWO�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�hьFILL�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�hҌMarket�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�hӌ-102.000000, FIX API, Telephone, None, CURRENT��+0.000000, FIX API, Telephone, None, CURRENT��+0.000000, FIX API, Telephone, None, CURRENT��+0.000000, FIX API, Telephone, None, CURRENT��-0.000000, SMART GUI, Telephone, None, CURRENT��+0.000000, FIX API, Telephone, None, CURRENT��+0.000000, FIX API, Telephone, None, CURRENT��+0.000000, FIX API, Telephone, None, CURRENT��+0.000000, FIX API, Telephone, None, CURRENT��+0.000000, FIX API, Telephone, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��&0.000000, FIX API, Ring, None, CURRENT��+0.000000, FIX API, Telephone, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��&0.000000, FIX API, Ring, None, CURRENT��+0.000000, FIX API, Telephone, None, CURRENT��+0.000000, FIX API, Telephone, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��+0.000000, FIX API, Telephone, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��+0.000000, FIX API, Telephone, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��&0.000000, FIX API, Ring, None, CURRENT��'0.000000, SELECT, Select, None, CURRENT��,0.000000, FIX API, Basis Ring, None, CURRENT��70.000000, FIX API, Telephone, Settlement Price, CURRENT��+0.000000, FIX API, Telephone, None, CURRENT��70.000000, FIX API, Telephone, Settlement Price, CURRENT��,0.000000, FIX API, Basis Ring, None, CURRENT��,0.000000, FIX API, Basis Ring, None, CURRENT��+0.000000, FIX API, Telephone, None, CURRENT��DEAL�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �
Standalone�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �25847|10278661��CARRIES|37276|10393941��44452|10381652��48227|10517161��51890|10558311��62611|10525442��CARRIES|48901|10512081��49047|10526092��62915|10537802��CARRIES|37276|10393952��33428|10361412��33433|10361452��33789|10358461��CARRIES|48901|10512092��54763|10590262��46496|10499331��46498|10499351��46497|10499341��46499|10499361��46500|10499371��46424|10498731��46442|10498961��46441|10498951��46443|10498971��46444|10498981��46445|10498991��46412|10498611��46419|10498681��46420|10498691��46421|10498701��46422|10498711��46423|10498721��46398|10498491��46399|10498501��46408|10498571��46409|10498581��46410|10498591��46411|10498601��34338|10372601��34439|10374121��34441|10374141��34443|10374161��34445|10374181��46397|10498481��34258|10371711��34332|10372541��34333|10372551��34334|10372561��34335|10372571��34336|10372581��34224|10365522��CARRIES|46478|10496811��CARRIES|21494|10201222��CARRIES|24981|10269241��CARRIES|24982|10269261��CARRIES|24983|10269281��CARRIES|21494|10201231��33374|10360871��CARRIES|24981|10269252��CARRIES|24982|10269272��CARRIES|24983|10269292��CARRIES|46478|10496822��31096|10335031��31097|10335041��33010|10339082��31449|10339042��34296|10368001��CARRIES|52460|10559492��31456|10327131��CARRIES|52460|10559501��33140|10342782��33154|10351301��32714|10327142�j  j   j!  j"  j#  j$  j%  j&  j'  j(  j)  j*  j+  j,  j-  j.  j/  j0  j1  j2  j3  j4  j5  j6  j7  j8  j9  j:  j;  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  jF  jG  jH  jI  jJ  jK  jL  jM  jN  jO  jP  jQ  jR  jS  jT  jU  jV  jW  jX  jY  jZ  j[  j\  j]  j^  j_  j`  ja  jb  jc  jd  je  jf  jg  �XLME�jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  e(jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  ]�(}�(�labelId��XLMEAHUSDOC2022-07-20 00:00:00��path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�u}�(jk  �XLMEAHUSDOC2022-07 00:00:00�jm  jn  jo  ju  u}�(jk  �+XLMEAHUSDOC2022-07-20 00:00:003000.00000000�jm  jn  jo  ju  u}�(jk  �(XLMEAHUSDOC2022-07 00:00:003000.00000000�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDC2022-07-20 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDC2022-07 00:00:00�jm  jn  jo  ju  u}�(jk  �*XLMEAHUSDC2022-07-20 00:00:003000.00000000�jm  jn  jo  ju  u}�(jk  �'XLMEAHUSDC2022-07 00:00:003000.00000000�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-07-20 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-07 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-07-20 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-07 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-07-20 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-07 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-07-20 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-07 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-07-20 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-07 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-07-20 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-07 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-07-20 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-07 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-07-20 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-07 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEAHUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEAHUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMENIUSDFF2022-05-24 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMENIUSDFF2022-05 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMENIUSDFF2022-05-24 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMENIUSDFF2022-05 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMENIUSDFF2022-08-18 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMENIUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMENIUSDFF2022-08-18 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMENIUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMENIUSDFF2022-08-18 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMENIUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMENIUSDFF2022-08-18 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMENIUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMENIUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMENIUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMENIUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMENIUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMENIUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMENIUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMENIUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMENIUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMENIUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMENIUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMENIUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMENIUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEPBUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEPBUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEPBUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEPBUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEPBUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEPBUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEZSUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEZSUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMEZSUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMEZSUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMECAUSDFF2022-05-24 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMECAUSDFF2022-05 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMECAUSDFF2022-05-24 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMECAUSDFF2022-05 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMECAUSDFF2022-08-05 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMECAUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMECAUSDFF2022-08-05 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMECAUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMECAUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMECAUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �XLMECAUSDFF2022-08-19 00:00:00�jm  jn  jo  ju  u}�(jk  �XLMECAUSDFF2022-08 00:00:00�jm  jn  jo  ju  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  �buyer�jo  jr  �ARRAY���R�u}�(jk  �lei:test_lme_executing_entity�jm  �reportDetails.executingEntity�jo  ju  u}�(jk  �id:lme�jm  �seller�jo  j�  u}�(jk  �id:una�jm  �counterparty�jo  ju  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:sfl�jm  j�  jo  ju  ue]�(}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �id:gsf�jm  j�  jo  ju  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:jpm�jm  j�  jo  ju  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:una�jm  j�  jo  ju  ue]�(}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �id:jpm�jm  j�  jo  ju  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:sfl�jm  j�  jo  ju  ue]�(}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �id:una�jm  j�  jo  ju  ue]�(}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �id:jpm�jm  j�  jo  ju  ue]�(}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �id:sfl�jm  j�  jo  ju  ue]�(}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  �clientIdentifiers.client�jo  j�  u}�(jk  �id:tl�jm  �trader�jo  j�  ue]�(}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:emv�jm  j�  jo  ju  ue]�(}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �id:sfl�jm  j�  jo  ju  ue]�(}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:ms�jm  jX  jo  j�  u}�(jk  �id:ms�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:ms�jm  jX  jo  j�  u}�(jk  �id:ms�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:ms�jm  jX  jo  j�  u}�(jk  �id:ms�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:ms�jm  jX  jo  j�  u}�(jk  �id:ms�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:ms�jm  jX  jo  j�  u}�(jk  �id:ms�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:ms�jm  jX  jo  j�  u}�(jk  �id:ms�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:ms�jm  jX  jo  j�  u}�(jk  �id:ms�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:ms�jm  jX  jo  j�  u}�(jk  �id:ms�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:ms�jm  jX  jo  j�  u}�(jk  �id:ms�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:ms�jm  jX  jo  j�  u}�(jk  �id:ms�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:ms�jm  jX  jo  j�  u}�(jk  �id:ms�jm  j[  jo  j�  ue]�(}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �id:emv�jm  j�  jo  ju  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:sfl�jm  j�  jo  ju  ue]�(}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �id:nhf�jm  j�  jo  ju  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:nhf�jm  j�  jo  ju  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:np�jm  jX  jo  j�  u}�(jk  �id:np�jm  j[  jo  j�  ue]�(}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �id:sfl�jm  j�  jo  ju  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:tl�jm  jX  jo  j�  u}�(jk  �id:tl�jm  j[  jo  j�  ue]�(}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �id:mfl�jm  j�  jo  ju  ue]�(}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �id:xxx�jm  j�  jo  ju  u}�(jk  �id:np�jm  jX  jo  j�  u}�(jk  �id:np�jm  j[  jo  j�  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:nhf�jm  j�  jo  ju  ue]�(}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �id:sfl�jm  j�  jo  ju  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:snc�jm  j�  jo  ju  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:sfl�jm  j�  jo  ju  ue]�(}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �id:nhf�jm  j�  jo  ju  ue]�(}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �id:nhf�jm  j�  jo  ju  ue]�(}�(jk  �id:lme�jm  j�  jo  j�  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  ju  u}�(jk  �lei:test_lme_executing_entity�jm  j�  jo  j�  u}�(jk  �id:snc�jm  j�  jo  ju  ue�lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��id:lme��lei:test_lme_executing_entity��lei:test_lme_executing_entity��id:lme��lei:test_lme_executing_entity��id:lme��id:lme��id:lme��id:lme��id:lme��lei:test_lme_executing_entity��id:lme��id:lme��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��id:lme��lei:test_lme_executing_entity��id:lme��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��id:lme��id:lme��id:lme��id:lme��lei:test_lme_executing_entity��lei:test_lme_executing_entity��id:lme��id:lme��lei:test_lme_executing_entity��id:lme��lei:test_lme_executing_entity��lei:test_lme_executing_entity��id:lme��lei:test_lme_executing_entity��id:lme��id:lme��id:lme��lei:test_lme_executing_entity��id:lme��id:lme��lei:test_lme_executing_entity��id:lme��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��id:lme��lei:test_lme_executing_entity��lei:test_lme_executing_entity��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��id:lme��lei:test_lme_executing_entity��id:lme��lei:test_lme_executing_entity��id:lme��id:lme��id:lme��id:lme��id:lme��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��lei:test_lme_executing_entity��id:lme��id:lme��lei:test_lme_executing_entity��lei:test_lme_executing_entity��id:lme��lei:test_lme_executing_entity��id:lme��id:lme��lei:test_lme_executing_entity��id:lme��lei:test_lme_executing_entity��id:una��id:sfl��id:gsf��id:jpm��id:una��id:jpm��id:sfl��id:una��id:jpm��id:sfl��id:xxx��id:xxx��id:emv��id:sfl��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:xxx��id:emv��id:sfl��id:nhf��id:xxx��id:xxx��id:xxx��id:nhf��id:xxx��id:xxx��id:xxx��id:xxx��id:sfl��id:xxx��id:xxx��id:mfl��id:xxx��id:nhf��id:sfl��id:snc��id:sfl��id:nhf��id:nhf��id:snc��pandas._libs.missing��NA���jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  �id:tl��id:tl�jl  jl  �id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:ms��id:ms��id:ms��id:ms��id:ms��id:tl��id:ms��id:ms��id:ms��id:ms��id:ms��id:ms�jl  jl  jl  �id:tl��id:tl��id:tl�jl  �id:np��id:tl��id:tl��id:tl�jl  �id:tl��id:tl�jl  �id:np�jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  �id:tl��id:tl�jl  jl  �id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:tl��id:ms��id:ms��id:ms��id:ms��id:ms��id:tl��id:ms��id:ms��id:ms��id:ms��id:ms��id:ms�jl  jl  jl  �id:tl��id:tl��id:tl�jl  �id:np��id:tl��id:tl��id:tl�jl  �id:tl��id:tl�jl  �id:np�jl  jl  jl  jl  jl  jl  jl  �test_lme_executing_entity��test_lme_executing_entity��lme��test_lme_executing_entity��test_lme_executing_entity��lme��test_lme_executing_entity��lme��lme��lme��lme��lme��test_lme_executing_entity��lme��lme��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity�e(�test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��lme��test_lme_executing_entity��lme��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��lme��lme��lme��lme��test_lme_executing_entity��test_lme_executing_entity��lme��lme��test_lme_executing_entity��lme��test_lme_executing_entity��test_lme_executing_entity��lme��test_lme_executing_entity��lme��lme��lme��test_lme_executing_entity��lme��lme��test_lme_executing_entity��lme��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��lme��test_lme_executing_entity��test_lme_executing_entity��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��lme��test_lme_executing_entity��lme��test_lme_executing_entity��lme��lme��lme��lme��lme��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��lme��lme��test_lme_executing_entity��test_lme_executing_entity��lme��test_lme_executing_entity��lme��lme��test_lme_executing_entity��lme��test_lme_executing_entity��una��sfl��gsf��jpm��una��jpm��sfl��una��jpm��sfl��xxx��xxx��emv��sfl��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��xxx��emv��sfl��nhf��xxx��xxx��xxx��nhf��xxx��xxx��xxx��xxx��sfl��xxx��xxx��mfl��xxx��nhf��sfl��snc��sfl��nhf��nhf��snc�jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  �tl��tl�jl  jl  �tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��ms��ms��ms��ms��ms��tl��ms��ms��ms��ms��ms��ms�jl  jl  jl  �tl��tl��tl�jl  �np��tl��tl��tl�jl  �tl��tl�jl  �np�jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  �tl��tl�jl  jl  �tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��tl��ms��ms��ms��ms��ms��tl��ms��ms��ms��ms��ms��ms�jl  jl  jl  �tl��tl��tl�jl  �np��tl��tl��tl�jl  �tl��tl�jl  �np�jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  �test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity��test_lme_executing_entity�]�(jj  jv  jx  jz  j|  j~  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j  j  e]�(j�  j�  j  j  j
  j  e]�(j�  j�  j  j  j  j  e]�(j�  j�  j  j  j  j  e]�(j�  j�  j!  j#  j%  j'  e]�(j�  j�  j*  j,  j.  j0  e]�(j�  j�  j3  j5  j7  j9  e]�(j�  j�  j<  j>  j@  jB  e]�(j�  j�  jE  jG  jI  jK  e]�(j�  j�  jN  jP  jR  jT  jV  jY  e]�(j�  j�  j]  j_  ja  jc  je  jg  e]�(j�  j�  jj  jl  jn  jp  e]�(j�  j�  js  ju  jw  jy  e]�(j�  j�  j|  j~  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j   j  j  j  j  e]�(j�  j�  j  j
  j  j  j  j  e]�(j  j  j  j  j  j  j   j"  e]�(j  j	  j%  j'  j)  j+  j-  j/  e]�(j  j  j2  j4  j6  j8  j:  j<  e]�(j  j  j?  jA  jC  jE  jG  jI  e]�(j  j  jL  jN  jP  jR  jT  jV  e]�(j  j  jY  j[  j]  j_  ja  jc  e]�(j   j"  jf  jh  jj  jl  jn  jp  e]�(j%  j'  js  ju  jw  jy  j{  j}  e]�(j*  j,  j�  j�  j�  j�  j�  j�  e]�(j/  j1  j�  j�  j�  j�  j�  j�  e]�(j4  j6  j�  j�  j�  j�  j�  j�  e]�(j9  j;  j�  j�  j�  j�  j�  j�  e]�(j>  j@  j�  j�  j�  j�  j�  j�  e]�(jC  jE  j�  j�  j�  j�  j�  j�  e]�(jH  jJ  j�  j�  j�  j�  j�  j�  e]�(jM  jO  j�  j�  j�  j�  j�  j�  e]�(jR  jT  j�  j�  j�  j�  j�  j�  e]�(jW  jY  j�  j�  j�  j�  j�  j�  e]�(j\  j^  j  j  j  j  j
  j  e]�(ja  jc  j  j  j  j  j  j  e]�(jf  jh  j  j  j   j"  j$  j&  e]�(jk  jm  j)  j+  j-  j/  j1  j3  e]�(jp  jr  j6  j8  j:  j<  j>  j@  e]�(ju  jw  jC  jE  jG  jI  jK  jM  e]�(jz  j|  jP  jR  jT  jV  e]�(j  j�  jY  j[  j]  j_  e]�(j�  j�  jb  jd  jf  jh  e]�(j�  j�  jk  jm  jo  jq  js  ju  e]�(j�  j�  jx  jz  j|  j~  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j  j  j  e]�(j�  j�  j  j
  j  j  e]�(j�  j�  j  j  j  j  e]�(j�  j�  j  j  j  j   e]�(j�  j�  j#  j%  j'  j)  e]�(j�  j�  j,  j.  j0  j2  e]�(j�  j�  j5  j7  j9  j;  e]�(j�  j�  j>  j@  jB  jD  e�Order�j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  �
OrderState�j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j  j   j!  j"  j#  j$  j%  j&  j'  j(  j)  j*  j+  j,  j-  j.  j/  j0  j1  j2  j3  j4  j5  j6  j7  j8  j9  j:  j;  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  jF  jG  jH  jI  jJ  jK  jL  jM  jN  jO  jP  jQ  jR  jS  jT  jU  jV  jW  jX  jY  jZ  j[  j\  j]  j^  j_  j`  ja  jb  jc  jd  je  jf  jg  j  j   j!  j"  j#  j$  j%  e(j&  j'  j(  j)  j*  j+  j,  j-  j.  j/  j0  j1  j2  j3  j4  j5  j6  j7  j8  j9  j:  j;  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  jF  jG  jH  jI  jJ  jK  jL  jM  jN  jO  jP  jQ  jR  jS  jT  jU  jV  jW  jX  jY  jZ  j[  j\  j]  j^  j_  j`  ja  jb  jc  jd  je  jf  jg  �
102.000000�G@�     G@������G@�N\(��G@�N\(��G@�N\(��G@�B     G@�N\(��G@�N\(��G@�     G@�     G@�     G@��     G@�D�    G@�     G@�P     G@�P     G@�P     G@�P     G@�P     G@�N     G@�P     G@�P     G@�Q     G@�Q     G@�Q     G@�N     G@�L     G@�L     G@�N     G@�N     G@�N     G@�J     G@�J     G@�N     G@�N     G@�N     G@�N     G@�     G@�     G@�     G@�     G@�     G@�J     G@�     G@�     G@�     G@�     G@�     G@�     G@�K�    G@�X     G@�&�    G@�#�    G@�#�    G@�#�    G@�&     G@�+     G@�#�    G@�#�    G@�#�    G@�]�    G@��     G@��     G@��     G@�<     G@�5�3333G@�    G@     G@�w�    G@�yl����G@�z,����G@�z@    j  j   j!  j"  j#  j$  j%  j&  j'  j(  j)  j*  j+  j,  j-  j.  j/  j0  j1  j2  j3  j4  j5  j6  j7  j8  j9  j:  j;  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  jF  jG  jH  jI  jJ  jK  jL  jM  jN  jO  jP  jQ  jR  jS  jT  jU  jV  jW  jX  jY  jZ  j[  j\  j]  j^  j_  j`  ja  jb  jc  jd  je  jf  jg  �2022-05-19T16:43:00.000000Z��2022-05-20T13:30:00.000000Z��2022-05-20T13:17:00.000000Z��2022-05-20T15:10:00.000000Z��2022-05-20T15:10:00.000000Z��2022-05-20T15:10:00.000000Z��2022-05-20T15:05:00.000000Z��2022-05-20T15:10:00.000000Z��2022-05-20T15:10:00.000000Z��2022-05-20T13:30:00.000000Z��2022-05-20T13:06:00.000000Z��2022-05-20T13:06:00.000000Z��2022-05-20T12:59:00.000000Z��2022-05-20T15:05:00.000000Z��2022-05-20T16:02:00.000000Z��2022-05-20T15:01:00.000000Z��2022-05-20T15:01:00.000000Z��2022-05-20T15:01:00.000000Z��2022-05-20T15:01:00.000000Z��2022-05-20T15:01:00.000000Z��2022-05-20T15:01:00.000000Z��2022-05-20T15:01:00.000000Z��2022-05-20T15:01:00.000000Z��2022-05-20T15:01:00.000000Z��2022-05-20T15:01:00.000000Z��2022-05-20T15:01:00.000000Z��2022-05-20T15:00:00.000000Z��2022-05-20T15:01:00.000000Z��2022-05-20T15:01:00.000000Z��2022-05-20T15:01:00.000000Z��2022-05-20T15:01:00.000000Z��2022-05-20T15:01:00.000000Z��2022-05-20T15:00:00.000000Z��2022-05-20T15:00:00.000000Z��2022-05-20T15:00:00.000000Z��2022-05-20T15:00:00.000000Z��2022-05-20T15:00:00.000000Z��2022-05-20T15:00:00.000000Z��2022-05-20T13:13:00.000000Z��2022-05-20T13:14:00.000000Z��2022-05-20T13:14:00.000000Z��2022-05-20T13:14:00.000000Z��2022-05-20T13:14:00.000000Z��2022-05-20T15:00:00.000000Z��2022-05-20T13:13:00.000000Z��2022-05-20T13:13:00.000000Z��2022-05-20T13:13:00.000000Z��2022-05-20T13:13:00.000000Z��2022-05-20T13:13:00.000000Z��2022-05-20T13:13:00.000000Z��2022-05-20T13:04:00.000000Z��2022-05-20T14:58:00.000000Z��2022-05-20T09:44:00.000000Z��2022-05-20T11:14:00.000000Z��2022-05-20T11:14:00.000000Z��2022-05-20T11:14:00.000000Z��2022-05-20T09:44:00.000000Z��2022-05-20T13:05:00.000000Z��2022-05-20T11:14:00.000000Z��2022-05-20T11:14:00.000000Z��2022-05-20T11:14:00.000000Z��2022-05-20T14:58:00.000000Z��2022-05-20T12:52:00.000000Z��2022-05-20T12:52:00.000000Z��2022-05-20T12:49:00.000000Z��2022-05-20T12:55:00.000000Z��2022-05-20T12:54:00.000000Z��2022-05-20T15:39:00.000000Z��2022-05-20T12:36:00.000000Z��2022-05-20T15:39:00.000000Z��2022-05-20T12:34:00.000000Z��2022-05-20T12:34:00.000000Z��2022-05-20T12:34:00.000000Z�j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�j�	  G@�     G@������G@�N\(��G@�N\(��G@�N\(��G@�B     G@�N\(��G@�N\(��G@�     G@�     G@�     G@��     G@�D�    G@�     G@�P     G@�P     G@�P     G@�P     G@�P     G@�N     G@�P     G@�P     G@�Q     G@�Q     G@�Q     G@�N     G@�L     G@�L     G@�N     G@�N     G@�N     G@�J     G@�J     G@�N     G@�N     G@�N     G@�N     G@�     G@�     G@�     G@�     G@�     G@�J     G@�     G@�     G@�     G@�     G@�     G@�     G@�K�    G@�X     G@�&�    G@�#�    G@�#�    G@�#�    G@�&     G@�+     G@�#�    G@�#�    G@�#�    G@�]�    G@��     G@��     G@��     G@�<     G@�5�3333G@�    G@     G@�w�    G@�yl����G@�z,����G@�z@    �MONE�j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  �UNIT�j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  ��%      j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  �Market Side�j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  e(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �option��future�j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  et�b�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KKI��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�REVERSAL��NORMAL��GIVE-UP EXECUTOR�j�	  j�	  j�	  j�	  �
CORRECTION�j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  et�b�_dtype�j�	  �StringDtype���)��ubj�	  )��}�(j�	  hhK ��h��R�(KKI��j�	  �]�(�25847��37276��44452��48227��51890��62611��48901��49047��62915�j
  �33428��33433��33789�j
  �54763��46496��46498��46497��46499��46500��46424��46442��46441��46443��46444��46445��46412��46419��46420��46421��46422��46423��46398��46399��46408��46409��46410��46411��34338��34439��34441��34443��34445��46397��34258��34332��34333��34334��34335��34336��34224��46478��21494��24981��24982��24983�j=
  �33374�j>
  j?
  j@
  j<
  �31096��31097��33010��31449��34296��52460��31456�jG
  �33140��33154��32714�et�bj 
  j
  )��ubj�	  )��}�(j�	  hhK ��h��R�(KKI��j�	  �]�(�75400�jl  �SIG�jU
  jU
  jU
  jl  jU
  jU
  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  et�bj 
  j
  )��ubj�	  )��}�(j�	  hhK ��h��R�(KKI��j�	  �]�(jl  jl  jl  jl  jl  jl  jl  jl  jl  jl  �TAAH20220520X0005851��TAAH20220520X0005853�jl  jl  �TAAH20220520X0010186��TAAH20220520X0008539��TAAH20220520X0008541��TAAH20220520X0008540��TAAH20220520X0008542��TAAH20220520X0008543��TAAH20220520X0008530��TAAH20220520X0008535��TAAH20220520X0008534��TAAH20220520X0008536��TAAH20220520X0008537��TAAH20220520X0008538��TAAH20220520X0008523��TAAH20220520X0008525��TAAH20220520X0008526��TAAH20220520X0008527��TAAH20220520X0008528��TAAH20220520X0008529��TAAH20220520X0008514��TAAH20220520X0008515��TAAH20220520X0008519��TAAH20220520X0008520��TAAH20220520X0008521��TAAH20220520X0008522��TAAH20220520X0006048��TAAH20220520X0006063��TAAH20220520X0006065��TAAH20220520X0006067��TAAH20220520X0006069��TAAH20220520X0008513��TAAH20220520X0006041��TAAH20220520X0006042��TAAH20220520X0006043��TAAH20220520X0006044��TAAH20220520X0006045��TAAH20220520X0006046�jl  jl  jl  �TANI20220520X0000579��TANI20220520X0000582��TANI20220520X0000585�jl  �TANI20220520X0000827��TANI20220520X0000579��TANI20220520X0000582��TANI20220520X0000585�jl  �TAPB20220520X0002381��TAPB20220520X0002382�jl  �TAZS20220520X0002353�jl  jl  jl  jl  jl  jl  jl  et�bj 
  j
  )��ubhhK ��h��R�(KKKI��h�f8�����R�(KhwNNNJ����J����K t�b�B�       @@      4@      4@      D@      D@      D@      D@      D@      D@      4@      �?      @      $@      D@      @      �?      �?      �?      �?      @       @      �?      �?      �?      �?      @      �?       @      �?      �?      @      �?      �?       @      �?      �?      �?      �?       @       @       @       @      �?       @      @      �?      �?      �?       @       @      �?      �?      2@       @       @      ,@      2@      �?       @       @      ,@      �?      @      �?      @      �?      �?      �?      �?      �?      �?      @      @     @@      4@      4@      D@      D@      D@      D@      D@      D@      4@      �?      @      $@      D@      @      �?      �?      �?      �?      @       @      �?      �?      �?      �?      @      �?       @      �?      �?      @      �?      �?       @      �?      �?      �?      �?       @       @       @       @      �?       @      @      �?      �?      �?       @       @      �?      �?      2@       @       @      ,@      2@      �?       @       @      ,@      �?      @      �?      @      �?      �?      �?      �?      �?      �?      @      @     @@      4@      4@      D@      D@      D@      D@      D@      D@      4@      �?      @      $@      D@      @      �?      �?      �?      �?      @       @      �?      �?      �?      �?      @      �?       @      �?      �?      @      �?      �?       @      �?      �?      �?      �?       @       @       @       @      �?       @      @      �?      �?      �?       @       @      �?      �?      2@       @       @      ,@      2@      �?       @       @      ,@      �?      @      �?      @      �?      �?      �?      �?      �?      �?      @      @�t�bhhK ��h��R�(KKKI��hv�BH                                                                  	       
                     
                                                                                                                                             !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       3       4       5       6       7       8       9       :       ;       <       =       >       ?       @       A       B       C       D       E       F       G       H       �t�bj�	  )��}�(j�	  hhK ��h��R�(KKI��j�	  �]�(�CURRENT�j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  et�bj 
  j
  )��ubj�	  )��}�(j�	  hhK ��h��R�(KKI��j�	  �]�(�USD�j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  et�bj 
  j
  )��ubj�	  )��}�(j�	  hhK ��h��R�(KKI��j�	  �]�(j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  et�bj 
  j
  )��ube]�(h
h}�(hhhK ��h��R�(KK8��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3h4h5h6h7h8h9h:h;h<h=h>h?h@hAhBhChDhEhFhGhHhIhJhKhLhPhRhThVhXhYhZh[h\h^h_hahchdhehfet�bhiNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hMat�bhiNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hNat�bhiNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hOat�bhiNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hQat�bhiNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hShUhbet�bhiNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hWat�bhiNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h]at�bhiNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h`at�bhiNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hgat�bhiNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs�hhK ��h��R�(KK8��h�i8�����R�(KhwNNNJ����J����K t�b�B�                                                                  	       
                     
                                                                                                                                             !       "       #       $       %       &       '       +       -       /       1       3       4       5       6       7       9       :       <       >       ?       @       A       �t�bu}�(j!  j�	  j"  �builtins��slice���K(K)K��R�u}�(j!  j
  j"  j0  K)K*K��R�u}�(j!  jN
  j"  j0  K*K+K��R�u}�(j!  jY
  j"  j0  K,K-K��R�u}�(j!  j�
  j"  hhK ��h��R�(KK��j)  �C.       0       =       �t�bu}�(j!  j�
  j"  j0  K2K3K��R�u}�(j!  j�
  j"  j0  K8K9K��R�u}�(j!  j�
  j"  j0  K;K<K��R�u}�(j!  j�
  j"  j0  KBKCK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.