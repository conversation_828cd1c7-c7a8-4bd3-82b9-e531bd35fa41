import os
from pathlib import Path
from unittest.mock import patch

import pandas as pd
import pytest
from prefect import context
from swarm.task.auditor import Auditor

from swarm_tasks.order.transformations.kooltra.monsas.kooltra_monsas_order_transformations import (
    KooltraMonsasOrderTransformations,
)
from swarm_tasks.order.transformations.kooltra.monsas.kooltra_monsas_order_transformations import (
    TempColumns,
)

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
FX_OPTIONS_TEST_FILE_PATH = TEST_FILES_DIR.joinpath(
    "Integration test FX Options-with-normalised-cols.csv"
)
FX_TRADES_TEST_FILE_PATH = TEST_FILES_DIR.joinpath(
    "Integration test FX Trade-with-normalised-cols.csv"
)
FX_OPTIONS_EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath(
    "fx_options_expected_result.pkl"
)
FX_TRADES_EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath("fx_trades_expected_result.pkl")


@pytest.fixture()
def fx_trades_source_frame() -> pd.DataFrame:
    df = pd.read_csv(FX_TRADES_TEST_FILE_PATH)
    df = df.set_index("__swarm_raw_index__")
    return df


@pytest.fixture()
def fx_options_source_frame() -> pd.DataFrame:
    df = pd.read_csv(FX_OPTIONS_TEST_FILE_PATH)
    df = df.set_index("__swarm_raw_index__")
    return df


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


class TestKooltraMonsasOrderTransformations:
    """
    Test suite for KooltraMonsasOrderTransformations
    """

    @pytest.mark.parametrize(
        "type_fx_file,test_file_path,expected_pickle_path",
        [
            ("FxTrades", FX_TRADES_TEST_FILE_PATH, FX_TRADES_EXPECTED_FILE_PATH),
            ("FxOptions", FX_OPTIONS_TEST_FILE_PATH, FX_OPTIONS_EXPECTED_FILE_PATH),
        ],
    )
    @patch.object(
        KooltraMonsasOrderTransformations,
        "_get_executing_entity",
    )
    def test_end_to_end_transformations(
        self,
        mock_exec_entity,
        type_fx_file,
        test_file_path,
        expected_pickle_path,
        fx_trades_source_frame,
        fx_options_source_frame,
        auditor,
    ):
        """Runs an end-to-end test for Fx Trade and Fx Option files using pickled data frames as the
        expected outputs.
        """
        os.environ["SWARM_FILE_URL"] = str(test_file_path)
        # Get the source frame from the appropriate fixture based on the file_type parameter
        source_frame = (
            fx_trades_source_frame
            if type_fx_file == "FxTrades"
            else fx_options_source_frame
        )
        # Mock a dummy lei as GetTenantLei needs to connect to Elastic
        mock_exec_entity.return_value = pd.DataFrame(
            data="lei:213800whtudjhvl2hg18",
            index=source_frame.index,
            columns=[TempColumns.EXECUTING_ENTITY_WITH_LEI],
        )
        task = KooltraMonsasOrderTransformations(
            source_frame=source_frame,
            logger=context.get("logger"),
            auditor=auditor,
        )
        result = task.process()
        result = result.drop(["sourceKey"], axis=1)

        expected = pd.read_pickle(expected_pickle_path)
        expected = expected.drop(["sourceKey"], axis=1)
        assert not pd.testing.assert_frame_equal(
            left=result, right=expected, check_dtype=False
        )
