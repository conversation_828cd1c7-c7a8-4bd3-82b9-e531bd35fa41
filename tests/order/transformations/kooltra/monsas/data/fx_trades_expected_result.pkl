��k=      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK8��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo��+executionDetails.passiveAggressiveIndicator�� executionDetails.tradingCapacity��	hierarchy��	_order.id��_orderState.id��_order.__meta_model__��_orderState.__meta_model__��orderIdentifiers.orderIdCode��!orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��"_orderState.priceFormingData.price��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��sourceIndex��	sourceKey��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��timestamps.tradingDateTime��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��$_orderState.transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��'_orderState.transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers��__expiry_date__��__option_strike_price__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C�                                          	       
                     
                                          �t�bh^�__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KKK��h�f8�����R�(KhlNNNJ����J����K t�b�B�      �cA    �SA    �SA    8�lA    8�lA    ��^AR��봚@    8�lA    ��^A    ��^A��Q�v�@    �SA    �SA    ��^A    8�lA�G�z�@t���n@Y�8��m@�|	n@�K�;��@Ǻ��F@�nض�@�����@���E@�1w-!?@n��S=@x
��@�Hèr@"p$�`s@��S ��@O��e��@"ߥ�%�@    �cA    �SA    �SA    8�lA    8�lA    ��^AR��봚@    8�lA    ��^A    ��^A��Q�v�@    �SA    �SA    ��^A    8�lA�G�z�@t���n@Y�8��m@�|	n@�K�;��@Ǻ��F@�nض�@�����@���E@�1w-!?@n��S=@x
��@�Hèr@"p$�`s@��S ��@O��e��@"ߥ�%�@    �cA    �SA    �SA    8�lA    8�lA    ��^AR��봚@    8�lA    ��^A    ��^A��Q�v�@    �SA    �SA    ��^A    8�lA�G�z�@�t�bhhK ��h��R�(KKK��hk�C�                                          	       
                     
                                          �t�bhhK ��h��R�(KK1K��h!�]�(�2��1�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��Kooltra�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��
2021-12-28��
2021-12-28��
2021-12-28��
2021-12-28��
2021-12-28��
2021-12-28��
2021-12-28��
2021-12-28��
2021-12-28��
2021-12-28��
2021-12-28��
2021-12-28��
2021-12-28��
2021-12-28��
2021-12-28��
2021-12-28��SELL��BUYI�h�h�h�h�h�h�h�h�h�h�h�h�h�h��NEWO�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��FILL�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��Market�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��Currency Pair: EURPLN��Currency Pair: EURPLN��Currency Pair: EURPLN��^Currency Pair: USDBRL, Fixing Date: 23/02/2022, Fixing Source: BRL PTAX (BRBR) Sao Paulo 13:15��^Currency Pair: USDBRL, Fixing Date: 27/04/2022, Fixing Source: BRL PTAX (BRBR) Sao Paulo 13:15��^Currency Pair: USDBRL, Fixing Date: 23/02/2022, Fixing Source: BRL PTAX (BRBR) Sao Paulo 13:15��Currency Pair: GBPPLN��^Currency Pair: USDBRL, Fixing Date: 27/04/2022, Fixing Source: BRL PTAX (BRBR) Sao Paulo 13:15��^Currency Pair: USDBRL, Fixing Date: 19/04/2022, Fixing Source: BRL PTAX (BRBR) Sao Paulo 13:15��^Currency Pair: USDBRL, Fixing Date: 19/04/2022, Fixing Source: BRL PTAX (BRBR) Sao Paulo 13:15��Currency Pair: GBPPLN��Currency Pair: EURPLN��Currency Pair: EURPLN��^Currency Pair: USDBRL, Fixing Date: 23/02/2022, Fixing Source: BRL PTAX (BRBR) Sao Paulo 13:15��^Currency Pair: USDBRL, Fixing Date: 23/02/2022, Fixing Source: BRL PTAX (BRBR) Sao Paulo 13:15��Currency Pair: GBPPLN��PASV�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��AOTC�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��
Standalone�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��99718.F1��99717.F1��99716.F1��99721.0��99724.0��99725.0��99729.0��99722.0��99726.0��99728.0��99730.0��99719.F1��99720.F1��99727.0��99723.0��99731.0�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�hÌOrder�h�h�h�h�h�h�h�h�h�h�h�h�h�h�hČ
OrderState�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�hÌ�/Users/<USER>/Documents/Work/swarms-core/swarm-tasks/tests/order/transformations/kooltra/monsas/data/Integration test FX Trade-with-normalised-cols.csv�h�h�h�h�h�h�h�h�h�h�h�h�h�h�hƌ2021-12-28T08:50:25.000000Z��2021-12-28T08:48:28.000000Z��2021-12-28T08:46:19.000000Z��2021-12-28T12:49:05.000000Z��2021-12-28T12:50:53.000000Z��2021-12-28T13:12:14.000000Z��2021-12-28T13:26:08.000000Z��2021-12-28T12:49:42.000000Z��2021-12-28T13:12:58.000000Z��2021-12-28T13:14:10.000000Z��2021-12-28T14:21:38.000000Z��2021-12-28T08:52:28.000000Z��2021-12-28T08:52:28.000000Z��2021-12-28T13:13:34.000000Z��2021-12-28T12:50:19.000000Z��2021-12-28T14:22:16.000000Z�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��pandas._libs.missing��NA���h�h�h�h�h�h�h�h�h�h�h�h�h�h�hٌPLN�h�hڌBRL�h�h�h�h�h�h�h�h�h�h�h�hڌMONE�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h܌EUR�h�h݌USD�h�hތGBP�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h܌Market Side�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h֌XOFF�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�]�(}�(�labelId��XXXXEURPLNFXFWD2022-01-13��path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�u}�(h�XXXXPLNEURFXFWD2022-01-13�h�h�h�h�ue]�(}�(h�XXXXEURPLNFXFWD2022-01-13�h�h�h�h�u}�(h�XXXXPLNEURFXFWD2022-01-13�h�h�h�h�ue]�(}�(h�XXXXEURPLNFXFWD2022-01-13�h�h�h�h�u}�(h�XXXXPLNEURFXFWD2022-01-13�h�h�h�h�ue]�(}�(h�XXXXUSDBRLFXFWD2022-02-25�h�h�h�h�u}�(h�XXXXBRLUSDFXFWD2022-02-25�h�h�h�h�ue]�(}�(h�XXXXUSDBRLFXFWD2022-04-29�h�h�h�h�u}�(h�XXXXBRLUSDFXFWD2022-04-29�h�h�h�h�ue]�(}�(h�XXXXUSDBRLFXFWD2022-02-25�h�h�h�h�u}�(h�XXXXBRLUSDFXFWD2022-02-25�h�h�h�h�ue]�}�(h�XXXXGBPPLNFXSPOT�h�h�h�h�ua]�(}�(h�XXXXUSDBRLFXFWD2022-04-29�h�h�h�h�u}�(h�XXXXBRLUSDFXFWD2022-04-29�h�h�h�h�ue]�(}�(h�XXXXUSDBRLFXFWD2022-04-22�h�h�h�h�u}�(h�XXXXBRLUSDFXFWD2022-04-22�h�h�h�h�ue]�(}�(h�XXXXUSDBRLFXFWD2022-04-22�h�h�h�h�u}�(h�XXXXBRLUSDFXFWD2022-04-22�h�h�h�h�ue]�(}�(h�XXXXGBPPLNFXFWD2022-01-13�h�h�h�h�u}�(h�XXXXPLNGBPFXFWD2022-01-13�h�h�h�h�ue]�(}�(h�XXXXEURPLNFXFWD2022-01-31�h�h�h�h�u}�(h�XXXXPLNEURFXFWD2022-01-31�h�h�h�h�ue]�(}�(h�XXXXEURPLNFXFWD2022-01-31�h�h�h�h�u}�(h�XXXXPLNEURFXFWD2022-01-31�h�h�h�h�ue]�(}�(h�XXXXUSDBRLFXFWD2022-02-25�h�h�h�h�u}�(h�XXXXBRLUSDFXFWD2022-02-25�h�h�h�h�ue]�(}�(h�XXXXUSDBRLFXFWD2022-02-25�h�h�h�h�u}�(h�XXXXBRLUSDFXFWD2022-02-25�h�h�h�h�ue]�(}�(h�XXXXGBPPLNFXFWD2022-01-31�h�h�h�h�u}�(h�XXXXPLNGBPFXFWD2022-01-31�h�h�h�h�ue]�(}�(h�lei:dummylei�h�buyer�h�h�ARRAY���R�u}�(h�lei:213800whtudjhvl2hg18�h�reportDetails.executingEntity�h�h�u}�(h�lei:213800whtudjhvl2hg18�h�seller�h�j@  u}�(h�id:lp�h�sellerDecisionMaker�h�j@  u}�(h�lei:dummylei�h�counterparty�h�h�u}�(h�id:lp�h�1tradersAlgosWaiversIndicators.executionWithinFirm�h�h�u}�(h�id:lp�h�trader�h�j@  ue]�(}�(h�lei:213800whtudjhvl2hg18�h�j=  h�j@  u}�(h�id:lp�h�buyerDecisionMaker�h�j@  u}�(h�lei:213800whtudjhvl2hg18�h�jC  h�h�u}�(h�lei:dummylei�h�jF  h�j@  u}�(h�lei:dummylei�h�jL  h�h�u}�(h�id:lp�h�jO  h�h�u}�(h�id:lp�h�jR  h�j@  ue]�(}�(h�lei:213800whtudjhvl2hg18�h�j=  h�j@  u}�(h�id:lp�h�jX  h�j@  u}�(h�lei:213800whtudjhvl2hg18�h�jC  h�h�u}�(h�lei:dummylei�h�jF  h�j@  u}�(h�lei:dummylei�h�jL  h�h�u}�(h�id:lp�h�jO  h�h�u}�(h�id:lp�h�jR  h�j@  ue]�(}�(h�lei:dummylei�h�j=  h�j@  u}�(h�lei:213800whtudjhvl2hg18�h�jC  h�h�u}�(h�lei:213800whtudjhvl2hg18�h�jF  h�j@  u}�(h�id:lp�h�jI  h�j@  u}�(h�lei:dummylei�h�jL  h�h�u}�(h�id:lp�h�jO  h�h�u}�(h�id:lp�h�jR  h�j@  ue]�(}�(h�lei:dummylei�h�j=  h�j@  u}�(h�lei:213800whtudjhvl2hg18�h�jC  h�h�u}�(h�lei:213800whtudjhvl2hg18�h�jF  h�j@  u}�(h�id:lp�h�jI  h�j@  u}�(h�lei:dummylei�h�jL  h�h�u}�(h�id:lp�h�jO  h�h�u}�(h�id:lp�h�jR  h�j@  ue]�(}�(h�lei:213800whtudjhvl2hg18�h�j=  h�j@  u}�(h�id:lp�h�jX  h�j@  u}�(h�lei:213800whtudjhvl2hg18�h�jC  h�h�u}�(h�lei:dummylei�h�jF  h�j@  u}�(h�lei:dummylei�h�jL  h�h�u}�(h�id:lp�h�jO  h�h�u}�(h�id:lp�h�jR  h�j@  ue]�(}�(h�lei:dummylei�h�j=  h�j@  u}�(h�lei:213800whtudjhvl2hg18�h�jC  h�h�u}�(h�lei:213800whtudjhvl2hg18�h�jF  h�j@  u}�(h�id:lp�h�jI  h�j@  u}�(h�lei:dummylei�h�jL  h�h�u}�(h�id:lp�h�jO  h�h�u}�(h�id:lp�h�jR  h�j@  ue]�(}�(h�lei:213800whtudjhvl2hg18�h�j=  h�j@  u}�(h�id:lp�h�jX  h�j@  u}�(h�lei:213800whtudjhvl2hg18�h�jC  h�h�u}�(h�lei:dummylei�h�jF  h�j@  u}�(h�lei:dummylei�h�jL  h�h�u}�(h�id:lp�h�jO  h�h�u}�(h�id:lp�h�jR  h�j@  ue]�(}�(h�lei:dummylei�h�j=  h�j@  u}�(h�lei:213800whtudjhvl2hg18�h�jC  h�h�u}�(h�lei:213800whtudjhvl2hg18�h�jF  h�j@  u}�(h�id:lp�h�jI  h�j@  u}�(h�lei:dummylei�h�jL  h�h�u}�(h�id:lp�h�jO  h�h�u}�(h�id:lp�h�jR  h�j@  ue]�(}�(h�lei:213800whtudjhvl2hg18�h�j=  h�j@  u}�(h�id:lp�h�jX  h�j@  u}�(h�lei:213800whtudjhvl2hg18�h�jC  h�h�u}�(h�lei:dummylei�h�jF  h�j@  u}�(h�lei:dummylei�h�jL  h�h�u}�(h�id:lp�h�jO  h�h�u}�(h�id:lp�h�jR  h�j@  ue]�(}�(h�lei:213800whtudjhvl2hg18�h�j=  h�j@  u}�(h�id:lp�h�jX  h�j@  u}�(h�lei:213800whtudjhvl2hg18�h�jC  h�h�u}�(h�lei:dummylei�h�jF  h�j@  u}�(h�lei:dummylei�h�jL  h�h�u}�(h�id:lp�h�jO  h�h�u}�(h�id:lp�h�jR  h�j@  ue]�(}�(h�lei:213800whtudjhvl2hg18�h�j=  h�j@  u}�(h�id:lp�h�jX  h�j@  u}�(h�lei:213800whtudjhvl2hg18�h�jC  h�h�u}�(h�lei:dummylei�h�jF  h�j@  u}�(h�lei:dummylei�h�jL  h�h�u}�(h�id:lp�h�jO  h�h�u}�(h�id:lp�h�jR  h�j@  ue]�(}�(h�lei:dummylei�h�j=  h�j@  u}�(h�lei:213800whtudjhvl2hg18�h�jC  h�h�u}�(h�lei:213800whtudjhvl2hg18�h�jF  h�j@  u}�(h�id:lp�h�jI  h�j@  u}�(h�lei:dummylei�h�jL  h�h�u}�(h�id:lp�h�jO  h�h�u}�(h�id:lp�h�jR  h�j@  ue]�(}�(h�lei:dummylei�h�j=  h�j@  u}�(h�lei:213800whtudjhvl2hg18�h�jC  h�h�u}�(h�lei:213800whtudjhvl2hg18�h�jF  h�j@  u}�(h�id:lp�h�jI  h�j@  u}�(h�lei:dummylei�h�jL  h�h�u}�(h�id:lp�h�jO  h�h�u}�(h�id:lp�h�jR  h�j@  ue]�(}�(h�lei:213800whtudjhvl2hg18�h�j=  h�j@  u}�(h�id:lp�h�jX  h�j@  u}�(h�lei:213800whtudjhvl2hg18�h�jC  h�h�u}�(h�lei:dummylei�h�jF  h�j@  u}�(h�lei:dummylei�h�jL  h�h�u}�(h�id:lp�h�jO  h�h�u}�(h�id:lp�h�jR  h�j@  ue]�(}�(h�lei:213800whtudjhvl2hg18�h�j=  h�j@  u}�(h�id:lp�h�jX  h�j@  u}�(h�lei:213800whtudjhvl2hg18�h�jC  h�h�u}�(h�lei:dummylei�h�jF  h�j@  u}�(h�lei:dummylei�h�jL  h�h�u}�(h�id:lp�h�jO  h�h�u}�(h�id:lp�h�jR  h�j@  ue�lei:213800whtudjhvl2hg18��lei:213800whtudjhvl2hg18��lei:213800whtudjhvl2hg18��lei:213800whtudjhvl2hg18��lei:213800whtudjhvl2hg18��lei:213800whtudjhvl2hg18��lei:213800whtudjhvl2hg18��lei:213800whtudjhvl2hg18��lei:213800whtudjhvl2hg18��lei:213800whtudjhvl2hg18��lei:213800whtudjhvl2hg18��lei:213800whtudjhvl2hg18��lei:213800whtudjhvl2hg18��lei:213800whtudjhvl2hg18��lei:213800whtudjhvl2hg18��lei:213800whtudjhvl2hg18��lei:dummylei��lei:213800whtudjhvl2hg18��lei:213800whtudjhvl2hg18��lei:dummylei��lei:dummylei��lei:213800whtudjhvl2hg18��lei:dummylei��lei:213800whtudjhvl2hg18��lei:dummylei��lei:213800whtudjhvl2hg18��lei:213800whtudjhvl2hg18��lei:213800whtudjhvl2hg18��lei:dummylei��lei:dummylei��lei:213800whtudjhvl2hg18��lei:213800whtudjhvl2hg18��lei:213800whtudjhvl2hg18��lei:dummylei��lei:dummylei��lei:213800whtudjhvl2hg18��lei:213800whtudjhvl2hg18��lei:dummylei��lei:213800whtudjhvl2hg18��lei:dummylei��lei:213800whtudjhvl2hg18��lei:dummylei��lei:dummylei��lei:dummylei��lei:213800whtudjhvl2hg18��lei:213800whtudjhvl2hg18��lei:dummylei��lei:dummylei��lei:dummylei��lei:dummylei��lei:dummylei��lei:dummylei��lei:dummylei��lei:dummylei��lei:dummylei��lei:dummylei��lei:dummylei��lei:dummylei��lei:dummylei��lei:dummylei��lei:dummylei��lei:dummylei��lei:dummylei��lei:dummylei�hٌid:lp��id:lp�h�hٌid:lp�hٌid:lp�hٌid:lp��id:lp��id:lp�h�hٌid:lp��id:lp��id:lp�h�hٌid:lp��id:lp�hٌid:lp�hٌid:lp�h�h�hٌid:lp��id:lp�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�hٌid:lp��id:lp��id:lp��id:lp��id:lp��id:lp��id:lp��id:lp��id:lp��id:lp��id:lp��id:lp��id:lp��id:lp��id:lp��id:lp�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�hٌid:lp��id:lp��id:lp��id:lp��id:lp��id:lp��id:lp��id:lp��id:lp��id:lp��id:lp��id:lp��id:lp��id:lp��id:lp��id:lp�]�(h�h�j;  jA  jD  jG  jJ  jM  jP  e]�(h�h�jT  jV  jY  j[  j]  j_  ja  e]�(h�h�jd  jf  jh  jj  jl  jn  jp  e]�(h�h�js  ju  jw  jy  j{  j}  j  e]�(j  j  j�  j�  j�  j�  j�  j�  j�  e]�(j  j  j�  j�  j�  j�  j�  j�  j�  e]�(j  j�  j�  j�  j�  j�  j�  j�  e]�(j  j  j�  j�  j�  j�  j�  j�  j�  e]�(j  j  j�  j�  j�  j�  j�  j�  j�  e]�(j  j  j�  j�  j�  j�  j�  j�  j�  e]�(j  j  j�  j�  j�  j�  j�  j�  j�  e]�(j"  j$  j�  j�  j�  j�  j�  j�  j�  e]�(j'  j)  j�  j�  j�  j   j  j  j  e]�(j,  j.  j	  j  j
  j  j  j  j  e]�(j1  j3  j  j  j  j  j   j"  j$  e]�(j6  j8  j'  j)  j+  j-  j/  j1  j3  e�
2022-01-13��
2022-01-13��
2022-01-13��
2022-02-25��
2022-04-29��
2022-02-25��
2021-12-30��
2022-04-29��
2022-04-22��
2022-04-22��
2022-01-13��
2022-01-31��
2022-01-31��
2022-02-25��
2022-02-25��
2022-01-31�et�bhhK ��h��R�(KKK��h!�]�(h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h7h8h9hChFet�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK1��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3h4h5h6h:h<h=h>h?h@hAhBhDhEhGhHhIhJhKhLhMhNhOhPhQhRhShThUhVhWhXhYhZh[et�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h\at�bh^Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hv�mgr_locs�hhK ��h��R�(KK��hk�C(                            !       �t�bu}�(j�  h�j�  �builtins��slice���KKK��R�u}�(j�  h�j�  hhK ��h��R�(KK1��hk�B�                                                                  	       
                     
                                                                                                          "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       3       4       5       6       �t�bu}�(j�  j�  j�  j  K7K8K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.