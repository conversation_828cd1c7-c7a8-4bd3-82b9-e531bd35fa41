from pathlib import Path

import pandas as pd
import pytest
from prefect import context
from swarm.task.auditor import Auditor

from swarm_tasks.order.transformations.oanda.oanda_v20_transformations import (
    OandaV20OrderTransformations,
)


TEST_FILE_PATH = Path(__file__).parent
TEST_DATA_DIR_PATH = TEST_FILE_PATH.joinpath("data")
INPUT_DATA = TEST_DATA_DIR_PATH.joinpath("input.pkl")
EXPECTED_RESULT_DATA = TEST_DATA_DIR_PATH.joinpath("expected_result.pkl")
INSTRUMENT_DATA = TEST_DATA_DIR_PATH.joinpath("instrument_data.pkl")


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="test", batch_id=1)


@pytest.fixture()
def input_source_frame() -> pd.DataFrame:
    return pd.read_pickle(INPUT_DATA)


@pytest.fixture()
def expected_result_frame() -> pd.DataFrame:
    return pd.read_pickle(EXPECTED_RESULT_DATA)


@pytest.fixture()
def instrument_data_frame() -> pd.DataFrame:
    return pd.read_pickle(INSTRUMENT_DATA)


class TestOandaV20OrderTransformations:
    def test_end_to_end(
        self,
        mocker,
        auditor: Auditor,
        input_source_frame: pd.DataFrame,
        expected_result_frame: pd.DataFrame,
        instrument_data_frame: pd.DataFrame,
    ):
        mocker.patch.object(
            OandaV20OrderTransformations,
            "read_instrument_data",
            return_value=instrument_data_frame,
        )

        task = OandaV20OrderTransformations(
            source_frame=input_source_frame,
            logger=context.get("logger"),
            auditor=auditor,
        )

        result = task.process().drop(["sourceKey"], axis=1)

        pd.testing.assert_frame_equal(
            left=result,
            right=expected_result_frame,
            check_dtype=False,
        )
