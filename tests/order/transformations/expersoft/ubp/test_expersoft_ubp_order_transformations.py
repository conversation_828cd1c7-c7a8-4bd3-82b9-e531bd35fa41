import os
from pathlib import Path
from unittest.mock import patch

import pandas as pd
import pytest
from prefect import context
from swarm.task.auditor import Auditor

from swarm_tasks.order.transformations.expersoft.ubp.expersoft_ubp_order_transformations import (
    ExpersoftUbpOrderTransformations,
)
from swarm_tasks.order.transformations.expersoft.ubp.expersoft_ubp_order_transformations import (
    SourceColumns,
)
from swarm_tasks.order.transformations.expersoft.ubp.expersoft_ubp_order_transformations import (
    TempColumns,
)

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
TEST_FILE_PATH = TEST_FILES_DIR.joinpath("order_expersoft_ubp_source.pkl")
EXPECTED_RESULT_PICKLE_PATH = TEST_FILES_DIR.joinpath("order_expersoft_ubp_result.pkl")


@pytest.fixture()
def source_frame() -> pd.DataFrame:
    df = pd.read_pickle(TEST_FILE_PATH).astype(str)
    for col in [
        SourceColumns.EXECUTION_PRICE,
        SourceColumns.ORDER_DETAILS_QUANTITY,
    ]:
        df[col] = df[col].astype("float")
    return df


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


class TestExpersoftUbpOrderTransformations:
    """
    Test suite for ExpersoftUbpOrderTransformations
    """

    @patch.object(
        ExpersoftUbpOrderTransformations,
        "_get_executing_entity",
    )
    def test_end_to_end_transformations(
        self,
        mock_exec_entity,
        source_frame,
        auditor,
    ):
        """Runs an end-to-end test for ExpersoftUbpOrderTransformations using pickled data frames as the
        expected outputs.
        """
        os.environ["SWARM_FILE_URL"] = str(TEST_FILE_PATH)

        # Mock a dummy lei as GetTenantLei needs to connect to Elastic
        mock_exec_entity.return_value = pd.DataFrame(
            data="lei:IPLPO8C7P68Q5FFRI280",
            index=source_frame.index,
            columns=[TempColumns.EXECUTING_ENTITY_WITH_LEI],
        )

        task = ExpersoftUbpOrderTransformations(
            source_frame=source_frame,
            logger=context.get("logger"),
            auditor=auditor,
        )

        result = task.process()
        result = result.drop(["sourceKey"], axis=1)

        expected = pd.read_pickle(EXPECTED_RESULT_PICKLE_PATH)
        expected = expected.drop(["sourceKey"], axis=1)
        assert not pd.testing.assert_frame_equal(
            left=result, right=expected, check_dtype=True
        )
