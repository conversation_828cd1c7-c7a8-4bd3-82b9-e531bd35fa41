��4      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK"��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�T.<PERSON><PERSON>��<PERSON><PERSON>UNT<PERSON><PERSON>��
ACCOUNTNUMBER��	ORDERDATE��	TRADEDATE��SETTLEMENTDATE��VALIDITY��TIMEORDERRECEIVED��TIMEORDERPLACEDTOTRADER��ORDERNATURE(DISCR.ADV.OREXEC.)��
DECISIONMAKER��)INVESTMENTDECISIONMAKER(PROVIDEROFADVICE)��
ORDERPLACEDBY�� ORDERTYPE(MARKET,LIMIT,VWAP,ETC)��ORDERDETAILS(SECURITY)��ISIN��ORDERDETAILS(BUY/SELL)��ORDERDETAILS(QUANTITY)�� UNITS/NOMINALVALUE/MONETARYVALUE��EXECUTIONPRICE��EXECUTIONPRICECCY��7EXECUTIONPRICECCY2(INCASEYOUSETTLEONADIFFERENTCURRENCY)��ACCRUEDINT(FORBONDS)��NETAMOUNT(+ACCRUED)��COUNTERPARTY��TIMEEXEC.PRICERECEIVED��	RATIONALE��VENUETRANSACTIONID��
SETTLEDATE��DVP��SUITABILITY��
ALGORITHMUSED��PRICEMULTIPLIER��VENUE�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C               �t�bhH�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h!�]�(�pandas._libs.missing��NA���hket�b�_dtype�h^�StringDtype���)��ubh`)��}�(hchhK ��h��R�(KK��h!�]�(hkhket�bhmho)��ubh`)��}�(hchhK ��h��R�(KK��h!�]�(�T.XEN�h�et�bhmho)��ubh`)��}�(hchhK ��h��R�(KK��h!�]�(�01032022�h�et�bhmho)��ubh`)��}�(hchhK ��h��R�(KK��h!�]�(�03032022�h�et�bhmho)��ubh`)��}�(hchhK ��h��R�(KK��h!�]�(�09:04:00��09:13:00�et�bhmho)��ubh`)��}�(hchhK ��h��R�(KK��h!�]�(�09:04:00��09:13:00�et�bhmho)��ubh`)��}�(hchhK ��h��R�(KK��h!�]�(�Execution Only�h�et�bhmho)��ubh`)��}�(hchhK ��h��R�(KK��h!�]�(�Client�h�et�bhmho)��ubh`)��}�(hchhK ��h��R�(KK��h!�]�(�James Laishley�h�et�bhmho)��ubh`)��}�(hchhK ��h��R�(KK��h!�]�(�Market�h�et�bhmho)��ubh`)��}�(hchhK ��h��R�(KK��h!�]�(�FR0000121329��DE000A1EWWW0�et�bhmho)��ubh`)��}�(hchhK ��h��R�(KK��h!�]�(�SELL��BUY�et�bhmho)��ubh`)��}�(hchhK ��h��R�(KK��h!�]�(�Units�h�et�bhmho)��ubhhK ��h��R�(KKK��h�f8�����R�(KhVNNNJ����J����K t�b�C0     d�@     ��@S�
c�Z@
p?j@      �      ��t�bhhK ��h��R�(KKK��h!�]�(�"Clucasgray Global Fund SPC Limited�j  �0835-1881565-55-000�j  �01032022�j  �DAY�j  G�      G�      �HO FP Equity��
ADS GY Equity�G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �N�j  G�      G�      et�bh`)��}�(hchhK ��h��R�(KK��h!�]�(�EUR�j  et�bhmho)��ubh`)��}�(hchhK ��h��R�(KK��h!�]�(�Credit Suisse Zurich�j  et�bhmho)��ubh`)��}�(hchhK ��h��R�(KK��h!�]�(�15:55:00��15:56:00�et�bhmho)��ube]�(h
h}�(hhhK ��h��R�(KK��h!�]�hDat�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hFat�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h(at�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h,at�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h1at�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h6h8hEet�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h&h'h)h+h0h3h:h;h<h?h@hAhBhCet�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h9at�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h=at�bhHNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bhHNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�ha�mgr_locs��builtins��slice���KK K��R�u}�(j�  hqj�  j�  K!K"K��R�u}�(j�  hzj�  j�  K KK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KK	K��R�u}�(j�  h�j�  j�  K	K
K��R�u}�(j�  h�j�  j�  K
KK��R�u}�(j�  h�j�  j�  KK
K��R�u}�(j�  h�j�  j�  K
KK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  hhK ��h��R�(KK��hU�C                      �t�bu}�(j�  j  j�  hhK ��h��R�(KK��hU�Cp                                                                                                  �t�bu}�(j�  j
  j�  j�  KKK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j!  j�  j�  KKK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.