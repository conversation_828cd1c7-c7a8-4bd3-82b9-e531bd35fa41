from pathlib import Path

import pandas as pd
import pytest
from prefect import context
from swarm.task.auditor import Auditor

from swarm_tasks.order.transformations.beacon.beacon_lighthouse_order_transformations import (
    BeaconLighthouseOrderTransformations,
)

TEST_FILE_PATH = Path(__file__).parent
TEST_DATA_DIR_PATH = TEST_FILE_PATH.joinpath("data")

BEACON_INPUT_SOURCE_DATA = TEST_DATA_DIR_PATH.joinpath("beacon_input_source_data.pkl")
BEACON_EXPECTED_RESULT_DATA = TEST_DATA_DIR_PATH.joinpath(
    "beacon_expected_result_data.pkl"
)


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="test", batch_id=1)


@pytest.fixture()
def beacon_input_source_frame() -> pd.DataFrame:
    return pd.read_pickle(BEACON_INPUT_SOURCE_DATA)


@pytest.fixture()
def beacon_expected_result_frame() -> pd.DataFrame:
    return pd.read_pickle(BEACON_EXPECTED_RESULT_DATA)


class TestBeaconLighthouseOrderTransformations:
    """Test class for Lighthouse Beacon Order Primary Transformations"""

    def test_end_to_end_lighthouse_primary_transformations(
        self,
        auditor: Auditor,
        beacon_input_source_frame: pd.DataFrame,
        beacon_expected_result_frame: pd.DataFrame,
    ):
        task = BeaconLighthouseOrderTransformations(
            source_frame=beacon_input_source_frame,
            logger=context.get("logger"),
            auditor=auditor,
        )
        result = task.process().drop(["sourceKey"], axis=1)
        assert not pd.testing.assert_frame_equal(
            left=result,
            right=beacon_expected_result_frame,
            check_dtype=False,
        )
