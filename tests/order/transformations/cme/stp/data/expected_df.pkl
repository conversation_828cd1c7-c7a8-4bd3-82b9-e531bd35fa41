��7      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK2��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo��+executionDetails.passiveAggressiveIndicator�� executionDetails.tradingCapacity��	_order.id��_orderState.id��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers��_order.__meta_model__��_orderState.__meta_model__��
orderClass��"orderIdentifiers.aggregatedOrderId��(orderIdentifiers.initialOrderDesignation��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��.orderIdentifiers.tradingVenueTransactionIdCode��!orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��	sourceKey��timestamps.orderReceived��timestamps.orderSubmitted��timestamps.tradingDateTime��#transactionDetails.buySellIndicator��$_order.transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime��'_orderState.transactionDetails.quantity��__last_px__��
__symbol__��	__price__��__stop_px__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C0                                           �t�bhXNu��R�e]�(hhK ��h��R�(KKK��h�f8�����R�(KhfNNNJ����J����K t�b�C�     @o@     @o@     @o@     @o@     @o@     @o@     @o@     @o@     @o@     @o@     @o@     @o@     @o@     @o@     @o@     @o@     @o@     @o@      B@     @_@      B@     `e@     @_@     `e@�t�bhhK ��h��R�(KK-K��h!�]�(�SELL��BUYI�h}h|h|h}h|h}h}h|h|h}�CME STP�h~h~h~h~h~�
2022-05-31��
2022-05-31��
2022-05-31��
2022-05-31��
2022-05-31��
2022-05-31�h|h}h}h|h|h}�NEWO�h�h�h�h�h��FILL�h�h�h�h�h��Market�h�h�h�h�h��Ex-Pit�h�h�h�h�h��AGRE��PASV�h�h�h�h��AOTC��DEAL�h�h�h�h��C18464828|ESZ2P2800|2��C18464828|ESZ2C4500|1��C18464828|ESZ2P2800|1��C18464828|ESZ2P3800|2��C18464828|ESZ2C4500|2��C18464828|ESZ2P3800|1�h�h�h�h�h�h�]�(}�(�labelId��id:imc_trading_bv��path��buyer��type��se_schema.static.market��IdentifierType����ARRAY���R�u}�(h��id:castor_petroleum_ltd�h��seller�h�h�u}�(h��id:kyte_broking_ltd�h��counterparty�h�h��OBJECT���R�u}�(h��id:castor_petroleum_ltd�h��clientIdentifiers.client�h�h�u}�(h��id:sdas5�h��trader�h�h�ue]�}�(h��id:castor_petroleum_ltd�h�h�h�h�ua]�(}�(h��id:imc_trading_bv�h�h�h�h�u}�(h��id:castor_petroleum_ltd�h�h�h�h�u}�(h��id:kyte_broking_ltd�h�h�h�h�u}�(h��id:imc_trading_bv�h�h�h�h�u}�(h��id:sdas5�h�h�h�h�ue]�(}�(h��id:castor_petroleum_ltd�h�h�h�h�u}�(h��id:imc_trading_bv�h�h�h�h�u}�(h��id:kyte_broking_ltd�h�h�h�h�u}�(h��id:imc_trading_bv�h�h�h�h�u}�(h��id:sdas5�h�h�h�h�ue]�(}�(h��id:castor_petroleum_ltd�h�h�h�h�u}�(h��id:kyte_broking_ltd�h�h�h�h�u}�(h��id:castor_petroleum_ltd�h�h�h�h�u}�(h��id:sdas5�h�h�h�h�ue]�(}�(h��id:castor_petroleum_ltd�h�h�h�h�u}�(h��id:imc_trading_bv�h�h�h�h�u}�(h��id:kyte_broking_ltd�h�h�h�h�u}�(h��id:castor_petroleum_ltd�h�h�h�h�u}�(h��id:sdas5�h�h�h�h�ue�pandas._libs.missing��NA���h�h�h�h�hތid:imc_trading_bv�hތid:imc_trading_bv��id:castor_petroleum_ltd�hތid:castor_petroleum_ltd��id:castor_petroleum_ltd��id:castor_petroleum_ltd��id:castor_petroleum_ltd��id:imc_trading_bv��id:castor_petroleum_ltd��id:imc_trading_bv��id:kyte_broking_ltd�hތid:kyte_broking_ltd��id:kyte_broking_ltd��id:kyte_broking_ltd��id:kyte_broking_ltd�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�hތid:castor_petroleum_ltd�hތid:imc_trading_bv��id:imc_trading_bv��id:castor_petroleum_ltd��id:castor_petroleum_ltd��id:sdas5�hތid:sdas5��id:sdas5��id:sdas5��id:sdas5�]�(h�h�h�h�h�e]�h�a]�(h�h�h�h�h�e]�(h�h�h�h�h�e]�(h�h�h�h�e]�(h�h�h�h�h�e�Order�h�h�h�h�h��
OrderState�h�h�h�h�h��Block Trade�j   j   j   j   j   �$b1881fcc-e074-11ec-b2f6-0242c0a80006��$b1881fcc-e074-11ec-b2f6-0242c0a80006��$b1881fcc-e074-11ec-b2f6-0242c0a80006��$b1881fcc-e074-11ec-b2f6-0242c0a80006��$b1881fcc-e074-11ec-b2f6-0242c0a80006��$b1881fcc-e074-11ec-b2f6-0242c0a80006��	T93142955��	T93142955��	T93142955��	T93142955��	T93142955��	T93142955�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��18464828��18464828��18464828��18464828��18464828��18464828�j
  j  j  j  j  j  ��s3://kytebroking-tr.uat.steeleye.co/sftp/CMESTP_Test_Files/Test1/1b4206799a9cf1b447d71b9656c0937e228fc7da0f6da56f8c7d59afa5c9d299_1110.fix���s3://kytebroking-tr.uat.steeleye.co/sftp/CMESTP_Test_Files/Test1/3687e7d5430b68f5bd9a274c14d5af05246b5541b57b4fffea402ad8d9b01571_1107.fix���s3://kytebroking-tr.uat.steeleye.co/sftp/CMESTP_Test_Files/Test1/73f1e488a1b70c751fd8825de3aeeb9a8890044af8503eb2715522f5fa66b76f_1109.fix���s3://kytebroking-tr.uat.steeleye.co/sftp/CMESTP_Test_Files/Test1/9e7bc347b9f59b2c1fff01126ca2b629f12806d3bd80ff8916e59c2e5416e740_1108.fix���s3://kytebroking-tr.uat.steeleye.co/sftp/CMESTP_Test_Files/Test1/cca6c1ab21b02eb994dc82bd5e0a5e10ccd63ac7b694b2cfa03b336783ea785b_1112.fix���s3://kytebroking-tr.uat.steeleye.co/sftp/CMESTP_Test_Files/Test1/ea4336117ee1c70038dc016fb5113000aabbe623f1aadc6806de63a0f35a3da7_1111.fix��2022-05-31T09:12:59.258000Z��2022-05-31T09:12:59.109000Z��2022-05-31T09:12:59.199000Z��2022-05-31T09:12:59.167000Z��2022-05-31T09:12:59.380000Z��2022-05-31T09:12:59.343000Z�j  j  j  j  j  j  j  j  j  j  j  j  h|h}h}h|h|h}�Market Side�j  j  j  j  j  h�h�h�h�h�h�j  j  j  j  j  j  �
ESZ2 P2800��
ESZ2 C4500��
ESZ2 P2800��
ESZ2 P3800��
ESZ2 C4500��
ESZ2 P3800�h�h�h�h�h�h�et�bhhK ��h��R�(KKK��h!�]�(h�h�h�h�h�h�et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(hGhHhRhSet�bhXNu��R�h
h}�(hhhK ��h��R�(KK-��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3h4h5h6h7h8h9h:h;h<h=h>h?h@hAhBhChDhEhFhIhJhKhLhMhNhOhPhQhThUet�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hVat�bhXNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�ho�mgr_locs�hhK ��h��R�(KK��h�i8�����R�(KhfNNNJ����J����K t�b�C "       #       -       .       �t�bu}�(jP  hyjQ  hhK ��h��R�(KK-��jX  �Bh                                                                  	       
                     
                                                                                                                                             !       $       %       &       '       (       )       *       +       ,       /       0       �t�bu}�(jP  j)  jQ  �builtins��slice���K1K2K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.