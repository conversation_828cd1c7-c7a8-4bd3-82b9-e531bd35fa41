���      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK2��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo��+executionDetails.passiveAggressiveIndicator�� executionDetails.tradingCapacity��	_order.id��_orderState.id��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers��_order.__meta_model__��_orderState.__meta_model__��
orderClass��"orderIdentifiers.aggregatedOrderId��(orderIdentifiers.initialOrderDesignation��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��.orderIdentifiers.tradingVenueTransactionIdCode��!orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��	sourceKey��timestamps.orderReceived��timestamps.orderSubmitted��timestamps.tradingDateTime��#transactionDetails.buySellIndicator��$_order.transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime��'_orderState.transactionDetails.quantity��__last_px__��
__symbol__��	__price__��__stop_px__�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(hXN�start�K �stop�K�step�Ku��R�e]�(hhK ��h��R�(KKK��h�f8�����R�(K�<�NNNJ����J����K t�b�C      @�@     @�@     @�@     Ђ@�t�bhhK ��h��R�(KK-K��h!�]�(�SELL�hu�CME STP��
2023-01-09�hu�NEWO��FILL��Market��Ex-Pit��AGRE��AOTC��C19016141|A9NG3|2�h~]�(}�(�labelId��id:cme��path��buyer��type��se_schema.static.market��IdentifierType����ARRAY���R�u}�(h��id:st511�h��seller�h�h�u}�(h��id:cme�h��counterparty�h�h��OBJECT���R�u}�(h��id:st511�h��clientIdentifiers.client�h�h�u}�(h��id:byap�h��trader�h�h�ue�pandas._libs.missing��NA����id:cme��id:st511��id:cme�h�h�h�h��id:st511��id:byap�]�(h�h�h�h�h�e�Order��
OrderState��Block Trade��$99f77222-8fb0-11ed-b1fe-0242c0a80007��	D47889559�h~h~h~�19016141�h���s3://diogo.dev.steeleye.co/ingress/raw/order-feed-cme-stp-fix/14f768b77c3e5efb3e28ecc1626f650317742f15f1ed03a2031de7c44a826072_1168.fix��2023-01-09T09:40:03.692000Z�h�h�hu�Market Side�h}h��A9NG3�h�et�bhhK ��h��R�(KKK��h!�]�h�at�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(hGhHhRhSet�bhXNu��R�h
h}�(hhhK ��h��R�(KK-��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3h4h5h6h7h8h9h:h;h<h=h>h?h@hAhBhChDhEhFhIhJhKhLhMhNhOhPhQhThUet�bhXNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hVat�bhXNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hg�mgr_locs�hhK ��h��R�(KK��h�i8�����R�(KhlNNNJ����J����K t�b�C "       #       -       .       �t�bu}�(h�hrh�hhK ��h��R�(KK-��h��Bh                                                                  	       
                     
                                                                                                                                             !       $       %       &       '       (       )       *       +       ,       /       0       �t�bu}�(h�h�hٌbuiltins��slice���K1K2K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.