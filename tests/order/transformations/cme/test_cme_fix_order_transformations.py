import os
from pathlib import Path
from unittest.mock import patch

import pandas as pd
import pytest
from prefect import context
from swarm.task.auditor import Auditor

from swarm_tasks.order.transformations.cme.cme_fix_order_transformations import (
    CmeFixOrderTransformations,
)
from swarm_tasks.order.transformations.cme.cme_fix_order_transformations import (
    TempColumns,
)

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")

# The source file is a pickle of the df which should be used as  the source dataframe
SOURCE_PICKLE_FILE = TEST_FILES_DIR.joinpath(
    "cme_primary_transformation_source_frame.pkl"
)
EXPECTED_RESULT_PICKLE_PATH = TEST_FILES_DIR.joinpath(
    "cme_primary_transformations_target_df.pkl"
)


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


class TestCmeFixOrderTransformations:
    """
    Test suite for CME Order Fix Feed
    """

    @patch.object(
        CmeFixOrderTransformations,
        "_get_executing_entity_with_lei",
    )
    def test_end_to_end_cme_fix_order_transformations(
        self, mock_executing_entity, auditor
    ):
        os.environ["SWARM_FILE_URL"] = str(SOURCE_PICKLE_FILE)
        source_frame = pd.read_pickle(SOURCE_PICKLE_FILE)
        mock_executing_entity.return_value = pd.DataFrame(
            data="lei:894500WOTA5040KHGX73",
            index=source_frame.index,
            columns=[TempColumns.EXECUTING_ENTITY_WITH_LEI],
        )

        task = CmeFixOrderTransformations(
            source_frame=source_frame,
            logger=context.get("logger"),
            auditor=auditor,
        )
        result = task.process()
        result = result.drop(["sourceKey"], axis=1)

        expected = pd.read_pickle(EXPECTED_RESULT_PICKLE_PATH)
        pd.testing.assert_frame_equal(left=result, right=expected)
