��&.      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK7��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType��&executionDetails.outgoingOrderAddlInfo�� executionDetails.routingStrategy�� executionDetails.tradingCapacity��executionDetails.validityPeriod��	hierarchy��	_order.id��_orderState.id��_order.isSynthetic��_order.__meta_model__��_orderState.__meta_model__��
orderClass��"orderIdentifiers.aggregatedOrderId��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��.orderIdentifiers.tradingVenueTransactionIdCode��!orderIdentifiers.transactionRefNo�� priceFormingData.initialQuantity��"priceFormingData.remainingQuantity��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��sourceIndex��	sourceKey��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��timestamps.tradingDateTime��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��!transactionDetails.positionEffect��transactionDetails.quantity��$_order.transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime��multiLegReportingType��
__symbol__��	__price__��__last_px__��__stop_px__��__newo_in_file__��	__buyer__��
__seller__��
__client__��__counterparty__��__executing_entity__��__execution_within_firm__��__investment_decision__��
__trader__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(h�numpy.core.numeric��_frombuffer���(�x                            
                                    $       &       '       ,       4       8       :       �h�i8�����R�(K�<�NNNJ����J����K t�bK���C�t�R�h]Nu��R�e]�(hhK ��h��R�(KKK��h!�]�(�BUYI�hyhyhyhyhyhyhyhyhyhyhyhyhyhyhyhyhyhyhyhyhyhyhyhyhyhyhyhyhy�ICE POF Exchange�hzhzhzhzhzhzhzhzhzhzhzhzhzhz�
2025-08-29��
2025-08-29��
2025-08-29��
2025-08-29��
2025-08-29��
2025-08-29��
2025-08-29��
2025-08-29��
2025-08-29��
2025-08-29��
2025-08-29��
2025-08-29��
2025-08-29��
2025-08-29��
2025-08-29�hyhyhyhyhyhyhyhyhyhyhyhyhyhyhy�NEWO�h�h�h�h�h�h�h�h�h�h�h�h�h�h��pandas._libs.missing��NA���h��PARF�h��REME��CAME�h�h�h�h��FILL�h�h�h�h��Limit�h�h�h�h�h�h�h�h�h�h�h�h�h�h��
2555|MGUARROW��0|Memo updated|2555|MGUARROW��0|2555|MGUARROW��0|Memo updated|2555|MGUARROW��
2555|MGUARROW��userKilled|2555|MGUARROW��0|Memo updated|2555|MGUARROW��0|Memo updated|2555|MGUARROW��
2555|MGUARROW��
2555|MGUARROW��0|2555|MGUARROW��0|Memo updated|2555|MGUARROW��0|2555|MGUARROW��0|2555|MGUARROW��0|2555|MGUARROW�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��AOTC�h�h�h�h�h�h�h�h�h�h�h�h�h�h�G�      G�      G�      G�      ]��GTCV�ah�G�      G�      h�h�G�      G�      G�      G�      G�      �
Standalone�h�h�h�h�h�h�h�h�h�h�h�h�h�h��977931875|7827662��977931875|7827662��977931875|7827662��977931875|7827662��901806911|5919960��901806911|5919960��977931875|7827662��977931875|7827662��901806911|5919960��901806911|5919960��977931875|7827662��977931875|7827662��977931875|7827662��977931875|7827662��977931875|7827662�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bhf(�         �h�b1�����R�(Kh"NNNJ����J����K t�bKK��hnt�R�hhK ��h��R�(KKK��h!�]�(�Order�h�h�h�h�h�h�h�h�h�h�h�h�h�hÌ
OrderState�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��
Regular Trade�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��11000007251601��11000007251601��11000007251601��11000007251601��21000003190805��21000003190805��11000007251601��11000007251601��21000003190805��21000003190805��11000007251601��11000007251601��11000007251601��11000007251601��11000007251601�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��977931875:1:Ack��11000007251604:671:Correct��11000007251603��11000007251603:668:Correct��901806911:2:Ack��901806911:4:Unfill��11000007251607:667:Correct��11000007251606:670:Correct��901806911:1:Ack��901806911:3:Ack��11000007251607��11000007251605:669:Correct��11000007251605��11000007251606��11000007251604�et�bhf(�x            @�@      �     @�@      �      Y@      Y@      �      �      �?      Y@     @�@      �     @�@     @�@     @�@�h�f8�����R�(KhkNNNJ����J����K t�bKK��hnt�R�hhK ��h��R�(KKK��h!�]�(G@�@     G@�H     G@��     G@��     G@Y      G        G        G@��     G?�      G@Y      G        G@��     G@��     G@��     G@�H     G        G@E      G@sP     G@sP     G        G        G@��     G@$      G        G        G@��     G@*      G@*      G@$      G@E      h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�et�bhf(�x                            
                                    $       &       '       ,       4       8       :       �hjKK��hnt�R�hhK ��h��R�(KKK��h!�]�(��s3://arrow.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/20250829/09d7da80833f7a7512d4b564b6b5e4f307426cd70ed403ff7a30152ac3b51501_59527.fix���s3://arrow.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/20250829/0e382b8326f5552f630d4bf2caf51561df5f80c7f0090d5128967fa37b9737ff_59537.fix���s3://arrow.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/20250829/1041236b07b02e5a868b8fc120a0f8880988e96c242875216b0e58da0bef4887_59528.fix���s3://arrow.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/20250829/2f38601b4a9a632e52e3138fa6c367ef2e6e19356751145024a1f5a7c9dfa415_59534.fix���s3://arrow.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/20250829/322c871f524d048977f528772781ae86be10e652c0fdfe3921ced69d60c82f0d_59970.fix���s3://arrow.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/20250829/581c245e452f933f678ce186db9dfe70e8f525c0af8c6c8449cc7a6db5844900_59989.fix���s3://arrow.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/20250829/7062244ca81b0066ebf832cf9a696f5b145e951322fe5e98ba2e642252243bf1_59533.fix���s3://arrow.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/20250829/719ea2201a9b26778ddde77383cc692642ad6557efabe5ee5ef13f5aabfa43d5_59536.fix���s3://arrow.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/20250829/7baf4e9f4c9a5f272e4729e4ac1de9d98789202ed02805f2ea6e5efeb4b2aac8_59969.fix���s3://arrow.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/20250829/82c2bcc5899b45a692749dbf1f6448897f57af88328fa394a384307d66c769e0_59971.fix���s3://arrow.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/20250829/8351b004d91b0bff0d79ace9f939514604802ff84383b964ddc424a848fb0bea_59532.fix���s3://arrow.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/20250829/a7cc8bd6fbde8be78def97dc9b87ea732d3686d1378e2f6e4c80f44bfb909388_59535.fix���s3://arrow.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/20250829/ce20453012ac245967ab07ddf54fac72e5c2e05f70f6c69cdf2c1abdfbedc9ab_59530.fix���s3://arrow.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/20250829/dd047157cf515b4e7b19c5092e4fa6b9be22c5bdd46144760e633b3e98b9e1c6_59531.fix���s3://arrow.uat.steeleye.co/ingress/raw/order-feed-ice-pof-fix/20250829/e23c610d01fdc4878d54e75efa70615da069881cc7fd31ce04cb468ac6ba023f_59529.fix��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z��2025-08-29T15:26:15.283857Z��2025-08-29T15:33:36.484776Z��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z��2025-08-29T15:26:12.447177Z��2025-08-29T15:26:20.785956Z��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z��2025-08-29T15:26:15.283857Z��2025-08-29T15:33:36.484776Z��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z��2025-08-29T15:26:12.447177Z��2025-08-29T15:26:20.785956Z��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z��2025-08-29T15:26:15.283857Z��2025-08-29T15:33:36.484776Z��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z��2025-08-29T15:26:12.447177Z��2025-08-29T15:26:20.785956Z��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z��2025-08-29T12:38:37.259473Z�j  j  j
  j  j  j  j  j  j  j  j  j  j  j  j  hyhyhyhyhyhyhyhyhyhyhyhyhyhyhyh�h�h�h�h�h�h�h�h�h�h�h�h�h�hԌOpen�j8  j8  j8  j8  h�j8  j8  j8  j8  j8  j8  j8  j8  j8  G        G@E      G@sP     G@sP     G        G        G@��     G@$      G        G        G@��     G@*      G@*      G@$      G@E      �Market Side�j9  j9  j9  j9  j9  j9  j9  j9  j9  j9  j9  j9  j9  j9  h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�j  j  j
  j  j  j  j  j  j  j  j  j  j  j  j  �se_elastic_schema.static.mifid2��MultiLegReportingType����Outright���R�j?  j?  j?  j?  j?  j?  j?  j?  j?  j?  j?  j?  j?  j?  �7827662��7827662��7827662��7827662��5919960��5919960��7827662��7827662��5919960��5919960��7827662��7827662��7827662��7827662��7827662�et�bhf(�x                                             "@      "@                      "@      "@                                        �h�KK��hnt�R�hhK ��h��R�(KKK��h!�]�(G        G        G        G        G        G        G        G        G        G        G        G        G        G        G        h�h�h�h�h�h�h�h�h�h�h�h�h�h�h����������������et�bhhK ��h��R�(KKK��h!�]�(�id:1��id:1��id:1��id:1��id:1��id:1��id:1��id:1��id:1��id:1��id:1��id:1��id:1��id:1��id:1��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE�h��id:64120�h��id:64120��id:100��id:100��id:64120��id:64120��id:100�h�h��id:64120�h�h�h��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:ICE��id:1��id:1��id:1��id:1��id:1��id:1��id:1��id:1��id:1��id:1��id:1��id:1��id:1��id:1��id:1��id:3��id:3��id:3��id:3��id:3�h��id:3��id:3��id:3��id:3��id:3��id:3��id:3��id:3��id:3�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��id:mdelaney8��id:mdelaney8��id:mdelaney8��id:mdelaney8��id:mdelaney8��id:mdelaney8��id:mdelaney8��id:mdelaney8��id:mdelaney8��id:mdelaney8��id:mdelaney8��id:mdelaney8��id:mdelaney8��id:mdelaney8��id:mdelaney8�et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3et�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h5h6h7h8h9h:h;h<et�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h=at�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h>h?h@et�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hBhChDhEhFhGhHhIhJhKhLhMhNhOet�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hPat�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hQhRhSet�bh]Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hThUhVhWhXhYhZh[et�bh]Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hv�mgr_locs��builtins��slice���K KK��R�u}�(j#  h�j$  j'  KKK��R�u}�(j#  h�j$  j'  KKK��R�u}�(j#  h�j$  j'  KKK��R�u}�(j#  h�j$  j'  KKK��R�u}�(j#  h�j$  j'  KKK��R�u}�(j#  h�j$  j'  KK+K��R�u}�(j#  jS  j$  j'  K+K,K��R�u}�(j#  jV  j$  j'  K,K/K��R�u}�(j#  j\  j$  j'  K/K7K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.