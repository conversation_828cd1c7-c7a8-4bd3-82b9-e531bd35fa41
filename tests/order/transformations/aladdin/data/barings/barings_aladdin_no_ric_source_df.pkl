���4      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK8��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�order.activatedTimestampUtc��order.avgPrice��order.basketId��order.createdTimestampUtc��order.modifiedTimestampUtc��
order.orderId��order.orderQuantity��order.orderStatus��order.orderType��order.pmInitials��order.priceCcy��	order.ric��order.timeInForce��order.trader��order.tranType��orderdetail.orderId��orderdetail.orderDetailId��orderdetail.origOrderId��orderdetail.portfolioId��__order_type__��transaction.assetId��transaction.cptyId��transaction.dealingCapacity��transaction.execCptyId��transaction.maturity��transaction.orderId��transaction.placementId��transaction.portfolioId��transaction.portfolioTicker��transaction.ric��transaction.secDesc1��transaction.secGroup��transaction.secTicker��transaction.secType��transaction.tradeCoupon��transaction.tradeNum��transaction.tradePurpose��transaction.tradeQuantity��transaction.trader��transaction.underlyingSnpCusip��placement.orderId��placement.placementId��fill.dealingCapacity��
fill.exchange��fill.executedPrice��fill.executedQuantity��fill.executedTimestampUtc��fill.fillId��fill.placementId��
order.isin��placement.limitValue��placement.stopValue��transaction.isin��placement.sendTimeUtc��order.genComments��__aggregate_portfolio_id__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(h�numpy.core.numeric��_frombuffer���(�h                                                                       	       
                     �h�i8�����R�(K�<�NNNJ����J����K t�bK
���C�t�R�h^�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK
��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�2024-07-01T20:03:36.366Z��2024-07-01T20:48:11.723Z�h��2024-08-28T16:12:06.730Z��2024-08-28T16:12:06.710Z��2024-08-28T13:30:56.193Z�h�h�h��2024-08-28T14:19:30.076Z��2024-08-28T14:34:48.160Z��2024-08-26T16:53:37.503Z��2024-08-28T15:09:38.450Z�et�b�_dtype�hv�StringDtype���)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�pandas._libs.missing��NA���h�h�h�h�h�h�h�h�h�h��PosRoll26AUG24 12:43:02�h�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�2024-07-01T19:43:59.623Z��2024-07-01T19:43:59.553Z��2024-07-01T19:43:59.646Z��2024-08-28T16:09:51.320Z��2024-08-28T16:09:51.356Z��2024-08-28T12:54:14.966Z�h�h�h��2024-08-28T14:17:25.380Z��2024-08-28T14:34:48.160Z��2024-08-26T16:43:53.573Z��2024-08-28T15:09:38.450Z�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�2024-07-01T20:11:26.066Z��2024-07-01T20:48:44.096Z�h��2024-08-28T16:16:05.980Z��2024-08-28T16:17:06.050Z��2024-08-28T14:13:54.556Z�h�h�h��2024-08-28T14:25:04.436Z��2024-08-28T16:24:28.793Z��2024-08-26T16:54:38.813Z��2024-08-28T16:31:07.510Z�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�1475217��1475248��1500306��1552261��1727666��1676148�h�h�hь1551596��1551796��1552055��1552061�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�F�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�M�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�KWSO�h�h�BRKU�h�ADRI�h�h�h��DUTO��HUBE��AIDO�h�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�USD�j  j  j  j  j  j  j  j  �GBP��EUR�j  j  et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(h�h�h�h�h�h�h�h�h�h�h��TYU24�h�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�1�j  j  j  j  j  j  j  j  j  j  j  j  et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�CAAD�j"  j"  �DUTO�j#  �SHA�j$  j$  j$  j#  �AIDO�j%  j%  et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�SELLOPEN��BUY�j/  �RCVFIX INIT�j1  �SELL�j2  j2  j2  �RCVBASE INIT��PAYBASE UNWIND�j2  j4  et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�1475317��1475348��1500406��1552361��1727766��1676248�jC  jC  jC  h�h�h�h�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�1500317��1475748��1525406��1553861��1728166��1677248��1677348��1677448��1677548�h�h�h�h�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�1475217��1475248��1500306��1552261��1727666��1676148�jd  jd  jd  h�h�h�h�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�857�jn  jn  �1099�jo  �1902��214��1092��743�h�h�h�h�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(hohohohohohohohohoh�h�h�h�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�	BGS2HPA96��	BGS2HPAB1��	BGS2HPA70��	BGS2NW1D5��	BGS2NW152��	BGS2HSWE5�j�  j�  j�  �	BGS2NV535��	BGS19PQ66��	TYU420242��	BGS19PYK6�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�2289��1978�j�  �1993�j�  �2171�j�  j�  j�  �2045�j�  �2169�j�  et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(h�h�h�h�h�h�h�h�h�h�h��A�h�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�2289��1978�j�  �2177��1966��2171�j�  j�  j�  �0�j�  �2169�j�  et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�	6/13/2025�j�  j�  �	8/30/2027��	8/30/2029��	10/3/2024�j�  j�  j�  �	10/5/2029��
11/13/2029��	9/19/2024�j�  et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�1475317��1475348��1500406��1552361��1727766��1676248�j�  j�  j�  �1551596��1551796��1552055��1552061�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(h�h�h��3150461��3550566�h�h�h�h�h�h��3650755�h�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�857�j�  j�  �1099�j�  �1902��214��1092��743�j�  j�  �1241�j�  et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�CMLDRV�j  j  �MMLDRV�j  �RPEMLDF��BCEMBTR��MMIFEM��40TREMF�j  j  �HOSTEM�j  et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(h�h�h�h�h�h�h�h�h�h�h��TYU24�h�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�JUN25 MXEA  C @ 2451��JUN25 SPLVP5UP  C @ 276��JUN25 SPLVP5UP  C @ 289��!SWP: OIS 3.478000 30-AUG-2027 SOF��!SWP: OIS 3.349500 30-AUG-2029 SOF��USD P ZAR C @18.38500 EO�j   j   j   �CSWAP: GBP/USD 05-OCT-2029��CSWAP: EUR/USD 13-NOV-2029��US 10YR NOTE SEP 24�j"  et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�OPTION�j-  j-  �SWAP�j.  j-  j-  j-  j-  j.  j.  �FUTURE�j.  et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�	MXEA  OTC��
SPLVP5UP  OTC�j:  �CME�j;  �PUT USD/ZAR�j<  j<  j<  �CREDAG��WELLS��TYU4�j>  et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�EQUITY�jI  jI  �SWAP�jJ  �CUROTC�jK  jK  jK  �CSWAP�jL  �FIN�jL  et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�4633��4638��4637��23058��23059��1790��12684��878��3657��23055��23061��1101��5023�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�MMALM�jm  jm  jm  jm  �PER_PM�jn  jn  jn  �MMFCP�jo  �MMMHP�jo  et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�CAAD�jz  jz  �DUTO�j{  �SHA�j|  j|  j|  j{  �AIDO�j}  j}  et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(�	MXEA00007��	BRTHLDTR0�j�  h�h��USD/ZAR�j�  j�  j�  h�h�h�h�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(h�h�h�h�h�h�h�h�h�h�h��1552055�h�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(h�h�h�h�h�h�h�h�h�h�h��3650755�h�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(h�h�h�h�h�h�h�h�h�h�h�j�  h�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(h�h�h�h�h�h�h�h�h�h�h��XCBT�h�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(h�h�h�h�h�h�h�h�h�h�h��2024-08-26T16:54:37.000Z�h�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(h�h�h�h�h�h�h�h�h�h�h��1600755�h�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(h�h�h�h�h�h�h�h�h�h�h��3650755�h�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubhx)��}�(h{hhK ��h��R�(KK
��h��]�(h�h�h�h�h�h�h�h�h�h�h�h�h�et�bh�h�)��ubhg(�p      q=
ףpY@�G�z�*@�Q���@                EZ�)!
@EZ�)!
@EZ�)!
@EZ�)!
@        �K���@     m\@�༻��@     ��    @�@    @��   �I�A    �C�A    �^u�    �^u�    �^u�    �^u�    �׷A    ̿i�     �U�    `�6�     &�@     @q@     r@m�����@�����
@��(\�b2@��(\�b2@��(\�b2@��(\�b2@     �@      �?              �?     ��    @�@    @��   �I�A    �C�A    ��>�    ��A�     j��    �
�    �׷A    ̿i�      4�    `�6�      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      ��h�f8�����R�(KhlNNNJ����J����K t�bKK
��hot�R�hhK ��h��R�(KKK
��h!�]�(h�h�h�h�h�h�h�h�h�h�h�G@\m     h�h�h�h�h�h�h�h�h�h�h�h�G�U�     h��2024-09-05T14:20:19.246000Z��2024-09-05T22:28:19.246000Z��2024-09-05T16:52:19.246000Z��2024-09-05T11:02:19.246000Z��2024-09-05T23:31:19.246000Z��2024-09-05T14:12:19.246000Z��2024-09-06T00:55:19.246000Z��2024-09-05T23:44:19.246000Z��2024-09-05T17:19:19.246000Z��2024-09-05T19:46:19.246000Z��2024-09-05T17:37:19.246000Z��2024-09-05T09:06:19.246000Z��2024-09-05T19:36:19.246000Z�� �j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �['857']��['857']��['857']��['1099']��['1099']��['1902', '214', '1092', '743']��['1902', '214', '1092', '743']��['1902', '214', '1092', '743']��['1902', '214', '1092', '743']�h�h�h�h�et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h(at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h)at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h,at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h0at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h1at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h9at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h:at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h<at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h=at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h?at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h@at�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hBat�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hCat�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hDat�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hEat�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hFat�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hHat�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hIat�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hKat�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hLat�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hMat�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hNat�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hOat�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hPat�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hSat�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hTat�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hUat�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hVat�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hYat�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h&h+hGhJhWhXet�bh^Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hQhRhZh[h\et�bh^Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hy�mgr_locs��builtins��slice���K KK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KK	K��R�u}�(j�  h�j�  j�  K	K
K��R�u}�(j�  h�j�  j�  K
KK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j  j�  j�  KK
K��R�u}�(j�  j  j�  j�  K
KK��R�u}�(j�  j(  j�  j�  KKK��R�u}�(j�  j7  j�  j�  KKK��R�u}�(j�  jF  j�  j�  KKK��R�u}�(j�  jX  j�  j�  KKK��R�u}�(j�  jg  j�  j�  KKK��R�u}�(j�  jv  j�  j�  KKK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j
  j�  j�  KKK��R�u}�(j�  j  j�  j�  KKK��R�u}�(j�  j&  j�  j�  KK K��R�u}�(j�  j2  j�  j�  K K!K��R�u}�(j�  jB  j�  j�  K!K"K��R�u}�(j�  jP  j�  j�  K#K$K��R�u}�(j�  jf  j�  j�  K$K%K��R�u}�(j�  js  j�  j�  K&K'K��R�u}�(j�  j�  j�  j�  K'K(K��R�u}�(j�  j�  j�  j�  K(K)K��R�u}�(j�  j�  j�  j�  K)K*K��R�u}�(j�  j�  j�  j�  K*K+K��R�u}�(j�  j�  j�  j�  K+K,K��R�u}�(j�  j�  j�  j�  K.K/K��R�u}�(j�  j�  j�  j�  K/K0K��R�u}�(j�  j�  j�  j�  K0K1K��R�u}�(j�  j�  j�  j�  K1K2K��R�u}�(j�  j�  j�  j�  K4K5K��R�u}�(j�  j�  j�  hg(�0                     "       %       2       3       �h�i8�����R�(KhlNNNJ����J����K t�bK��hot�R�u}�(j�  j�  j�  hg(�(       ,       -       5       6       7       �jF  K��hot�R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.