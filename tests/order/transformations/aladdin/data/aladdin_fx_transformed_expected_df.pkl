��Q�      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK^��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��executionDetails.limitPrice��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType��executionDetails.stopPrice��&executionDetails.outgoingOrderAddlInfo�� executionDetails.tradingCapacity��executionDetails.validityPeriod��	_order.id��_orderState.id�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��asset_class_attribute��currency_attribute��notional_currency_2_attribute��option_type_attribute��#instrument_classification_attribute��underlying_index_term_attribute��expiry_date_attribute��option_strike_price_attribute��underlying_symbol_attribute��venue_attribute��isin_attribute��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��__fallback_buyer__��__fallback_seller__��__fallback_counterparty__��__fallback_client__��__fallback_buyer_dec_maker__��__fallback_seller_dec_maker__��__fallback_inv_dec_in_firm__��__fallback_trader__��__fallback_exec_within_firm__��__fallback_executing_entity__��marketIdentifiers��_order.__meta_model__��_orderState.__meta_model__��(orderIdentifiers.initialOrderDesignation��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��!orderIdentifiers.transactionRefNo��.orderIdentifiers.tradingVenueTransactionIdCode�� priceFormingData.initialQuantity��priceFormingData.price��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��sourceIndex��timestamps.orderReceived��timestamps.orderSubmitted�� timestamps.internalOrderReceived��!timestamps.internalOrderSubmitted��timestamps.orderStatusUpdated��&_orderState.timestamps.tradingDateTime��transactionDetails.basketId��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��'_orderState.transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��transactionDetails.recordType��"transactionDetails.tradingCapacity��._orderState.transactionDetails.tradingDateTime�� __instrument_unique_identifier__��__ric__��__asset_class__��__pricing_reference_lxid__��__pricing_reference_redcode__��__newo_in_file__��__instrument_full_name__��__instrument_classification__��__cfi_category__��
__cfi_group__��__file_type_asset_class__��!__instrument_created_through_fb__��__best_ex_asset_class_main__�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(h�N�start�K �stop�K�step�Ku��R�e]�(hhK ��h��R�(KKKK��h!�]�(�1�h��2�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��Aladdin�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��BUYI�h��SELL�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��pandas._libs.missing��NA���h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��NEWO�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��FILL�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��PARF�h�h�h�h�h�h�h�h�h�h�h�h�h�h��Market�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��BRDG QBON FX
assetId: ASSETID��BRDG QBON FX
assetId: ASSETID��assetId: ASSETID��assetId: ASSETID��BRDG QBON FX
assetId: ASSETID��assetId: ASSETID��assetId: ASSETID��assetId: ASSETID��assetId: ASSETID��assetId: ASSETID��assetId: ASSETID��assetId: ASSETID��assetId: ASSETID��assetId: ASSETID��assetId: ASSETID��assetId: ASSETID��BRDG QBON FX
assetId: ASSETID��assetId: ASSETID��assetId: ASSETID��BRDG QBON FX
assetId: ASSETID��assetId: ASSETID��assetId: ASSETID��assetId: ASSETID��assetId: ASSETID��assetId: ASSETID��assetId: ASSETID��assetId: ASSETID��assetId: ASSETID��assetId: ASSETID��assetId: ASSETID��assetId: ASSETID��AOTC�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�]��DAVY�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a]�h�a�FXRQ�j  �FXAL�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �XOFF�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  ]�(}�(�labelId��XXXXUSDSGDFXFWD2022-11-30��path��instrumentDetails.instrument��type��se_elastic_schema.static.market��IdentifierType����OBJECT���R�u}�(j  �XXXXSGDUSDFXFWD2022-11-30�j  j	  j
  j  ue]�(}�(j  �XXXXUSDSGDFXFWD2022-11-30�j  j	  j
  j  u}�(j  �XXXXSGDUSDFXFWD2022-11-30�j  j	  j
  j  ue]�(}�(j  �XXXXUSDAUDFXFWD2022-11-29�j  j	  j
  j  u}�(j  �XXXXAUDUSDFXFWD2022-11-29�j  j	  j
  j  ue]�(}�(j  �XXXXUSDAUDFXFWD2022-11-25�j  j	  j
  j  u}�(j  �XXXXAUDUSDFXFWD2022-11-25�j  j	  j
  j  ue]�}�(j  �XXXXUSDSGDFXSPOT�j  j	  j
  j  ua]�(}�(j  �XXXXEURAUDFXFWD2023-01-30�j  j	  j
  j  u}�(j  �XXXXAUDEURFXFWD2023-01-30�j  j	  j
  j  ue]�(}�(j  �XXXXUSDAUDFXFWD2022-11-29�j  j	  j
  j  u}�(j  �XXXXAUDUSDFXFWD2022-11-29�j  j	  j
  j  ue]�(}�(j  �XXXXEURUSDFXFWD2022-11-29�j  j	  j
  j  u}�(j  �XXXXUSDEURFXFWD2022-11-29�j  j	  j
  j  ue]�(}�(j  �XXXXUSDAUDFXFWD2023-01-30�j  j	  j
  j  u}�(j  �XXXXAUDUSDFXFWD2023-01-30�j  j	  j
  j  ue]�(}�(j  �XXXXUSDAUDFXFWD2023-01-30�j  j	  j
  j  u}�(j  �XXXXAUDUSDFXFWD2023-01-30�j  j	  j
  j  ue]�(}�(j  �XXXXUSDAUDFXFWD2022-11-25�j  j	  j
  j  u}�(j  �XXXXAUDUSDFXFWD2022-11-25�j  j	  j
  j  ue]�(}�(j  �XXXXGBPAUDFXFWD2022-11-29�j  j	  j
  j  u}�(j  �XXXXAUDGBPFXFWD2022-11-29�j  j	  j
  j  ue]�(}�(j  �XXXXEURAUDFXFWD2022-11-29�j  j	  j
  j  u}�(j  �XXXXAUDEURFXFWD2022-11-29�j  j	  j
  j  ue]�(}�(j  �XXXXUSDAUDFXFWD2023-01-30�j  j	  j
  j  u}�(j  �XXXXAUDUSDFXFWD2023-01-30�j  j	  j
  j  ue]�(}�(j  �XXXXEURAUDFXFWD2022-11-29�j  j	  j
  j  u}�(j  �XXXXAUDEURFXFWD2022-11-29�j  j	  j
  j  ue]�(}�(j  �XXXXEURAUDFXFWD2022-11-29�j  j	  j
  j  u}�(j  �XXXXAUDEURFXFWD2022-11-29�j  j	  j
  j  ue]�(}�(j  �XXXXUSDSGDFXFWD2022-11-30�j  j	  j
  j  u}�(j  �XXXXSGDUSDFXFWD2022-11-30�j  j	  j
  j  ue]�(}�(j  �XXXXUSDAUDFXFWD2022-11-29�j  j	  j
  j  u}�(j  �XXXXAUDUSDFXFWD2022-11-29�j  j	  j
  j  ue]�(}�(j  �XXXXUSDAUDFXFWD2022-11-25�j  j	  j
  j  u}�(j  �XXXXAUDUSDFXFWD2022-11-25�j  j	  j
  j  ue]�}�(j  �XXXXUSDSGDFXSPOT�j  j	  j
  j  ua]�(}�(j  �XXXXEURAUDFXFWD2023-01-30�j  j	  j
  j  u}�(j  �XXXXAUDEURFXFWD2023-01-30�j  j	  j
  j  ue]�(}�(j  �XXXXUSDAUDFXFWD2022-11-29�j  j	  j
  j  u}�(j  �XXXXAUDUSDFXFWD2022-11-29�j  j	  j
  j  ue]�(}�(j  �XXXXEURUSDFXFWD2022-11-29�j  j	  j
  j  u}�(j  �XXXXUSDEURFXFWD2022-11-29�j  j	  j
  j  ue]�(}�(j  �XXXXUSDAUDFXFWD2023-01-30�j  j	  j
  j  u}�(j  �XXXXAUDUSDFXFWD2023-01-30�j  j	  j
  j  ue]�(}�(j  �XXXXUSDAUDFXFWD2023-01-30�j  j	  j
  j  u}�(j  �XXXXAUDUSDFXFWD2023-01-30�j  j	  j
  j  ue]�(}�(j  �XXXXUSDAUDFXFWD2022-11-25�j  j	  j
  j  u}�(j  �XXXXAUDUSDFXFWD2022-11-25�j  j	  j
  j  ue]�(}�(j  �XXXXGBPAUDFXFWD2022-11-29�j  j	  j
  j  u}�(j  �XXXXAUDGBPFXFWD2022-11-29�j  j	  j
  j  ue]�(}�(j  �XXXXEURAUDFXFWD2022-11-29�j  j	  j
  j  u}�(j  �XXXXAUDEURFXFWD2022-11-29�j  j	  j
  j  ue]�(}�(j  �XXXXUSDAUDFXFWD2023-01-30�j  j	  j
  j  u}�(j  �XXXXAUDUSDFXFWD2023-01-30�j  j	  j
  j  ue]�(}�(j  �XXXXEURAUDFXFWD2022-11-29�j  j	  j
  j  u}�(j  �XXXXAUDEURFXFWD2022-11-29�j  j	  j
  j  ue]�(}�(j  �XXXXEURAUDFXFWD2022-11-29�j  j	  j
  j  u}�(j  �XXXXAUDEURFXFWD2022-11-29�j  j	  j
  j  ue�
fx forward�j�  j�  j�  �fx spot�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �USD��USD��USD��USD��USD��EUR��USD��EUR��USD��USD��USD��GBP��EUR��USD��EUR��EUR��USD��USD��USD��USD��EUR��USD��EUR��USD��USD��USD��GBP��EUR��USD��EUR��EUR��SGD��SGD��AUD��AUD��SGD��AUD��AUD��USD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��SGD��AUD��AUD��SGD��AUD��AUD��USD��AUD��AUD��AUD��AUD��AUD��AUD��AUD��AUD�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��
2022-11-30��
2022-11-30��
2022-11-29��
2022-11-25��
2022-11-02��
2023-01-30��
2022-11-29��
2022-11-29��
2023-01-30��
2023-01-30��
2022-11-25��
2022-11-29��
2022-11-29��
2023-01-30��
2022-11-29��
2022-11-29��
2022-11-30��
2022-11-29��
2022-11-25��
2022-11-02��
2023-01-30��
2022-11-29��
2022-11-29��
2023-01-30��
2023-01-30��
2022-11-25��
2022-11-29��
2022-11-29��
2023-01-30��
2022-11-29��
2022-11-29�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  ]�(}�(j  �!lei:test_aladdin_executing_entity�j  �buyer�j
  j
  �ARRAY���R�u}�(j  �!lei:test_aladdin_executing_entity�j  �reportDetails.executingEntity�j
  j  u}�(j  �id:74�j  �seller�j
  j
  u}�(j  �id:74�j  �counterparty�j
  j  u}�(j  �id:cso�j  �:tradersAlgosWaiversIndicators.investmentDecisionWithinFirm�j
  j  u}�(j  �id:mxb�j  �1tradersAlgosWaiversIndicators.executionWithinFirm�j
  j  u}�(j  �id:62�j  �clientIdentifiers.client�j
  j
  u}�(j  �id:mxb�j  �trader�j
  j
  ue]�(}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �id:74�j  j  j
  j
  u}�(j  �id:74�j  j  j
  j  u}�(j  �id:cso�j  j  j
  j  u}�(j  �id:mxb�j  j  j
  j  u}�(j  �id:409�j  j  j
  j
  u}�(j  �id:mxb�j  j  j
  j
  ue]�(}�(j  �id:53�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:53�j  j  j
  j  u}�(j  �id:bsam�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j  u}�(j  �id:669�j  j  j
  j
  u}�(j  �id:sm�j  j  j
  j
  ue]�(}�(j  �id:94�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:94�j  j  j
  j  u}�(j  �id:dm�j  j  j
  j  u}�(j  �id:kzc�j  j  j
  j  u}�(j  �id:760�j  j  j
  j
  u}�(j  �id:kzc�j  j  j
  j
  ue]�(}�(j  �id:50�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:50�j  j  j
  j  u}�(j  �id:cso�j  j  j
  j  u}�(j  �id:mxb�j  j  j
  j  u}�(j  �id:62�j  j  j
  j
  u}�(j  �id:mxb�j  j  j
  j
  ue]�(}�(j  �id:55�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:55�j  j  j
  j  u}�(j  �id:bsam�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j  u}�(j  �id:950�j  j  j
  j
  u}�(j  �id:sm�j  j  j
  j
  ue]�(}�(j  �id:53�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:53�j  j  j
  j  u}�(j  �id:bsam�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j  u}�(j  �id:610�j  j  j
  j
  u}�(j  �id:sm�j  j  j
  j
  ue]�(}�(j  �id:59�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:59�j  j  j
  j  u}�(j  �id:bsam�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j  u}�(j  �id:196�j  j  j
  j
  u}�(j  �id:sm�j  j  j
  j
  ue]�(}�(j  �id:85�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:85�j  j  j
  j  u}�(j  �id:bsam�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j  u}�(j  �id:950�j  j  j
  j
  u}�(j  �id:sm�j  j  j
  j
  ue]�(}�(j  �id:85�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:85�j  j  j
  j  u}�(j  �id:bsam�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j  u}�(j  �id:1420�j  j  j
  j
  u}�(j  �id:sm�j  j  j
  j
  ue]�(}�(j  �id:94�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:94�j  j  j
  j  u}�(j  �id:dm�j  j  j
  j  u}�(j  �id:kzc�j  j  j
  j  u}�(j  �id:223�j  j  j
  j
  u}�(j  �id:kzc�j  j  j
  j
  ue]�(}�(j  �id:55�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:55�j  j  j
  j  u}�(j  �id:bsam�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j  u}�(j  �id:313�j  j  j
  j
  u}�(j  �id:sm�j  j  j
  j
  ue]�(}�(j  �id:94�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:94�j  j  j
  j  u}�(j  �id:bsam�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j  u}�(j  �id:610�j  j  j
  j
  u}�(j  �id:sm�j  j  j
  j
  ue]�(}�(j  �id:85�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:85�j  j  j
  j  u}�(j  �id:bsam�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j  u}�(j  �id:313�j  j  j
  j
  u}�(j  �id:sm�j  j  j
  j
  ue]�(}�(j  �id:94�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:94�j  j  j
  j  u}�(j  �id:bsam�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j  u}�(j  �id:313�j  j  j
  j
  u}�(j  �id:sm�j  j  j
  j
  ue]�(}�(j  �id:74�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:74�j  j  j
  j  u}�(j  �id:bsam�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j  u}�(j  �id:1420�j  j  j
  j
  u}�(j  �id:sm�j  j  j
  j
  ue]�(}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �id:74�j  j  j
  j
  u}�(j  �id:74�j  j  j
  j  u}�(j  �id:cso�j  j  j
  j  u}�(j  �id:mxb�j  j  j
  j  u}�(j  �id:mxb�j  j  j
  j
  ue]�(}�(j  �id:53�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:53�j  j  j
  j  u}�(j  �id:bsam�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j
  ue]�(}�(j  �id:94�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:94�j  j  j
  j  u}�(j  �id:dm�j  j  j
  j  u}�(j  �id:kzc�j  j  j
  j  u}�(j  �id:kzc�j  j  j
  j
  ue]�(}�(j  �id:50�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:50�j  j  j
  j  u}�(j  �id:cso�j  j  j
  j  u}�(j  �id:mxb�j  j  j
  j  u}�(j  �id:mxb�j  j  j
  j
  ue]�(}�(j  �id:55�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:55�j  j  j
  j  u}�(j  �id:bsam�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j
  ue]�(}�(j  �id:53�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:53�j  j  j
  j  u}�(j  �id:bsam�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j
  ue]�(}�(j  �id:59�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:59�j  j  j
  j  u}�(j  �id:bsam�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j
  ue]�(}�(j  �id:85�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:85�j  j  j
  j  u}�(j  �id:bsam�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j
  ue]�(}�(j  �id:85�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:85�j  j  j
  j  u}�(j  �id:bsam�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j
  ue]�(}�(j  �id:94�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:94�j  j  j
  j  u}�(j  �id:dm�j  j  j
  j  u}�(j  �id:kzc�j  j  j
  j  u}�(j  �id:kzc�j  j  j
  j
  ue]�(}�(j  �id:55�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:55�j  j  j
  j  u}�(j  �id:bsam�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j
  ue]�(}�(j  �id:94�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:94�j  j  j
  j  u}�(j  �id:bsam�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j
  ue]�(}�(j  �id:85�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:85�j  j  j
  j  u}�(j  �id:bsam�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j
  ue]�(}�(j  �id:94�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:94�j  j  j
  j  u}�(j  �id:bsam�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j
  ue]�(}�(j  �id:74�j  j  j
  j
  u}�(j  �!lei:test_aladdin_executing_entity�j  j
  j
  j  u}�(j  �!lei:test_aladdin_executing_entity�j  j  j
  j
  u}�(j  �id:74�j  j  j
  j  u}�(j  �id:bsam�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j  u}�(j  �id:sm�j  j  j
  j
  ue�!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��id:53��id:94��id:50��id:55��id:53��id:59��id:85��id:85��id:94��id:55��id:94��id:85��id:94��id:74��!lei:test_aladdin_executing_entity��id:53��id:94��id:50��id:55��id:53��id:59��id:85��id:85��id:94��id:55��id:94��id:85��id:94��id:74��id:74��id:74��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��id:74��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��id:62��id:409��id:669��id:760��id:62��id:950��id:610��id:196�e(�id:950��id:1420��id:223��id:313��id:610��id:313��id:313��id:1420�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��test_aladdin_executing_entity��test_aladdin_executing_entity��53��94��50��55��53��59��85��85��94��55��94��85��94��74��test_aladdin_executing_entity��53��94��50��55��53��59��85��85��94��55��94��85��94��74��74��74��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��74��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��62��409��669��760��62��950��610��196��950��1420��223��313��610��313��313��1420�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity�]�(j  j  j  j  j  j  j  j  j  j  e]�(j  j  j!  j#  j%  j'  j)  j+  j-  j/  e]�(j  j  j2  j4  j6  j8  j:  j<  j>  j@  e]�(j  j   jC  jE  jG  jI  jK  jM  jO  jQ  e]�(j#  jT  jV  jX  jZ  j\  j^  j`  jb  e]�(j&  j(  je  jg  ji  jk  jm  jo  jq  js  e]�(j+  j-  jv  jx  jz  j|  j~  j�  j�  j�  e]�(j0  j2  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j5  j7  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j:  j<  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j?  jA  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jD  jF  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jI  jK  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jN  jP  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jS  jU  j�  j   j  j  j  j  j
  j  e]�(jX  jZ  j  j  j  j  j  j  j  j  e]�(j]  j_  j   j"  j$  j&  j(  j*  j,  e]�(jb  jd  j/  j1  j3  j5  j7  j9  j;  e]�(jg  ji  j>  j@  jB  jD  jF  jH  jJ  e]�(jl  jM  jO  jQ  jS  jU  jW  jY  e]�(jo  jq  j\  j^  j`  jb  jd  jf  jh  e]�(jt  jv  jk  jm  jo  jq  js  ju  jw  e]�(jy  j{  jz  j|  j~  j�  j�  j�  j�  e]�(j~  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  e�Order�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �
OrderState�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  J��� J��� J�
� J��� J�߽ J�� J�'� J�(� Jd)� J�� J��� J%�� J�� Jr� Jі� J�� J��� J�
� J��� J�߽ J�� J�'� J�(� Jd)� J�� J��� J%�� J�� Jr� Jі� J�� J��� J��� J�
� J��� J�߽ J�� J�'� J�(� Jd)� J�� J��� J%�� J�� Jr� Jі� J�� J��� J�
� J��� J�߽ J�� J�'� J�(� Jd)� J�� J��� J%�� J�� Jr� Jі� J�� M�%M�
MaM��M�%M��M@dM�RM��Mb�M+wMMQMAdMKQMLQMc�J��ZJ$�YJfskJ�dJ(�qJ{�gJ��sJ,�sJnxJj�|J�fhJ�"vJ�c�J%��J�h�M�%M�
MaM��M�%M��M@dM�RM��Mb�M+wMMQMAdMKQMLQMc�J��ZJ$�YJfskJ�dJ(�qJ{�gJ��sJ,�sJnxJj�|J�fhJ�"vJ�c�J%��J�h�G?�����$�G?�����$�G?����Yr�G?���ϪWG?��Su���G?��9-�+G?��˹;iG?�" 6 mG?��F ���G?��?~�2G?���ϪWG?��Cä/ G?���-/��G?��F ���G?���-/��G?�҆e	oG?�����$�G?����Yr�G?���ϪWG?��Su���G?��9-�+G?��˹;iG?�" 6 mG?��F ���G?��?~�2G?���ϪWG?��Cä/ G?���-/��G?��F ���G?���-/��G?�҆e	oGAc�    G@�j     G@���\GA
H    GA,�@    G@�˰    GA
Xz�G�G@�s3333G@ࠥ�Q�GAK��\(�GA%��    GAH(�G@��9�Q�GA5)I���
GA)*�p��
GA���Q�GAc+:    G@���\GA
H    GA,�@    G@�˰    GA
Xz�G�G@�s3333G@ࠥ�Q�GAK��\(�GA%��    GAH(�G@��9�Q�GA5)I���
GA)*�p��
GA���Q�M�%M�
MaM��M�%M��M@dM�RM��Mb�M+wMMQMAdMKQMLQMc�J��ZJ$�YJfskJ�dJ(�qJ{�gJ��sJ,�sJnxJj�|J�fhJ�"vJ�c�J%��J�h��2022-10-27T09:34:26.853000Z��2022-10-27T09:34:26.853000Z��2022-10-27T06:20:41.993000Z��2022-10-27T06:02:30.926000Z��2022-10-27T09:31:40.176000Z��2022-10-27T06:33:06.450000Z��2022-10-27T06:33:06.396000Z��2022-10-27T06:20:41.966000Z��2022-10-27T06:33:06.453000Z��2022-10-27T06:20:41.956000Z��2022-10-27T06:04:24.600000Z��2022-10-27T06:33:06.373000Z��2022-10-27T06:33:06.450000Z��2022-10-27T06:33:06.396000Z��2022-10-27T06:33:06.396000Z��2022-10-27T06:20:41.956000Z��2022-10-27T10:10:28.383000Z��2022-10-27T06:22:35.586000Z��2022-10-27T06:07:01.030000Z��2022-10-27T10:10:28.383000Z��2022-10-27T06:33:59.450000Z��2022-10-27T06:33:59.450000Z��2022-10-27T06:22:35.586000Z��2022-10-27T06:33:59.450000Z��2022-10-27T06:22:35.580000Z��2022-10-27T06:07:01.033000Z��2022-10-27T06:33:59.450000Z��2022-10-27T06:33:59.450000Z��2022-10-27T06:33:59.450000Z��2022-10-27T06:33:59.450000Z��2022-10-27T06:22:35.580000Z��2022-10-27T10:10:28.383000Z��2022-10-27T10:10:28.383000Z��2022-10-27T06:22:35.586000Z��2022-10-27T06:07:01.030000Z��2022-10-27T10:10:28.383000Z��2022-10-27T06:33:59.450000Z��2022-10-27T06:33:59.450000Z��2022-10-27T06:22:35.586000Z��2022-10-27T06:33:59.450000Z��2022-10-27T06:22:35.580000Z��2022-10-27T06:07:01.033000Z��2022-10-27T06:33:59.450000Z��2022-10-27T06:33:59.450000Z��2022-10-27T06:33:59.450000Z��2022-10-27T06:33:59.450000Z��2022-10-27T06:22:35.580000Z��2022-10-27T10:10:28.383000Z��2022-10-27T06:22:35.586000Z��2022-10-27T06:07:01.030000Z��2022-10-27T10:10:28.383000Z��2022-10-27T06:33:59.450000Z��2022-10-27T06:33:59.450000Z��2022-10-27T06:22:35.586000Z��2022-10-27T06:33:59.450000Z��2022-10-27T06:22:35.580000Z��2022-10-27T06:07:01.033000Z��2022-10-27T06:33:59.450000Z��2022-10-27T06:33:59.450000Z��2022-10-27T06:33:59.450000Z��2022-10-27T06:33:59.450000Z��2022-10-27T06:22:35.580000Z�j�  j�  j�  j�  j�  j   j  j  j  j  j  j  j  j  j	  j
  j  j  j
  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j   j!  j"  j#  j$  j%  j&  j'  j(  j)  j*  j+  j,  j-  j.  j/  j0  j1  j2  j3  j4  j5  j6  j7  j8  �2022-10-27T10:11:10.606000Z��2022-10-27T10:11:10.606000Z��2022-10-27T06:23:53.410000Z��2022-10-27T06:07:18.243000Z��2022-10-27T10:11:24.963000Z��2022-10-27T06:36:18.276000Z��2022-10-27T06:35:29.183000Z��2022-10-27T06:25:30.656000Z��2022-10-27T06:35:39.376000Z��2022-10-27T06:24:58.723000Z��2022-10-27T06:07:18.680000Z��2022-10-27T06:36:30.936000Z��2022-10-27T06:36:02.783000Z��2022-10-27T06:35:39.433000Z��2022-10-27T06:36:02.783000Z��2022-10-27T06:25:14.316000Z��2022-10-27T10:11:08.000000Z��2022-10-27T06:23:51.000000Z��2022-10-27T06:07:16.000000Z��2022-10-27T10:11:23.000000Z��2022-10-27T06:36:16.000000Z��2022-10-27T06:35:26.000000Z��2022-10-27T06:25:28.000000Z��2022-10-27T06:35:37.000000Z��2022-10-27T06:24:56.000000Z��2022-10-27T06:07:16.000000Z��2022-10-27T06:36:29.000000Z��2022-10-27T06:36:01.000000Z��2022-10-27T06:35:37.000000Z��2022-10-27T06:36:01.000000Z��2022-10-27T06:25:12.000000Z�j9  j:  j;  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  jF  jG  jH  jI  jJ  jK  jL  jM  jN  jO  jP  jQ  jR  jS  jT  jU  jV  jW  h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�J��� J��� J�
� J��� J�߽ J�� J�'� J�(� Jd)� J�� J��� J%�� J�� Jr� Jі� J�� J��� J�
� J��� J�߽ J�� J�'� J�(� Jd)� J�� J��� J%�� J�� Jr� Jі� J�� G?�����$�G?�����$�G?����Yr�G?���ϪWG?��Su���G?��9-�+G?��˹;iG?�" 6 mG?��F ���G?��?~�2G?���ϪWG?��Cä/ G?���-/��G?��F ���G?���-/��G?�҆e	oG?�����$�G?����Yr�G?���ϪWG?��Su���G?��9-�+G?��˹;iG?�" 6 mG?��F ���G?��?~�2G?���ϪWG?��Cä/ G?���-/��G?��F ���G?���-/��G?�҆e	oj�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �MONE�jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  GAc�    G@�j     G@���\GA
H    GA,�@    G@�˰    GA
Xz�G�G@�s3333G@ࠥ�Q�GAK��\(�GA%��    GAH(�G@��9�Q�GA5)I���
GA)*�p��
GA���Q�GAc+:    G@���\GA
H    GA,�@    G@�˰    GA
Xz�G�G@�s3333G@ࠥ�Q�GAK��\(�GA%��    GAH(�G@��9�Q�GA5)I���
GA)*�p��
GA���Q�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  jX  �
Allocation�jY  jY  jY  jY  jY  jY  jY  jY  jY  jY  jY  jY  jY  jY  jY  �Market Side�jZ  jZ  jZ  jZ  jZ  jZ  jZ  jZ  jZ  jZ  jZ  jZ  jZ  jZ  h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�e(h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�j9  j:  j;  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  jF  jG  jH  jI  jJ  jK  jL  jM  jN  jO  jP  jQ  jR  jS  jT  jU  jV  jW  j  j  j  j  j$  j'  j,  j1  j6  j;  j@  jE  jJ  jO  jT  jY  j^  jc  jh  jm  jp  ju  jz  j  j�  j�  j�  j�  j�  j�  j�  h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��������������������������������h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��FX�j[  j[  j[  j[  j[  j[  j[  j[  j[  j[  j[  j[  j[  j[  j[  j[  j[  j[  j[  j[  j[  j[  j[  j[  j[  j[  j[  j[  j[  j[  �Currency Derivatives�j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  et�b�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h!�]�(�
C|11924723��
C|11924823��
C|12149192��
C|12249510��
C|12424521��
C|12474376��
C|12574119��
C|12549308��
C|12549508��
C|12575525��
C|12599666��
C|12649541��
C|12649738��
C|12699183��
C|12774129��
C|12803089��
M|29879123��
M|30234092��
M|33379210��
M|33329221��
M|32139176��
M|31239019��
M|30574108��
M|30574208��
M|29829725��
M|31779066��
M|30224241��
M|33584338��
M|33179083��
M|31579029��
M|32329989�et�b�_dtype�j^  �StringDtype���)��ubj`  )��}�(jc  hhK ��h��R�(KK��h!�]�(ji  jj  jk  jl  jm  jn  jo  jp  jq  jr  js  jt  ju  jv  jw  jx  jy  jz  j{  j|  j}  j~  j  j�  j�  j�  j�  j�  j�  j�  j�  et�bj�  j�  )��ub�pandas.core.arrays.integer��IntegerArray���)��}�(�_data��numpy.core.numeric��_frombuffer���(��                                                                                                                                                                                                                                �h�i8�����R�(K�<�NNNJ����J����K t�bK���C�t�R��_mask�j�  (�       �h�b1�����R�(Kh"NNNJ����J����K t�bK��j�  t�R��_cache�}��dtype�j�  �
Int64Dtype���)��}�j�  }�(�numpy_dtype�h�i8�����R�(Kj�  NNNJ����J����K t�b�kind��i�usbsubj`  )��}�(jc  hhK ��h��R�(KK��h!�]�(�id:74��id:74��id:53��id:94��id:50��id:55��id:53��id:59��id:85��id:85��id:94��id:55��id:94��id:85��id:94��id:74��id:74��id:53��id:94��id:50��id:55��id:53��id:59��id:85��id:85��id:94��id:55��id:94��id:85��id:94��id:74�et�bj�  j�  )��ubj`  )��}�(jc  hhK ��h��R�(KK��h!�]�(�id:cso��id:cso��id:bsam��id:dm��id:cso��id:bsam��id:bsam��id:bsam��id:bsam��id:bsam��id:dm��id:bsam��id:bsam��id:bsam��id:bsam��id:bsam��id:cso��id:bsam��id:dm��id:cso��id:bsam��id:bsam��id:bsam��id:bsam��id:bsam��id:dm��id:bsam��id:bsam��id:bsam��id:bsam��id:bsam�et�bj�  j�  )��ubj`  )��}�(jc  hhK ��h��R�(KK��h!�]�(�id:mxb��id:mxb��id:sm��id:kzc��id:mxb��id:sm��id:sm��id:sm��id:sm��id:sm��id:kzc��id:sm��id:sm��id:sm��id:sm��id:sm��id:mxb��id:sm��id:kzc��id:mxb��id:sm��id:sm��id:sm��id:sm��id:sm��id:kzc��id:sm��id:sm��id:sm��id:sm��id:sm�et�bj�  j�  )��ubj`  )��}�(jc  hhK ��h��R�(KK��h!�]�(�id:mxb��id:mxb��id:sm��id:kzc��id:mxb��id:sm��id:sm��id:sm��id:sm��id:sm��id:kzc��id:sm��id:sm��id:sm��id:sm��id:sm��id:mxb��id:sm��id:kzc��id:mxb��id:sm��id:sm��id:sm��id:sm��id:sm��id:kzc��id:sm��id:sm��id:sm��id:sm��id:sm�et�bj�  j�  )��ubj`  )��}�(jc  hhK ��h��R�(KK��h!�]�(�74��74��53��94��50��55��53��59��85��85��94��55��94��85��94��74��74��53��94��50��55��53��59��85��85��94��55��94��85��94��74�et�bj�  j�  )��ubj`  )��}�(jc  hhK ��h��R�(KK��h!�]�(�cso��cso��bsam��dm��cso��bsam��bsam��bsam��bsam��bsam��dm��bsam��bsam��bsam��bsam��bsam��cso��bsam��dm��cso��bsam��bsam��bsam��bsam��bsam��dm��bsam��bsam��bsam��bsam��bsam�et�bj�  j�  )��ubj`  )��}�(jc  hhK ��h��R�(KK��h!�]�(�mxb��mxb��sm��kzc��mxb��sm��sm��sm��sm��sm��kzc��sm��sm��sm��sm��sm��mxb��sm��kzc��mxb��sm��sm��sm��sm��sm��kzc��sm��sm��sm��sm��sm�et�bj�  j�  )��ubj`  )��}�(jc  hhK ��h��R�(KK��h!�]�(�mxb��mxb��sm��kzc��mxb��sm��sm��sm��sm��sm��kzc��sm��sm��sm��sm��sm��mxb��sm��kzc��mxb��sm��sm��sm��sm��sm��kzc��sm��sm��sm��sm��sm�et�bj�  j�  )��ubj`  )��}�(jc  hhK ��h��R�(KK��h!�]�(ji  jj  jk  jl  jm  jn  jo  jp  jq  jr  js  jt  ju  jv  jw  jx  jy  jz  j{  j|  j}  j~  j  j�  j�  j�  j�  j�  j�  j�  j�  et�bj�  j�  )��ubj�  (��          :+cA    :+cA\�����@    H
A    @�,A    ���@�G�zX
A3333s�@�Q����@�(\�KA    ھ%A���(HA�Q�9��@
ף�I)5A
ףp�*)A�Q���A    :+cA\�����@    H
A    @�,A    ���@�G�zX
A3333s�@�Q����@�(\�KA    ھ%A���(HA�Q�9��@
ף�I)5A
ףp�*)A�Q���A      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      ��h�f8�����R�(Kj�  NNNJ����J����K t�bKK��j�  t�R�j�  (��                                                                       	       
                     
                                                                                                                              �j�  KK��j�  t�R�j`  )��}�(jc  hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�BRDG��QBON��CBUSEI��WPIN�j!  �WGCB��WEFC��CMGCI�j%  �WGCD��WCFD��WGCI�j&  j*  j*  j(  j"  j#  j$  j!  j%  j&  j'  j%  j(  j)  j*  j&  j*  j*  j(  et�bj�  j�  )��ubj`  )��}�(jc  hhK ��h��R�(KK��h!�]�(�USD/SGD FWRD��USD/SGD FWRD��USD/AUD FWRD��USD/AUD FWRD��USD/SGD SPOT��EUR/AUD FWRD��USD/AUD FWRD��EUR/USD FWRD��USD/AUD FWRD��USD/AUD FWRD��USD/AUD FWRD��GBP/AUD FWRD��EUR/AUD FWRD��USD/AUD FWRD��EUR/AUD FWRD��EUR/AUD FWRD��USD/SGD FWRD��USD/AUD FWRD��USD/AUD FWRD��USD/SGD SPOT��EUR/AUD FWRD��USD/AUD FWRD��EUR/USD FWRD��USD/AUD FWRD��USD/AUD FWRD��USD/AUD FWRD��GBP/AUD FWRD��EUR/AUD FWRD��USD/AUD FWRD��EUR/AUD FWRD��EUR/AUD FWRD�et�bj�  j�  )��ubj�  (�       �h�b1�����R�(Kh"NNNJ����J����K t�bKK��j�  t�R�e]�(h
h}�(hhhK ��h��R�(KKK��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h4h5h6h7h8h9h:h;h<h=h>h?h@hBhChDhEhGhHhKhMhNhPhQhRhVhWhXhYhZh[h]h^h`hahbhdhehfhghhhihkhlhmhnhohphqhrhshthuhvhwhxhyhzh{h}h�h�et�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hFat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hIat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hJat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hLat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hOat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hSat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hTat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hUat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h\at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h_h~het�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hcat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hjat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h|at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs�j�  (�X                                                                      	       
                                                                                                                                             "       #       &       (       )       +       ,       -       1       2       3       4       5       6       8       9       ;       <       =       ?       @       A       B       C       D       F       G       H       I       J       K       L       M       N       O       P       Q       R       S       T       U       V       X       [       ]       �j�  KK��j�  t�R�u}�(j  ja  j  �builtins��slice���K
KK��R�u}�(j  j�  j  j  KKK��R�u}�(j  j�  j  j  KKK��R�u}�(j  j�  j  j  K!K"K��R�u}�(j  j�  j  j  K$K%K��R�u}�(j  j  j  j  K%K&K��R�u}�(j  j9  j  j  K'K(K��R�u}�(j  ja  j  j  K*K+K��R�u}�(j  j�  j  j  K.K/K��R�u}�(j  j�  j  j  K/K0K��R�u}�(j  j�  j  j  K0K1K��R�u}�(j  j  j  j  K7K8K��R�u}�(j  j  j  j�  (�       :       Y       Z       �j�  K��j�  t�R�u}�(j  j  j  j  K>K?K��R�u}�(j  j  j  j  KEKFK��R�u}�(j  j-  j  j  KWKXK��R�u}�(j  j\  j  j  K\K]K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.