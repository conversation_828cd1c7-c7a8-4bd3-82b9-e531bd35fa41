��YP      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK1��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�order.activatedTimestampUtc��order.avgPrice��order.basketId��order.createdTimestampUtc��
order.isin��order.modifiedTimestampUtc��
order.orderId��order.orderQuantity��order.orderStatus��order.orderType��order.pmInitials��order.priceCcy��	order.ric��order.timeInForce��order.trader��order.tranType��orderdetail.orderId��orderdetail.orderDetailId��orderdetail.origOrderId��orderdetail.portfolioId��__order_type__��transaction.dealingCapacity��transaction.execCptyId��transaction.maturity��transaction.orderId��transaction.placementId��transaction.portfolioId��transaction.portfolioTicker��transaction.ric��transaction.secDesc1��transaction.secGroup��transaction.secType��transaction.tradeNum��transaction.tradeQuantity��placement.limitValue��placement.stopValue��placement.orderId��placement.placementId��fill.dealingCapacity��
fill.exchange��fill.executedPrice��fill.executedQuantity��fill.executedTimestampUtc��fill.fillId��fill.placementId��transaction.assetId��transaction.tradeCoupon��transaction.underlyingSnpCusip��transaction.isin�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(hWN�start�K �stop�K�step�Ku��R�e]�(�pandas.core.arrays.integer��IntegerArray���)��}�(�_data��numpy.core.numeric��_frombuffer���(��                                                                                                                                                                                                                                �h�i8�����R�(K�<�NNNJ����J����K t�bK���C�t�R��_mask�hl(�       �h�b1�����R�(Kh"NNNJ����J����K t�bK��htt�R��_cache�}��dtype�hd�
Int64Dtype���)��}�h�}�(�numpy_dtype�hp�kind��i��is_unsigned_integer���itemsize�Kusbsubhf)��}�(hihl(��       C��     C��     D
�     "��     ߽     ��     {'�     8(�      )�     ���     ^��     ���     ���     �q�     m��     5�     ���     �
�     ���     �߽     ��     �'�     �(�     d)�     ��     ���     %��     ��     r�     і�     ��     �hpK��htt�R�hwhl(�                                      �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(��                                                                                                                                                                                                                                �hpK��htt�R�hwhl(�       �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(��                                                                                                                                                                                                                                �hpK��htt�R�hwhl(�                                      �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(��       ���     ���     �
�     ���     �߽     ��     �'�     �(�     d)�     ��     ���     %��     ��     r�     і�     ��                                                                                                              �hpK��htt�R�hwhl(�                       �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(��       ���     W��     �a�     ��     I��     X�     �ݿ     �|�     �}�     %�     rA�     E�     
�     /��     ���     \�                                                                                                              �hpK��htt�R�hwhl(�                       �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(��       C��     C��     D
�     "��     ߽     ��     {'�     8(�      )�     ���     ^��     ���     ���     �q�     m��     5�                                                                                                              �hpK��htt�R�hwhl(�                       �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(��       >       �      �      �      >       �      b      �       �      �      �       9      b      9      9      �                                                                                                               �hpK��htt�R�hwhl(�                       �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(��       J       J       5       ^       2       7       5       ;       U       U       ^       7       ^       U       ^       J       J       5       ^       2       7       5       ;       U       U       ^       7       ^       U       ^       J       �hpK��htt�R�hwhl(�                                      �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(��       ���     ���     �
�     ���     �߽     ��     �'�     �(�     d)�     ��     ���     %��     ��     r�     і�     ��     ���     �
�     ���     �߽     ��     �'�     �(�     d)�     ��     ���     %��     ��     r�     і�     ��     �hpK��htt�R�hwhl(�                                      �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(��       S��    S��    �U�    �S�    E��    �g�    k��    ��    ���    ]*�    ���    q/�    �t     �E�    ���    Q�    S��    �U�    �S�    E��    �g�    k��    ��    ���    ]*�    ���    q/�    �t     �E�    ���    Q�    �hpK��htt�R�hwhl(�                                      �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(��       >       �      �      �      >       �      b      �       �      �      �       9      b      9      9      �      �      �      �      >       �      b      �       �      �      �       9      b      9      9      �      �hpK��htt�R�hwhl(�                                      �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(��                                                                                                                                                                                                                                �hpK��htt�R�hwhl(�       �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(��       �%      �
      a      ��      �%      ��      @d      �R      ��      b�      +w      MQ      Ad      KQ      LQ      c�      �
      a      ��      �%      ��      @d      �R      ��      b�      +w      MQ      Ad      KQ      LQ      c�      �hpK��htt�R�hwhl(�                                      �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(��                                                                                                                                                                                                                                �hpK��htt�R�hwhl(�       �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(��                                                                                                                                                                                                                                �hpK��htt�R�hwhl(�       �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(��                                                                                                                       ���     �
�     ���     �߽     ��     �'�     �(�     d)�     ��     ���     %��     ��     r�     і�     ��     �hpK��htt�R�hwhl(�                      �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(��                                                                                                                       S��    �U�    �S�    E��    �g�    k��    ��    ���    ]*�    ���    q/�    �t     �E�    ���    Q�    �hpK��htt�R�hwhl(�                      �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(��                                                                                                                       ��Z    $�Y    fsk    �d    (�q    {�g    ��s    ,�s    nx    j�|    �fh    �"v    �c�    %��    �h�    �hpK��htt�R�hwhl(�                      �h{K��htt�R�h�}�h�h�subhf)��}�(hihl(��                                                                                                                       S��    �U�    �S�    E��    �g�    k��    ��    ���    ]*�    ���    q/�    �t     �E�    ���    Q�    �hpK��htt�R�hwhl(�                      �h{K��htt�R�h�}�h�h�sub�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�2022-10-27T10:10:28.383Z�jm  �2022-10-27T06:22:35.586Z��2022-10-27T06:07:01.030Z�jm  �2022-10-27T06:33:59.450Z�jp  jn  jp  �2022-10-27T06:22:35.580Z��2022-10-27T06:07:01.033Z�jp  jp  jp  jp  jq  jm  jn  jo  jm  jp  jp  jn  jp  jq  jr  jp  jp  jp  jp  jq  et�b�_dtype�j^  �StringDtype���)��ubj`  )��}�(jc  hhK ��h��R�(KK��jj  �]�(�BRDG QBON FX�j  �pandas._libs.missing��NA���j�  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j  j�  j�  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bjt  jv  )��ubj`  )��}�(jc  hhK ��h��R�(KK��jj  �]�(�2022-10-27T09:34:26.853Z�j�  �2022-10-27T06:20:41.993Z��2022-10-27T06:02:30.926Z��2022-10-27T09:31:40.176Z��2022-10-27T06:33:06.450Z��2022-10-27T06:33:06.396Z��2022-10-27T06:20:41.966Z��2022-10-27T06:33:06.453Z��2022-10-27T06:20:41.956Z��2022-10-27T06:04:24.600Z��2022-10-27T06:33:06.373Z�j�  j�  j�  j�  �2022-10-27T10:10:28.383Z��2022-10-27T06:22:35.586Z��2022-10-27T06:07:01.030Z�j�  �2022-10-27T06:33:59.450Z�j�  j�  j�  �2022-10-27T06:22:35.580Z��2022-10-27T06:07:01.033Z�j�  j�  j�  j�  j�  et�bjt  jv  )��ubj`  )��}�(jc  hhK ��h��R�(KK��jj  �]�(�2022-10-27T10:11:10.606Z�j�  �2022-10-27T06:23:53.410Z��2022-10-27T06:07:18.243Z��2022-10-27T10:11:24.963Z��2022-10-27T06:36:18.276Z��2022-10-27T06:35:29.183Z��2022-10-27T06:25:30.656Z��2022-10-27T06:35:39.376Z��2022-10-27T06:24:58.723Z��2022-10-27T06:07:18.680Z��2022-10-27T06:36:30.936Z��2022-10-27T06:36:02.783Z��2022-10-27T06:35:39.433Z�j�  �2022-10-27T06:25:14.316Z��2022-10-27T10:11:10.540Z��2022-10-27T06:23:53.376Z��2022-10-27T06:07:18.206Z��2022-10-27T10:11:24.930Z��2022-10-27T06:36:18.246Z��2022-10-27T06:35:29.150Z��2022-10-27T06:25:30.620Z��2022-10-27T06:35:39.340Z��2022-10-27T06:24:58.693Z��2022-10-27T06:07:18.653Z��2022-10-27T06:36:30.906Z��2022-10-27T06:36:02.750Z��2022-10-27T06:35:39.400Z��2022-10-27T06:36:02.746Z��2022-10-27T06:25:14.283Z�et�bjt  jv  )��ubj`  )��}�(jc  hhK ��h��R�(KK��jj  �]�(�F�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bjt  jv  )��ubj`  )��}�(jc  hhK ��h��R�(KK��jj  �]�(�M�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bjt  jv  )��ubj`  )��}�(jc  hhK ��h��R�(KK��jj  �]�(�CSO�j�  �BSAM��DM�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bjt  jv  )��ubj`  )��}�(jc  hhK ��h��R�(KK��jj  �]�(�SGD�j�  �AUD�j�  j�  j�  j�  �USD�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bjt  jv  )��ubj`  )��}�(jc  hhK ��h��R�(KK��jj  �]�(�MXB�j�  �SM��KZC�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bjt  jv  )��ubj`  )��}�(jc  hhK ��h��R�(KK��jj  �]�(�BUY�j  �SELL�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bjt  jv  )��ubj`  )��}�(jc  hhK ��h��R�(KK��jj  �]�(�A�j  �R�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bjt  jv  )��ubj`  )��}�(jc  hhK ��h��R�(KK��jj  �]�(�
11/30/2022�j  �
11/29/2022��
11/25/2022��	11/2/2022��	1/30/2023�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bjt  jv  )��ubj`  )��}�(jc  hhK ��h��R�(KK��jj  �]�(�BRDG��QBON��CBUSEI��WPIN�j(  �WGCB��WEFC��CMGCI�j,  �WGCD��WCFD��WGCI�j-  j1  j1  j/  j)  j*  j+  j(  j,  j-  j.  j,  j/  j0  j1  j-  j1  j1  j/  et�bjt  jv  )��ubj`  )��}�(jc  hhK ��h��R�(KK��jj  �]�(�USD/SGD�j;  �USD/AUD�j<  j;  �EUR/AUD�j<  �EUR/USD�j<  j<  j<  �GBP/AUD�j=  j<  j=  j=  j;  j<  j<  j;  j=  j<  j>  j<  j<  j<  j?  j=  j<  j=  j=  et�bjt  jv  )��ubj`  )��}�(jc  hhK ��h��R�(KK��jj  �]�(�FX�jI  jI  jI  jI  jI  jI  jI  jI  jI  jI  jI  jI  jI  jI  jI  jI  jI  jI  jI  jI  jI  jI  jI  jI  jI  jI  jI  jI  jI  jI  et�bjt  jv  )��ubj`  )��}�(jc  hhK ��h��R�(KK��jj  �]�(�FWRD�jS  jS  jS  �SPOT�jS  jS  jS  jS  jS  jS  jS  jS  jS  jS  jS  jS  jS  jS  jT  jS  jS  jS  jS  jS  jS  jS  jS  jS  jS  jS  et�bjt  jv  )��ubj`  )��}�(jc  hhK ��h��R�(KK��jj  �]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bjt  jv  )��ubj`  )��}�(jc  hhK ��h��R�(KK��jj  �]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �FXRQ��FXAL�jh  jg  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  jh  et�bjt  jv  )��ubj`  )��}�(jc  hhK ��h��R�(KK��jj  �]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �2022-10-27T10:11:08.000Z��2022-10-27T06:23:51.000Z��2022-10-27T06:07:16.000Z��2022-10-27T10:11:23.000Z��2022-10-27T06:36:16.000Z��2022-10-27T06:35:26.000Z��2022-10-27T06:25:28.000Z��2022-10-27T06:35:37.000Z��2022-10-27T06:24:56.000Z�jt  �2022-10-27T06:36:29.000Z��2022-10-27T06:36:01.000Z�jy  j|  �2022-10-27T06:25:12.000Z�et�bjt  jv  )��ubhl(��      �$Ί���?�$Ί���?�rY����?W�ς��?���uS��?+�-9��?i;�ˣ�?m 6 "�?��� F��?2��~?��?W�ς��? /��C��?��/-���?��� F��?��/-���?o	e���?�$Ί���?�rY����?W�ς��?���uS��?+�-9��?i;�ˣ�?m 6 "�?��� F��?2��~?��?W�ς��? /��C��?��/-���?��� F��?��/-���?o	e���?    :+cA    :+cA\������    H
�    @�,�    �����G�zX
�3333s���Q������(\�K�    ھ%����(H��Q�9���
ף�I)5�
ףp�*)��Q����    :+cA\������    H
�    @�,�    �����G�zX
�3333s���Q������(\�K�    ھ%����(H��Q�9���
ף�I)5�
ףp�*)��Q����    �cA     j�@\������    H
�    @�,�    �����G�zX
�3333s���Q������(\�K�    ھ%����(H��Q�9���
ף�I)5�
ףp�*)��Q����     j�@\������    H
�    @�,�    �����G�zX
�3333s���Q������(\�K�    ھ%����(H��Q�9���
ף�I)5�
ףp�*)��Q�����h�f8�����R�(KhqNNNJ����J����K t�bKK��htt�R�hhK ��h��R�(KKK��h!�]�(hthththththththththththththththtj�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  G?�����$�G?����Yr�G?���ϪWG?��Su���G?��9-�+G?��˹;iG?�" 6 mG?��F ���G?��?~�2G?���ϪWG?��Cä/ G?���-/��G?��F ���G?���-/��G?�҆e	oj�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  GAc+:    G����\G�
H    G�,�@    G��˰    G�
Xz�G�G��s3333G�ࠥ�Q�G�K��\(�G�%��    G�H(�G���9�Q�G�5)I���
G�)*�p��
G����Q�ASSETID�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bhf)��}�(hihl(��                                                                                                                                                                                                                                �hpK��htt�R�hwhl(�       �h{K��htt�R�h�}��dtype�h�)��}�h�}�(�numpy_dtype�h�i8�����R�(KhqNNNJ����J����K t�b�kind�h�usbsube]�(h
h}�(hhhK ��h��R�(KK��h!�]�h)at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h+at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h1at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h6at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h7at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h8at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h=at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h>at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h?at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hEat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hGat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hHat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hIat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hJat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hPat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hQat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h%at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h'at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h(at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h-at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h/at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h0at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h4at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h:at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h<at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h@at�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hBat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hCat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hDat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hKat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hLat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hOat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h&h,hFet�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h9hMhNhRhShTet�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hUat�bhWNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hg�mgr_locs��builtins��slice���KKK��R�u}�(j&  h�j'  j*  KKK��R�u}�(j&  h�j'  j*  KK
K��R�u}�(j&  h�j'  j*  K
KK��R�u}�(j&  h�j'  j*  KKK��R�u}�(j&  h�j'  j*  KKK��R�u}�(j&  h�j'  j*  KKK��R�u}�(j&  h�j'  j*  KKK��R�u}�(j&  h�j'  j*  KKK��R�u}�(j&  h�j'  j*  KKK��R�u}�(j&  h�j'  j*  KKK��R�u}�(j&  h�j'  j*  KKK��R�u}�(j&  j  j'  j*  KKK��R�u}�(j&  j  j'  j*  K K!K��R�u}�(j&  j  j'  j*  K"K#K��R�u}�(j&  j'  j'  j*  K#K$K��R�u}�(j&  j2  j'  j*  K$K%K��R�u}�(j&  j=  j'  j*  K%K&K��R�u}�(j&  jH  j'  j*  K+K,K��R�u}�(j&  jS  j'  j*  K,K-K��R�u}�(j&  ja  j'  j*  K KK��R�u}�(j&  jx  j'  j*  KKK��R�u}�(j&  j�  j'  j*  KKK��R�u}�(j&  j�  j'  j*  KKK��R�u}�(j&  j�  j'  j*  KK	K��R�u}�(j&  j�  j'  j*  K	K
K��R�u}�(j&  j�  j'  j*  K
KK��R�u}�(j&  j�  j'  j*  KKK��R�u}�(j&  j�  j'  j*  KKK��R�u}�(j&  j�  j'  j*  KKK��R�u}�(j&  j  j'  j*  KKK��R�u}�(j&  j  j'  j*  KKK��R�u}�(j&  j!  j'  j*  KKK��R�u}�(j&  j4  j'  j*  KKK��R�u}�(j&  jB  j'  j*  KKK��R�u}�(j&  jL  j'  j*  KK K��R�u}�(j&  jW  j'  j*  K&K'K��R�u}�(j&  j`  j'  j*  K'K(K��R�u}�(j&  jk  j'  j*  K*K+K��R�u}�(j&  j�  j'  hl(�                     !       �hpK��htt�R�u}�(j&  j�  j'  hl(�0              (       )       -       .       /       �j�  K��htt�R�u}�(j&  j�  j'  j*  K0K1K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.