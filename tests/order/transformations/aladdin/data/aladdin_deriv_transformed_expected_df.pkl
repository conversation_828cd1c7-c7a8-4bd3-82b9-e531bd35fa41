���3      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK^��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��executionDetails.limitPrice��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType��executionDetails.stopPrice��&executionDetails.outgoingOrderAddlInfo�� executionDetails.tradingCapacity��executionDetails.validityPeriod��	_order.id��_orderState.id�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��asset_class_attribute��currency_attribute��notional_currency_2_attribute��option_type_attribute��#instrument_classification_attribute��underlying_index_term_attribute��expiry_date_attribute��option_strike_price_attribute��underlying_symbol_attribute��venue_attribute��isin_attribute��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��__fallback_buyer__��__fallback_seller__��__fallback_counterparty__��__fallback_client__��__fallback_buyer_dec_maker__��__fallback_seller_dec_maker__��__fallback_inv_dec_in_firm__��__fallback_trader__��__fallback_exec_within_firm__��__fallback_executing_entity__��marketIdentifiers��_order.__meta_model__��_orderState.__meta_model__��(orderIdentifiers.initialOrderDesignation��$orderIdentifiers.internalOrderIdCode��orderIdentifiers.orderIdCode��!orderIdentifiers.transactionRefNo��.orderIdentifiers.tradingVenueTransactionIdCode�� priceFormingData.initialQuantity��priceFormingData.price��+_orderState.priceFormingData.tradedQuantity��reportDetails.transactionRefNo��sourceIndex��timestamps.orderReceived��timestamps.orderSubmitted�� timestamps.internalOrderReceived��!timestamps.internalOrderSubmitted��timestamps.orderStatusUpdated��&_orderState.timestamps.tradingDateTime��transactionDetails.basketId��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��'_orderState.transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��transactionDetails.recordType��"transactionDetails.tradingCapacity��._orderState.transactionDetails.tradingDateTime��__ric__�� __instrument_unique_identifier__��__asset_class__��__pricing_reference_lxid__��__pricing_reference_redcode__��__newo_in_file__��__instrument_full_name__��__instrument_classification__��__cfi_category__��
__cfi_group__��__file_type_asset_class__��!__instrument_created_through_fb__��__best_ex_asset_class_main__�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(h�N�start�K �stop�K�step�Ku��R�e]�(hhK ��h��R�(KKDK��h!�]�(�2��1�h�h�h�h�h�h��Aladdin�h�h�h��
2022-10-27��
2022-10-27��
2022-10-27��
2022-10-27��SELL��BUYI�h�h��pandas._libs.missing��NA���h�h�h��NEWO�h�h�h��FILL�h�h�h��Market�h�h�h�h�h�h�h��assetId: ASSETID��assetId: ASSETID��assetId: ASSETID��assetId: ASSETID��AOTC�h�h�h�]��DAVY�a]�h�a]�h�a]�h�a�XOFF�h�h�h�h�h�h�h�]�(}�(�labelId��XXXXFKLIFF2022-10-31 00:00:00��path��instrumentDetails.instrument��type��se_elastic_schema.static.market��IdentifierType����OBJECT���R�u}�(h��XXXXFKLIFF2022-10 00:00:00�h�h�h�h�ue]�(}�(h��XXXXFKLIFF2022-11-30 00:00:00�h�h�h�h�u}�(h��XXXXFKLIFF2022-11 00:00:00�h�h�h�h�ue]�(}�(h��SGXDB0951002�h�h�h�h�u}�(h��SGXDB0951002USDXXXX�h�h�h�h�u}�(h��XXXXSFCFF2022-10-28 00:00:00�h�h�h�h�u}�(h��XXXXSFCFF2022-10 00:00:00�h�h�h�h�ue]�(}�(h��SGXDB0983179�h�h�h�h�u}�(h��SGXDB0983179USDXXXX�h�h�h�h�u}�(h��XXXXSFCFF2022-11-29 00:00:00�h�h�h�h�u}�(h��XXXXSFCFF2022-11 00:00:00�h�h�h�h�ue�future�h�h�h֌MYR�h׌USD�h�h�h�h�h�G�      G�      G�      G�      h�h�h�h�h�h�h�h��
2022-10-31��
2022-11-30��
2022-10-28��
2022-11-29�h�h�h�h��FKLI��FKLI��SFC��SFC�h�h�h�h�]�(}�(h��id:80�h��buyer�h�h��ARRAY���R�u}�(h��!lei:test_aladdin_executing_entity�h��reportDetails.executingEntity�h�h�u}�(h��!lei:test_aladdin_executing_entity�h��seller�h�h�u}�(h��id:80�h��counterparty�h�h�u}�(h��id:ah�h��:tradersAlgosWaiversIndicators.investmentDecisionWithinFirm�h�h�u}�(h��id:ab�h��1tradersAlgosWaiversIndicators.executionWithinFirm�h�h�u}�(h��id:201�h��clientIdentifiers.client�h�h�u}�(h��id:ab�h��trader�h�h�ue]�(}�(h��!lei:test_aladdin_executing_entity�h�h�h�h�u}�(h��!lei:test_aladdin_executing_entity�h�h�h�h�u}�(h��id:80�h�h�h�h�u}�(h��id:80�h�h�h�h�u}�(h��id:ah�h�h�h�h�u}�(h��id:ab�h�h�h�h�u}�(h��id:201�h�h�h�h�u}�(h��id:ab�h�h�h�h�ue]�(}�(h��id:80�h�h�h�h�u}�(h��!lei:test_aladdin_executing_entity�h�h�h�h�u}�(h��!lei:test_aladdin_executing_entity�h�h�h�h�u}�(h��id:80�h�h�h�h�u}�(h��id:ah�h�h�h�h�u}�(h��id:ab�h�h�h�h�u}�(h��id:201�h�h�h�h�u}�(h��id:ab�h�h�h�h�ue]�(}�(h��!lei:test_aladdin_executing_entity�h�h�h�h�u}�(h��!lei:test_aladdin_executing_entity�h�h�h�h�u}�(h��id:80�h�h�h�h�u}�(h��id:80�h�h�h�h�u}�(h��id:ah�h�h�h�h�u}�(h��id:ab�h�h�h�h�u}�(h��id:201�h�h�h�h�u}�(h��id:ab�h�h�h�h�ue�!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��id:80��!lei:test_aladdin_executing_entity��id:80��!lei:test_aladdin_executing_entity��!lei:test_aladdin_executing_entity��id:80��!lei:test_aladdin_executing_entity��id:80�h�h�h�h�h�h�h�h��id:201��id:201��id:201��id:201��80��test_aladdin_executing_entity��80��test_aladdin_executing_entity��test_aladdin_executing_entity��80��test_aladdin_executing_entity��80��201��201��201��201�h�h�h�h�h�h�h�h��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity��test_aladdin_executing_entity�]�(h�h�h�h�h�h�h�h�h�h�e]�(h�h�h�j   j  j  j  j  j
  j  e]�(h�h�h�h�j  j  j  j  j  j  j  j  e]�(h�h�h�h�j   j"  j$  j&  j(  j*  j,  j.  e�Order�jT  jT  jT  �
OrderState�jU  jU  jU  KKK5K5KKK5K5�2022-10-27T00:26:20.950000Z��2022-10-27T00:26:20.946000Z��2022-10-27T00:26:20.946000Z��2022-10-27T00:26:20.950000Z��2022-10-27T01:05:44.220000Z��2022-10-27T01:05:44.220000Z��2022-10-27T01:05:44.210000Z��2022-10-27T01:05:44.210000Z�jV  jW  jX  jY  jZ  j[  j\  j]  �2022-10-27T01:10:54.406000Z��2022-10-27T01:10:54.346000Z��2022-10-27T01:10:04.306000Z��2022-10-27T01:10:04.306000Z�j^  j_  j`  ja  h�h�h�h�h�h�h�h،MONE�jb  jb  jb  KKK5K5G�      G�      G�      G�      jb  jb  �UNIT�jc  �
Allocation�jd  jd  jd  h�h�h�h�j^  j_  j`  ja  h�h�h�h�h�h�h�h�h�h�h�h�����h�h�h�h�G�      G�      G�      G�      G�      G�      G�      G�      �DERIV�je  je  je  �Derivatives�jf  jf  jf  et�b�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h!�]�(�
C|11999612��
C|11999512��
C|12225279��
C|12225379�et�b�_dtype�jh  �StringDtype���)��ubjj  )��}�(jm  hhK ��h��R�(KK��h!�]�(js  jt  ju  jv  et�bjx  jz  )��ubjj  )��}�(jm  hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(h�h�h�h�et�bjx  jz  )��ubjj  )��}�(jm  hhK ��h��R�(KK��h!�]�(�id:80��id:80��id:80��id:80�et�bjx  jz  )��ubjj  )��}�(jm  hhK ��h��R�(KK��h!�]�(�id:ah��id:ah��id:ah��id:ah�et�bjx  jz  )��ubjj  )��}�(jm  hhK ��h��R�(KK��h!�]�(�id:ab��id:ab��id:ab��id:ab�et�bjx  jz  )��ubjj  )��}�(jm  hhK ��h��R�(KK��h!�]�(�id:ab��id:ab��id:ab��id:ab�et�bjx  jz  )��ubjj  )��}�(jm  hhK ��h��R�(KK��h!�]�(�80��80��80��80�et�bjx  jz  )��ubjj  )��}�(jm  hhK ��h��R�(KK��h!�]�(�ah��ah��ah��ah�et�bjx  jz  )��ubjj  )��}�(jm  hhK ��h��R�(KK��h!�]�(�ab��ab��ab��ab�et�bjx  jz  )��ubjj  )��}�(jm  hhK ��h��R�(KK��h!�]�(�ab��ab��ab��ab�et�bjx  jz  )��ub�pandas.core.arrays.integer��IntegerArray���)��}�(�_data��numpy.core.numeric��_frombuffer���(�        \ŷ     �ķ     �5�     6�     �h�i8�����R�(K�<�NNNJ����J����K t�bK���C�t�R��_mask�j  (�           �h�b1�����R�(Kh"NNNJ����J����K t�bK��j
  t�R��_cache�}��dtype�j�  �
Int64Dtype���)��}�j  }�(�numpy_dtype�h�i8�����R�(Kj  NNNJ����J����K t�b�kind��i�usbsubj�  )��}�(j�  j  (�        \ŷ     �ķ     �5�     6�     �j  K��j
  t�R�j
  j  (�           �j  K��j
  t�R�j  }�j  j  subjj  )��}�(jm  hhK ��h��R�(KK��h!�]�(js  jt  ju  jv  et�bjx  jz  )��ubj�  )��}�(j�  j  (�        V�      W�      T�      U�      �j  K��j
  t�R�j
  j  (�           �j  K��j
  t�R�j  }�j  j  subj�  )��}�(j�  j  (�        V�      W�      T�      U�      �j  K��j
  t�R�j
  j  (�           �j  K��j
  t�R�j  }�j  j  subj  (�@            ��@     Ȗ@     @�@     <�@     ��@     Ȗ@     @�@     <�@�h�f8�����R�(Kj  NNNJ����J����K t�bKK��j
  t�R�j�  )��}�(j�  j  (�        V�      W�      T�      U�      �j  K��j
  t�R�j
  j  (�           �j  K��j
  t�R�j  }�j  j  subj  (�                                     �j!  KK��j
  t�R�jj  )��}�(jm  hhK ��h��R�(KK��j�  �]�(�WCPI�jm  jm  jm  et�bjx  jz  )��ubj�  )��}�(j�  j  (�        \ŷ     �ķ     �5�     6�     �j  K��j
  t�R�j
  j  (�           �j  K��j
  t�R�j  }�j  j  subjj  )��}�(jm  hhK ��h��R�(KK��j�  �]�(�FKLIV2��FKLIX2��SFCV2��SFCX2�et�bjx  jz  )��ubjj  )��}�(jm  hhK ��h��R�(KK��h!�]�(�aladdin-ric-FKLIV2��aladdin-ric-FKLIX2��aladdin-ric-SFCV2��aladdin-ric-SFCX2�et�bjx  jz  )��ubjj  )��}�(jm  hhK ��h��R�(KK��j�  �]�(�KL COMPOSITE INDX OCT 22��KL COMPOSITE INDX NOV 22��FTSE CHINA A50 OCT 22��FTSE CHINA A50 NOV 22�et�bjx  jz  )��ubj  (�       �h�b1�����R�(Kh"NNNJ����J����K t�bKK��j
  t�R�e]�(h
h}�(hhhK ��h��R�(KKD��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h4h5h6h7h8h9h:h;h<h=h>h?h@hBhChDhEhGhHhKhMhNhPhQhRhVhWhXhYh_hahdhehfhghhhihkhnhohphqhrhshthuhxhyhzh{h}h~hh�h�et�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h2at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h3at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hFat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hIat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hJat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hLat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hOat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hSat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hTat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hUat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hZat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h[at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h\at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h]at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h^at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h`hmet�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hbat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hcat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hjat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hlat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hvat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hwat�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h|at�bh�Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h�at�bh�Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs�j  (�                                                                       	       
                                                                                                                                             "       #       &       (       )       +       ,       -       1       2       3       4       :       <       ?       @       A       B       C       D       F       I       J       K       L       M       N       O       P       S       T       U       V       X       Y       Z       [       ]       �j!  KD��j
  t�R�u}�(j�  jk  j�  �builtins��slice���K
KK��R�u}�(j�  j|  j�  j�  KKK��R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  j�  K!K"K��R�u}�(j�  j�  j�  j�  K$K%K��R�u}�(j�  j�  j�  j�  K%K&K��R�u}�(j�  j�  j�  j�  K'K(K��R�u}�(j�  j�  j�  j�  K*K+K��R�u}�(j�  j�  j�  j�  K.K/K��R�u}�(j�  j�  j�  j�  K/K0K��R�u}�(j�  j�  j�  j�  K0K1K��R�u}�(j�  j�  j�  j�  K5K6K��R�u}�(j�  j%  j�  j�  K6K7K��R�u}�(j�  j0  j�  j�  K7K8K��R�u}�(j�  j9  j�  j�  K8K9K��R�u}�(j�  jD  j�  j�  K9K:K��R�u}�(j�  jV  j�  j�  K;KUK
��R�u}�(j�  jW  j�  j�  K=K>K��R�u}�(j�  je  j�  j�  K>K?K��R�u}�(j�  jf  j�  j�  KEKFK��R�u}�(j�  jp  j�  j�  KGKHK��R�u}�(j�  j{  j�  j�  KQKRK��R�u}�(j�  j�  j�  j�  KRKSK��R�u}�(j�  j�  j�  j�  KWKXK��R�u}�(j�  j�  j�  j�  K\K]K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.