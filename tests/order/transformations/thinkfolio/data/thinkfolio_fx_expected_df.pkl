��      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK@��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�_order.buySell��_orderState.buySell��dataSourceName��date��!executionDetails.buySellIndicator��#_order.executionDetails.orderStatus��(_orderState.executionDetails.orderStatus��executionDetails.orderType�� executionDetails.tradingCapacity��	hierarchy��orderIdentifiers.orderIdCode��	_order.id��_orderState.id��_order.__meta_model__��_orderState.__meta_model__��orderIdentifiers.parentOrderId��!orderIdentifiers.transactionRefNo��"_orderState.priceFormingData.price�� transactionDetails.priceCurrency��#transactionDetails.quantityCurrency��+_orderState.priceFormingData.tradedQuantity�� priceFormingData.initialQuantity��reportDetails.transactionRefNo��sourceIndex��	sourceKey��timestamps.orderReceived��timestamps.orderStatusUpdated��timestamps.orderSubmitted��&_orderState.timestamps.tradingDateTime��#transactionDetails.buySellIndicator��$_orderState.transactionDetails.price�� transactionDetails.priceNotation��'_orderState.transactionDetails.quantity��#transactionDetails.quantityNotation��transactionDetails.recordType��"transactionDetails.tradingCapacity��._orderState.transactionDetails.tradingDateTime�� transactionDetails.ultimateVenue��transactionDetails.venue��marketIdentifiers.instrument��isin_attribute��notional_currency_2_attribute��venue_attribute��#instrument_classification_attribute��expiry_date_attribute��swap_near_leg_date_attribute��currency_attribute��asset_class_attribute��marketIdentifiers.parties��,reportDetails.executingEntity.fileIdentifier��buyerFileIdentifier��sellerFileIdentifier��counterpartyFileIdentifier�� buyerDecisionMakerFileIdentifier��!sellerDecisionMakerFileIdentifier��HtradersAlgosWaiversIndicators.investmentDecisionWithinFirmFileIdentifier��?tradersAlgosWaiversIndicators.executionWithinFirmFileIdentifier��clientFileIdentifier��traderFileIdentifier��marketIdentifiers��__is_created_through_fallback__��__newo_in_file__��
__full_name__��__classification__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK���h�i8�����R�(K�<�NNNJ����J����K t�b�B�                                                                  	       
                     
                                                                                                                                             !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       3       4       5       6       7       8       9       :       ;       <       =       >       ?       @       A       B       C       D       E       F       G       H       I       J       K       L       M       N       O       P       Q       R       S       T       U       V       W       X       Y       Z       [       \       ]       ^       _       `       a       b       c       d       e       f       g       h       i       j       k       l       m       n       o       p       q       r       s       t       u       v       w       x       y       z       {       |       }       ~              �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �t�bhf�__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KKK���h!�]�(�1�h�h�h��2�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��
Thinkfolio�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-04��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-04��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��
2023-09-01��BUYI�j  j  j  �SELL�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �NEWO�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �FILL�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  e(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �MARKET�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  �AOTC�j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   �
Standalone�j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  j!  �#2972179_NTMULTI_2023-09-01T00:00:00��#2972184_NTMULTI_2023-09-01T00:00:00��2972185_ABP_2023-09-01T00:00:00�� 2972185_PALM_2023-09-01T00:00:00��2972186_PME_2023-09-01T00:00:00�� 2972187_PALM_2023-09-01T00:00:00�� 2972188_PALM_2023-09-01T00:00:00��'2972191_SICSHORT_AP_2023-09-01T00:00:00��'2972193_SICSHORT_AP_2023-09-01T00:00:00��'2972194_SICSHORT_AP_2023-09-01T00:00:00��#2972204_ABDLPUS_2023-09-01T00:00:00��2972204_ABP_2023-09-01T00:00:00��&2972204_AEMLCDF_AP_2023-09-01T00:00:00��%2972204_CITTRF_AP_2023-09-01T00:00:00�� 2972204_FFAC_2023-09-01T00:00:00��"2972204_MP83LC_2023-09-01T00:00:00��"2972204_ORCHID_2023-09-01T00:00:00�� 2972204_PALM_2023-09-01T00:00:00��2972204_PME_2023-09-01T00:00:00��#2972204_PUBLICA_2023-09-01T00:00:00�� 2972204_SGIL_2023-09-01T00:00:00��%2972204_SICARD_AP_2023-09-01T00:00:00��&2972204_SICLCBF_AP_2023-09-01T00:00:00��%2972204_SICTRF_AP_2023-09-01T00:00:00��!2972204_SZZ11_2023-09-01T00:00:00��!2972204_SZZLC_2023-09-01T00:00:00��%2972204_USLCBF_AP_2023-09-01T00:00:00��$2972204_USTRF_AP_2023-09-01T00:00:00��2972207_PME_2023-09-01T00:00:00��2972207_ABP_2023-09-01T00:00:00��!2972207_SZZLC_2023-09-01T00:00:00��'2972207_SICIGLCF_AP_2023-09-01T00:00:00��2972208_WMB_2023-09-04T00:00:00��2972209_PME_2023-09-01T00:00:00��'2972209_SICIGLCF_AP_2023-09-01T00:00:00��2972209_ABP_2023-09-01T00:00:00��!2972209_SZZLC_2023-09-01T00:00:00��2972210_WMB_2023-09-04T00:00:00�� 2972213_SGIL_2023-09-01T00:00:00�� 2972214_SGIL_2023-09-01T00:00:00�� 2972225_PALM_2023-09-01T00:00:00��&2972226_SIC1CBF_AP_2023-09-01T00:00:00��2972238_PME_2023-09-01T00:00:00�� 2972246_SGIL_2023-09-01T00:00:00��&2972246_AEMLCDF_AP_2023-09-01T00:00:00��2972246_APK_2023-09-01T00:00:00��"2972246_MP83LC_2023-09-01T00:00:00��#2972246_PUBLICA_2023-09-01T00:00:00��&2972246_SICLCBF_AP_2023-09-01T00:00:00��'2972246_SICLCBFU_AP_2023-09-01T00:00:00��!2972246_SZZLC_2023-09-01T00:00:00��%2972246_USLCBF_AP_2023-09-01T00:00:00��'2972246_SICIGLCF_AP_2023-09-01T00:00:00��2972247_WMB_2023-09-01T00:00:00��#2972249_NTMULTI_2023-09-01T00:00:00��#2972250_NTMULTI_2023-09-01T00:00:00��#2972251_NTMULTI_2023-09-01T00:00:00��#2972252_NTMULTI_2023-09-01T00:00:00��#2972253_NTMULTI_2023-09-01T00:00:00�� 2972254_PALM_2023-09-01T00:00:00�� 2972259_PALM_2023-09-01T00:00:00��!2972260_SZZ11_2023-09-01T00:00:00��#2972261_ABDLPUS_2023-09-01T00:00:00��2972262_ABP_2023-09-01T00:00:00��&2972263_AEMLCDF_AP_2023-09-01T00:00:00��%2972264_CITTRF_AP_2023-09-01T00:00:00��"2972265_MP83LC_2023-09-01T00:00:00��#2972266_PENNINE_2023-09-01T00:00:00��#2972267_PUBLICA_2023-09-01T00:00:00�� 2972268_SGIL_2023-09-01T00:00:00��&2972269_SICLCBF_AP_2023-09-01T00:00:00��'2972270_SICLCBFU_AP_2023-09-01T00:00:00��%2972271_SICTRF_AP_2023-09-01T00:00:00��!2972272_SZZLC_2023-09-01T00:00:00��%2972273_USLCBF_AP_2023-09-01T00:00:00��$2972274_USTRF_AP_2023-09-01T00:00:00��2972275_WMB_2023-09-01T00:00:00�� 2972276_PALM_2023-09-01T00:00:00��"2972278_KYBURG_2023-09-01T00:00:00��#2972284_ABDLPUS_2023-09-01T00:00:00��2972285_ABP_2023-09-01T00:00:00��&2972286_AEMLCDF_AP_2023-09-01T00:00:00��%2972287_CITTRF_AP_2023-09-01T00:00:00�� 2972288_FFAC_2023-09-01T00:00:00��"2972289_MP83LC_2023-09-01T00:00:00�� 2972290_PALM_2023-09-01T00:00:00��#2972291_PENNINE_2023-09-01T00:00:00��#2972292_PUBLICA_2023-09-01T00:00:00�� 2972293_SGIL_2023-09-01T00:00:00��'2972294_SICIGTRF_AP_2023-09-01T00:00:00��'2972295_SICLCBFU_AP_2023-09-01T00:00:00��%2972296_SICTRF_AP_2023-09-01T00:00:00��!2972297_SZZLC_2023-09-01T00:00:00��$2972298_USTRF_AP_2023-09-01T00:00:00��"2972299_VIKING_2023-09-01T00:00:00��2972300_WMB_2023-09-01T00:00:00��'2972302_SICIGLCF_AP_2023-09-01T00:00:00��"2972310_MP83LC_2023-09-01T00:00:00�� 2972311_PALM_2023-09-01T00:00:00��#2972312_PENNINE_2023-09-01T00:00:00��'2972313_SICIGLCF_AP_2023-09-01T00:00:00��&2972314_SICLCBF_AP_2023-09-01T00:00:00��%2972315_SICTRF_AP_2023-09-01T00:00:00��!2972316_SZZLC_2023-09-01T00:00:00��$2972317_USTRF_AP_2023-09-01T00:00:00��"2972318_MP83LC_2023-09-01T00:00:00�� 2972319_PALM_2023-09-01T00:00:00��#2972320_PENNINE_2023-09-01T00:00:00��#2972321_PUBLICA_2023-09-01T00:00:00��#2972322_ABDLPUS_2023-09-01T00:00:00��2972323_ABP_2023-09-01T00:00:00��&2972324_AEMLCDF_AP_2023-09-01T00:00:00��%2972325_CITTRF_AP_2023-09-01T00:00:00�� 2972326_SGIL_2023-09-01T00:00:00��'2972327_SICIGLCF_AP_2023-09-01T00:00:00��'2972328_SICIGTRF_AP_2023-09-01T00:00:00��&2972329_SICLCBF_AP_2023-09-01T00:00:00��%2972330_SICTRF_AP_2023-09-01T00:00:00��!2972331_SZZ11_2023-09-01T00:00:00��!2972332_SZZLC_2023-09-01T00:00:00��%2972333_USLCBF_AP_2023-09-01T00:00:00��$2972334_USTRF_AP_2023-09-01T00:00:00�� 2972335_PALM_2023-09-01T00:00:00��"2972336_KYBURG_2023-09-01T00:00:00��"2972337_KYBURG_2023-09-01T00:00:00��#2972384_PENNINE_2023-09-01T00:00:00��'2972385_SICLCBFU_AP_2023-09-01T00:00:00��"2972386_VIKING_2023-09-01T00:00:00��2972387_WMB_2023-09-01T00:00:00��#2972406_NTMULTI_2023-09-01T00:00:00��2972410_APK_2023-09-01T00:00:00�� 2972411_BIA4_2023-09-01T00:00:00��"2972412_MP83LC_2023-09-01T00:00:00��2972413_PME_2023-09-01T00:00:00��#2972414_PUBLICA_2023-09-01T00:00:00��'2972415_SICLCBFU_AP_2023-09-01T00:00:00��2972416_WMB_2023-09-01T00:00:00��#2972418_NTMULTI_2023-09-01T00:00:00��$2972438_SICAV123_2023-09-01T00:00:00��%2972439_SICVFEF06_2023-09-01T00:00:00��#2972442_NTMULTI_2023-09-01T00:00:00�� 2972450_ALPS_2023-09-01T00:00:00��#2972452_EMPIRE2_2023-09-01T00:00:00��#2972458_NTMULTI_2023-09-01T00:00:00��%2972459_SICCDF_AP_2023-09-01T00:00:00��$2972460_USCDF_AP_2023-09-01T00:00:00��2972466_APK_2023-09-01T00:00:00��"2972467_KYBURG_2023-09-01T00:00:00��"2972468_KYBURG_2023-09-01T00:00:00��"2972469_KYBURG_2023-09-01T00:00:00��"2972470_KYBURG_2023-09-01T00:00:00�j"  j#  j$  j%  j&  j'  j(  j)  j*  j+  j,  j-  j.  j/  j0  j1  j2  j3  j4  j5  j6  j7  j8  j9  j:  j;  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  jF  jG  jH  jI  jJ  jK  jL  jM  jN  jO  jP  jQ  jR  jS  jT  jU  jV  jW  jX  jY  jZ  j[  j\  j]  j^  j_  j`  ja  jb  jc  jd  je  jf  jg  jh  ji  jj  jk  jl  jm  jn  jo  jp  jq  jr  js  jt  ju  jv  jw  jx  jy  jz  j{  j|  j}  j~  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j"  j#  j$  j%  j&  j'  j(  j)  j*  j+  j,  j-  j.  j/  j0  j1  j2  j3  j4  j5  j6  j7  j8  j9  j:  j;  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  jF  jG  jH  jI  jJ  jK  jL  jM  jN  jO  jP  jQ  jR  jS  jT  jU  jV  jW  jX  jY  jZ  j[  j\  j]  j^  j_  j`  ja  jb  jc  jd  je  jf  jg  jh  ji  jj  jk  jl  jm  jn  jo  jp  jq  jr  js  jt  ju  jv  jw  jx  jy  jz  j{  j|  j}  j~  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �Order�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �
OrderState�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �Trade Id�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �2972179_07:47:25:633_0��2972184_06:31:55:327_1��2972185_08:26:47:207_2��2972185_08:26:47:207_3��2972186_07:43:12:220_4��2972187_07:43:26:623_5��2972188_08:28:57:180_6��2972191_07:07:24:673_7��2972193_08:08:17:530_8��2972194_07:07:35:043_9��2972204_13:03:02:407_10��2972204_13:03:02:407_11��2972204_13:03:02:407_12��2972204_13:03:02:407_13��2972204_13:03:02:407_14��2972204_13:03:02:407_15��2972204_13:03:02:407_16��2972204_13:03:02:407_17��2972204_13:03:02:407_18��2972204_13:03:02:407_19��2972204_13:03:02:407_20��2972204_13:03:02:407_21��2972204_13:03:02:407_22��2972204_13:03:02:407_23��2972204_13:03:02:407_24��2972204_13:03:02:407_25��2972204_13:03:02:407_26��2972204_13:03:02:407_27��2972207_07:42:07:233_28��2972207_07:42:07:233_29��2972207_07:42:07:233_30��2972207_07:42:07:233_31��2972208_23:04:54:763_32��2972209_07:42:07:233_33��2972209_07:42:07:233_34��2972209_07:42:07:233_35��2972209_07:42:07:233_36��2972210_23:04:54:763_37��2972213_10:42:27:940_38��2972214_10:42:27:940_39��2972225_07:53:17:113_40��2972226_07:52:56:407_41��2972238_09:27:28:407_42��2972246_08:26:47:207_43��2972246_08:26:47:207_44��2972246_08:26:47:207_45��2972246_08:26:47:207_46��2972246_08:26:47:207_47��2972246_08:26:47:207_48��2972246_08:26:47:207_49��2972246_08:26:47:207_50��2972246_08:26:47:207_51��2972246_08:26:47:207_52��2972247_08:27:43:683_53��2972249_08:47:18:057_54��2972250_08:47:25:913_55��2972251_08:47:20:583_56��2972252_08:47:28:540_57��2972253_08:47:23:147_58��2972254_10:18:11:227_59��2972259_08:58:17:173_60��2972260_08:58:17:173_61��2972261_09:00:10:407_62��2972262_08:58:17:173_63��2972263_08:58:17:173_64��2972264_09:00:28:597_65��2972265_09:00:42:137_66��2972266_09:01:07:053_67��2972267_08:58:17:173_68��2972268_08:59:31:027_69��2972269_08:58:17:173_70��2972270_08:58:17:173_71��2972271_08:58:17:173_72��2972272_08:58:17:173_73��2972273_08:58:17:173_74��2972274_08:58:17:173_75��2972275_08:58:45:043_76��2972276_09:09:00:740_77��2972278_09:12:10:323_78��2972284_09:30:19:717_79��2972285_09:17:42:270_80��2972286_09:17:42:270_81��2972287_09:17:42:270_82��2972288_09:17:42:270_83��2972289_09:27:06:077_84��2972290_09:17:42:270_85��2972291_09:26:52:110_86��2972292_09:17:42:270_87��2972293_09:21:15:527_88��2972294_09:17:42:270_89��2972295_09:17:42:270_90��2972296_09:17:42:270_91��2972297_09:17:42:270_92��2972298_09:17:42:270_93��2972299_09:27:57:140_94��2972300_09:21:46:853_95��2972302_10:18:11:227_96��2972310_10:19:53:420_97��2972311_10:19:14:890_98��2972312_10:19:31:750_99��2972313_10:19:14:890_100��2972314_10:19:14:890_101��2972315_10:19:14:890_102��2972316_10:19:14:890_103��2972317_10:19:14:890_104��2972318_10:27:39:503_105��2972319_10:22:55:010_106��2972320_10:27:25:497_107��2972321_10:22:55:010_108��2972322_10:26:34:530_109��2972323_10:22:55:010_110��2972324_10:22:55:010_111��2972325_10:22:55:010_112��2972326_10:27:07:373_113��2972327_10:22:55:010_114��2972328_10:22:55:010_115��2972329_10:22:55:010_116��2972330_10:22:55:010_117��2972331_10:22:55:010_118��2972332_10:22:55:010_119��2972333_10:22:55:010_120��2972334_10:22:55:010_121��2972335_10:36:00:893_122��2972336_10:39:04:827_123��2972337_10:39:51:103_124��2972384_12:57:17:737_125��2972385_12:58:16:673_126��2972386_12:57:42:070_127��2972387_13:00:12:807_128��2972406_13:47:32:977_129��2972410_14:18:04:850_130��2972411_14:22:18:937_131��2972412_14:19:39:177_132��2972413_14:18:26:000_133��2972414_14:18:04:850_134��2972415_14:20:34:070_135��2972416_14:22:56:407_136��2972418_14:39:17:473_137��2972438_15:26:44:870_138��2972439_15:26:39:577_139��2972442_14:54:37:990_140��2972450_15:53:30:760_141��2972452_16:18:26:503_142��2972458_16:16:13:223_143��2972459_16:16:45:213_144��2972460_16:17:03:430_145��2972466_16:39:57:260_146��2972467_16:57:18:363_147��2972468_16:57:58:223_148��2972469_16:58:22:853_149��2972470_16:59:00:343_150��7.26529��15255��15271�jU  �34.9922��35.0061��4.618��1.710763��1.465689��1.08465��4.93146�j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  �15250�j]  j]  j]  �15230.12142857��15270�j_  j_  j_  �15243.359375��15244��15260��7.23378��7.23322��	17.323769�jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  �15261.2��4.5563��18.7517��4.1243��353.292��22.222��4.563405��	18.841342�jm  �	18.832595�jm  jm  �
18.8325076��	18.831076��	18.837925�jm  �	18.829795�jm  jm  jm  jm  jm  jm  �	18.839109��18.7405��18.742��	17.340773��	17.331367�jw  jw  jw  �	17.330407�jw  �	17.333809�jw  �	17.340344�jw  jw  jw  jw  jw  �	17.323383��
17.3415714�jl  �4.137126��4.13628��4.137426�j~  j~  j~  j~  j~  �22.29443��22.29505��22.29599�j�  �22.29305�j�  j�  j�  �22.29894�j�  j�  j�  j�  j�  j�  j�  j�  �22.234��354.07��4.5586��4.93542��4.93678��4.93837��4.93363��
17.0254688��3.6964��3.6972��3.6963��3.695�j�  j�  �3.6951��4.932��0.88415��1.26195��4064��	1.0793679��1.07831��1.0784�j�  j�  �18.814��22.383��356.7��17.0996��18.8332��CNY��IDR��IDR��IDR��THB��THB��MYR��SGD��SGD��USD��BRL��BRL��BRL��BRL��BRL��BRL��BRL��BRL��BRL��BRL��BRL��BRL��BRL��BRL��BRL��BRL��BRL��BRL��IDR��IDR��IDR��IDR��IDR��IDR��IDR��IDR��IDR��IDR��IDR��IDR�j�  j�  �MXN��IDR��IDR��IDR��IDR��IDR��IDR��IDR��IDR��IDR��IDR��IDR��RON��ZAR��PLN��HUF��CZK��RON��ZAR��ZAR��ZAR��ZAR��ZAR��ZAR��ZAR��ZAR��ZAR��ZAR��ZAR��ZAR��ZAR��ZAR��ZAR��ZAR��ZAR��ZAR��ZAR��MXN��MXN��MXN��MXN��MXN��MXN��MXN��MXN��MXN��MXN��MXN��MXN��MXN��MXN��MXN��MXN��MXN��RON��PLN��PLN��PLN��PLN��PLN��PLN��PLN��PLN��CZK��CZK��CZK��CZK��CZK��CZK��CZK��CZK��CZK��CZK��CZK��CZK��CZK��CZK��CZK��CZK��CZK��CZK��HUF��RON��BRL��BRL��BRL��BRL��MXN��PEN��PEN��PEN��PEN��PEN��PEN��PEN��BRL��CHF��USD��COP��USD��USD��USD��USD��USD��ZAR��CZK��HUF��MXN��ZAR��USD��USD��USD��USD��USD��USD��USD��GBP��EUR��EUR��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD��USD�e(�USD��USD��USD��USD��USD��USD��USD��USD��GBP��USD��EUR��EUR��EUR��EUR��EUR��USD��USD��USD��USD��USD��	509728.35��	550838.93��910000��11355000��1374000��6803000�j�  �13700��6500��10515.6��1670000��1800000��2920000��770000��62200000��485000��3755000��20130000��3020000��2000000��2900000��185000��12525000��5540000��7865000��31745000��75000��2855000��
3089842.53��	682401.11��
5357452.05��36935.46��	133235.19��
3085795.58��36887.09��	681507.33��
5350435.08��	133119.48��	483837.12��	483329.82��20408000��51000��585000��534000��1275000��168000��332000��1780000��6186000��17000��6936000��32000��26000�j�  �	201758.84��	472820.27��	402980.12��	211054.83��	323817.93��7142000��5500000��4110000��375000��710000��470000��265000��70000��725000��320000��550000��895000��10000��1000000��7685000��5000��545000��115000��23099512.92��	420474.15��510000��465000��240000��235000��1475000�j  �8745000��655000��500000��190000�j  j  j�  �2095000��860000��215000��35000��7000��195000��11224000��449000��33000��1230000��539000��2144000��296000��134000��9863000��494000��248000��257000��182000��157000��59000��322000��8000�j�  �1758000��432000��2656000��4644000�j$  �	690984.65��
5969946.24��577782.8��	584043.29��2005000��85000��1380000��700000��	560827.74��680000��510000��855000��6870000��3480000��125000��375000��	536613.25��
3591423.17��3432.77��	245028.62��150353��110258.3��89348.97��	441251.51��	320910.52��53.15��71482.82��89991.59��70177.08��	371684.05�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j   j  j  j  j  j  j  j  j  j	  j
  j  j  j
  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j�  j   j!  j"  j#  j$  j%  j&  j'  j(  j)  j*  j+  j,  j-  j.  j/  j0  j1  j2  j3  j4  j5  j6  j�  j7  j8  j9  j:  j$  j;  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  jF  jG  jH  jI  jJ  jK  jL  jM  jN  jO  jP  jQ  jR  jS  jT  jU  jV  jW  jX  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j   j  j  j  j  j  j  j  j  j	  j
  j  j  j
  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j   j!  j"  j#  j$  j%  j&  j'  j(  j)  j*  j+  j,  j-  j.  j/  j0  j1  j2  j3  j4  j5  j6  j7  j8  j9  j:  j;  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  jF  jG  jH  jI  jJ  jK  jL  jM  jN  jO  jP  jQ  jR  et�bhhK ��h��R�(KKK���hs�B�                                                                  	       
                     
                                                                                                                                             !       "       #       $       %       &       '       (       )       *       +       ,       -       .       /       0       1       2       3       4       5       6       7       8       9       :       ;       <       =       >       ?       @       A       B       C       D       E       F       G       H       I       J       K       L       M       N       O       P       Q       R       S       T       U       V       W       X       Y       Z       [       \       ]       ^       _       `       a       b       c       d       e       f       g       h       i       j       k       l       m       n       o       p       q       r       s       t       u       v       w       x       y       z       {       |       }       ~              �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �       �t�bhhK ��h��R�(KK
K���h!�]�(�'dummy/path/TEST_FILLS_1_FX_20230901.pkl�je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  je  �2023-09-01T07:47:25.633000Z��2023-09-01T06:31:55.327000Z��2023-09-01T08:26:47.207000Z��2023-09-01T08:26:47.207000Z��2023-09-01T07:43:12.220000Z��2023-09-01T07:43:26.623000Z��2023-09-01T08:28:57.180000Z��2023-09-01T07:07:24.673000Z��2023-09-01T08:08:17.530000Z��2023-09-01T07:07:35.043000Z��2023-09-01T13:03:02.407000Z��2023-09-01T13:03:02.407000Z��2023-09-01T13:03:02.407000Z��2023-09-01T13:03:02.407000Z��2023-09-01T13:03:02.407000Z��2023-09-01T13:03:02.407000Z��2023-09-01T13:03:02.407000Z��2023-09-01T13:03:02.407000Z��2023-09-01T13:03:02.407000Z��2023-09-01T13:03:02.407000Z��2023-09-01T13:03:02.407000Z��2023-09-01T13:03:02.407000Z��2023-09-01T13:03:02.407000Z��2023-09-01T13:03:02.407000Z��2023-09-01T13:03:02.407000Z��2023-09-01T13:03:02.407000Z��2023-09-01T13:03:02.407000Z��2023-09-01T13:03:02.407000Z��2023-09-01T07:42:07.233000Z��2023-09-01T07:42:07.233000Z��2023-09-01T07:42:07.233000Z��2023-09-01T07:42:07.233000Z��2023-09-04T23:04:54.763000Z��2023-09-01T07:42:07.233000Z��2023-09-01T07:42:07.233000Z��2023-09-01T07:42:07.233000Z��2023-09-01T07:42:07.233000Z��2023-09-04T23:04:54.763000Z��2023-09-01T10:42:27.940000Z��2023-09-01T10:42:27.940000Z��2023-09-01T07:53:17.113000Z��2023-09-01T07:52:56.407000Z��2023-09-01T09:27:28.407000Z��2023-09-01T08:26:47.207000Z��2023-09-01T08:26:47.207000Z��2023-09-01T08:26:47.207000Z��2023-09-01T08:26:47.207000Z��2023-09-01T08:26:47.207000Z��2023-09-01T08:26:47.207000Z��2023-09-01T08:26:47.207000Z��2023-09-01T08:26:47.207000Z��2023-09-01T08:26:47.207000Z��2023-09-01T08:26:47.207000Z��2023-09-01T08:27:43.683000Z��2023-09-01T08:47:18.057000Z��2023-09-01T08:47:25.913000Z��2023-09-01T08:47:20.583000Z��2023-09-01T08:47:28.540000Z��2023-09-01T08:47:23.147000Z��2023-09-01T10:18:11.227000Z��2023-09-01T08:58:17.173000Z��2023-09-01T08:58:17.173000Z��2023-09-01T09:00:10.407000Z��2023-09-01T08:58:17.173000Z��2023-09-01T08:58:17.173000Z��2023-09-01T09:00:28.597000Z��2023-09-01T09:00:42.137000Z��2023-09-01T09:01:07.053000Z��2023-09-01T08:58:17.173000Z��2023-09-01T08:59:31.027000Z��2023-09-01T08:58:17.173000Z��2023-09-01T08:58:17.173000Z��2023-09-01T08:58:17.173000Z��2023-09-01T08:58:17.173000Z��2023-09-01T08:58:17.173000Z��2023-09-01T08:58:17.173000Z��2023-09-01T08:58:45.043000Z��2023-09-01T09:09:00.740000Z��2023-09-01T09:12:10.323000Z��2023-09-01T09:30:19.717000Z��2023-09-01T09:17:42.270000Z��2023-09-01T09:17:42.270000Z��2023-09-01T09:17:42.270000Z��2023-09-01T09:17:42.270000Z��2023-09-01T09:27:06.077000Z��2023-09-01T09:17:42.270000Z��2023-09-01T09:26:52.110000Z��2023-09-01T09:17:42.270000Z��2023-09-01T09:21:15.527000Z��2023-09-01T09:17:42.270000Z��2023-09-01T09:17:42.270000Z��2023-09-01T09:17:42.270000Z��2023-09-01T09:17:42.270000Z��2023-09-01T09:17:42.270000Z��2023-09-01T09:27:57.140000Z��2023-09-01T09:21:46.853000Z��2023-09-01T10:18:11.227000Z��2023-09-01T10:19:53.420000Z��2023-09-01T10:19:14.890000Z��2023-09-01T10:19:31.750000Z��2023-09-01T10:19:14.890000Z��2023-09-01T10:19:14.890000Z��2023-09-01T10:19:14.890000Z��2023-09-01T10:19:14.890000Z��2023-09-01T10:19:14.890000Z��2023-09-01T10:27:39.503000Z��2023-09-01T10:22:55.010000Z��2023-09-01T10:27:25.497000Z��2023-09-01T10:22:55.010000Z��2023-09-01T10:26:34.530000Z��2023-09-01T10:22:55.010000Z��2023-09-01T10:22:55.010000Z��2023-09-01T10:22:55.010000Z��2023-09-01T10:27:07.373000Z��2023-09-01T10:22:55.010000Z��2023-09-01T10:22:55.010000Z��2023-09-01T10:22:55.010000Z��2023-09-01T10:22:55.010000Z��2023-09-01T10:22:55.010000Z��2023-09-01T10:22:55.010000Z��2023-09-01T10:22:55.010000Z��2023-09-01T10:22:55.010000Z��2023-09-01T10:36:00.893000Z��2023-09-01T10:39:04.827000Z��2023-09-01T10:39:51.103000Z��2023-09-01T12:57:17.737000Z��2023-09-01T12:58:16.673000Z��2023-09-01T12:57:42.070000Z��2023-09-01T13:00:12.807000Z��2023-09-01T13:47:32.977000Z��2023-09-01T14:18:04.850000Z��2023-09-01T14:22:18.937000Z��2023-09-01T14:19:39.177000Z��2023-09-01T14:18:26.000000Z��2023-09-01T14:18:04.850000Z��2023-09-01T14:20:34.070000Z��2023-09-01T14:22:56.407000Z��2023-09-01T14:39:17.473000Z��2023-09-01T15:26:44.870000Z��2023-09-01T15:26:39.577000Z��2023-09-01T14:54:37.990000Z��2023-09-01T15:53:30.760000Z��2023-09-01T16:18:26.503000Z��2023-09-01T16:16:13.223000Z��2023-09-01T16:16:45.213000Z��2023-09-01T16:17:03.430000Z��2023-09-01T16:39:57.260000Z��2023-09-01T16:57:18.363000Z��2023-09-01T16:57:58.223000Z��2023-09-01T16:58:22.853000Z��2023-09-01T16:59:00.343000Z�jf  jg  jh  ji  jj  jk  jl  jm  jn  jo  jp  jq  jr  js  jt  ju  jv  jw  jx  jy  jz  j{  j|  j}  j~  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  jf  jg  jh  ji  jj  jk  jl  jm  jn  jo  jp  jq  jr  js  jt  ju  jv  jw  jx  jy  jz  j{  j|  j}  j~  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  jf  jg  jh  ji  jj  jk  jl  jm  jn  jo  jp  jq  jr  js  jt  ju  jv  jw  jx  jy  jz  j{  j|  j}  j~  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  jS  jT  jU  jU  jV  jW  jX  jY  jZ  j[  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j\  j]  j]  j]  j]  j^  j_  j_  j_  j_  j`  ja  jb  jc  jd  je  jU  jU  jU  jU  jU  jU  jU  jU  jU  jU  jf  jg  jh  ji  jj  jk  jl  jm  jm  jn  jm  jm  jo  jp  jq  jm  jr  jm  jm  jm  jm  jm  jm  js  jt  ju  jv  jw  jw  jw  jw  jx  jw  jy  jw  jz  jw  jw  jw  jw  jw  e(j{  j|  jl  j}  j~  j  j~  j~  j~  j~  j~  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �MONE�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j   j  j  j  j  j  j  j  j  j	  j
  j  j  j
  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j�  j   j!  j"  j#  j$  j%  j&  j'  j(  j)  j*  j+  j,  j-  j.  j/  j0  j1  j2  j3  j4  j5  j6  j�  j7  j8  j9  j:  j$  j;  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  jF  jG  jH  jI  jJ  jK  jL  jM  jN  jO  jP  jQ  jR  jS  jT  jU  jV  jW  jX  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �Market Side�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   j   jf  jg  jh  ji  jj  jk  jl  jm  jn  jo  jp  jq  jr  js  jt  ju  jv  jw  jx  jy  jz  j{  j|  j}  j~  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�b�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK���h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�BMTF��XOFF�j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�b�_dtype�j   �StringDtype���)��ubj  )��}�(j  hhK ��h��R�(KK���j  �]�(j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  et�bj  j  )��ubhhK ��h��R�(KKK���h!�]�(]�}�(�labelId��XXXXCNYUSDFXSPOT��path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�ua]�}�(j&  �XXXXIDRUSDFXSPOT�j(  j)  j*  j0  ua]�(}�(j&  �XXXXIDRUSDFXFWD2023-10-12�j(  j)  j*  j0  u}�(j&  �XXXXUSDIDRFXFWD2023-10-12�j(  j)  j*  j0  ue]�(}�(j&  �XXXXIDRUSDFXFWD2023-10-12�j(  j)  j*  j0  u}�(j&  �XXXXUSDIDRFXFWD2023-10-12�j(  j)  j*  j0  ue]�(}�(j&  �XXXXTHBUSDFXFWD2023-09-20�j(  j)  j*  j0  u}�(j&  �XXXXUSDTHBFXFWD2023-09-20�j(  j)  j*  j0  ue]�(}�(j&  �XXXXTHBUSDFXFWD2023-09-20�j(  j)  j*  j0  u}�(j&  �XXXXUSDTHBFXFWD2023-09-20�j(  j)  j*  j0  ue]�(}�(j&  �XXXXMYRUSDFXFWD2023-11-30�j(  j)  j*  j0  u}�(j&  �XXXXUSDMYRFXFWD2023-11-30�j(  j)  j*  j0  ue]�}�(j&  �XXXXSGDGBPFXSPOT�j(  j)  j*  j0  ua]�}�(j&  �XXXXSGDEURFXSPOT�j(  j)  j*  j0  ua]�}�(j&  �XXXXUSDEURFXSPOT�j(  j)  j*  j0  ua]�(}�(j&  �XXXXBRLUSDFXFWD2023-10-03�j(  j)  j*  j0  u}�(j&  �XXXXUSDBRLFXFWD2023-10-03�j(  j)  j*  j0  ue]�(}�(j&  �XXXXBRLUSDFXFWD2023-10-03�j(  j)  j*  j0  u}�(j&  �XXXXUSDBRLFXFWD2023-10-03�j(  j)  j*  j0  ue]�(}�(j&  �XXXXBRLUSDFXFWD2023-10-03�j(  j)  j*  j0  u}�(j&  �XXXXUSDBRLFXFWD2023-10-03�j(  j)  j*  j0  ue]�(}�(j&  �XXXXBRLUSDFXFWD2023-10-03�j(  j)  j*  j0  u}�(j&  �XXXXUSDBRLFXFWD2023-10-03�j(  j)  j*  j0  ue]�(}�(j&  �XXXXBRLUSDFXFWD2023-10-03�j(  j)  j*  j0  u}�(j&  �XXXXUSDBRLFXFWD2023-10-03�j(  j)  j*  j0  ue]�(}�(j&  �XXXXBRLUSDFXFWD2023-10-03�j(  j)  j*  j0  u}�(j&  �XXXXUSDBRLFXFWD2023-10-03�j(  j)  j*  j0  ue]�(}�(j&  �XXXXBRLUSDFXFWD2023-10-03�j(  j)  j*  j0  u}�(j&  �XXXXUSDBRLFXFWD2023-10-03�j(  j)  j*  j0  ue]�(}�(j&  �XXXXBRLUSDFXFWD2023-10-03�j(  j)  j*  j0  u}�(j&  �XXXXUSDBRLFXFWD2023-10-03�j(  j)  j*  j0  ue]�(}�(j&  �XXXXBRLUSDFXFWD2023-10-03�j(  j)  j*  j0  u}�(j&  �XXXXUSDBRLFXFWD2023-10-03�j(  j)  j*  j0  ue]�(}�(j&  �XXXXBRLUSDFXFWD2023-10-03�j(  j)  j*  j0  u}�(j&  �XXXXUSDBRLFXFWD2023-10-03�j(  j)  j*  j0  ue]�(}�(j&  �XXXXBRLUSDFXFWD2023-10-03�j(  j)  j*  j0  u}�(j&  �XXXXUSDBRLFXFWD2023-10-03�j(  j)  j*  j0  ue]�(}�(j&  �XXXXBRLUSDFXFWD2023-10-03�j(  j)  j*  j0  u}�(j&  �XXXXUSDBRLFXFWD2023-10-03�j(  j)  j*  j0  ue]�(}�(j&  �XXXXBRLUSDFXFWD2023-10-03�j(  j)  j*  j0  u}�(j&  �XXXXUSDBRLFXFWD2023-10-03�j(  j)  j*  j0  ue]�(}�(j&  �XXXXBRLUSDFXFWD2023-10-03�j(  j)  j*  j0  u}�(j&  �XXXXUSDBRLFXFWD2023-10-03�j(  j)  j*  j0  ue]�(}�(j&  �XXXXBRLUSDFXFWD2023-10-03�j(  j)  j*  j0  u}�(j&  �XXXXUSDBRLFXFWD2023-10-03�j(  j)  j*  j0  ue]�(}�(j&  �XXXXBRLUSDFXFWD2023-10-03�j(  j)  j*  j0  u}�(j&  �XXXXUSDBRLFXFWD2023-10-03�j(  j)  j*  j0  ue]�(}�(j&  �XXXXBRLUSDFXFWD2023-10-03�j(  j)  j*  j0  u}�(j&  �XXXXUSDBRLFXFWD2023-10-03�j(  j)  j*  j0  ue]�(}�(j&  �XXXXBRLUSDFXFWD2023-10-03�j(  j)  j*  j0  u}�(j&  �XXXXUSDBRLFXFWD2023-10-03�j(  j)  j*  j0  ue]�}�(j&  �XXXXIDRUSDFXSPOT�j(  j)  j*  j0  ua]�}�(j&  �XXXXIDRUSDFXSPOT�j(  j)  j*  j0  ua]�}�(j&  �XXXXIDRUSDFXSPOT�j(  j)  j*  j0  ua]�}�(j&  �XXXXIDRUSDFXSPOT�j(  j)  j*  j0  ua]�}�(j&  �XXXXIDRUSDFXSPOT�j(  j)  j*  j0  ua]�(}�(j&  �XXXXIDRUSDFXFWD2023-10-12�j(  j)  j*  j0  u}�(j&  �XXXXUSDIDRFXFWD2023-10-12�j(  j)  j*  j0  ue]�(}�(j&  �XXXXIDRUSDFXFWD2023-10-12�j(  j)  j*  j0  u}�(j&  �XXXXUSDIDRFXFWD2023-10-12�j(  j)  j*  j0  ue]�(}�(j&  �XXXXIDRUSDFXFWD2023-10-12�j(  j)  j*  j0  u}�(j&  �XXXXUSDIDRFXFWD2023-10-12�j(  j)  j*  j0  ue]�(}�(j&  �XXXXIDRUSDFXFWD2023-10-12�j(  j)  j*  j0  u}�(j&  �XXXXUSDIDRFXFWD2023-10-12�j(  j)  j*  j0  ue]�(}�(j&  �XXXXIDRUSDFXFWD2023-10-12�j(  j)  j*  j0  u}�(j&  �XXXXUSDIDRFXFWD2023-10-12�j(  j)  j*  j0  ue]�}�(j&  �XXXXIDRUSDFXSPOT�j(  j)  j*  j0  ua]�(}�(j&  �XXXXIDRUSDFXFWD2023-10-12�j(  j)  j*  j0  u}�(j&  �XXXXUSDIDRFXFWD2023-10-12�j(  j)  j*  j0  ue]�(}�(j&  �XXXXCNYUSDFXFWD2023-11-20�j(  j)  j*  j0  u}�(j&  �XXXXUSDCNYFXFWD2023-11-20�j(  j)  j*  j0  ue]�(}�(j&  �XXXXCNYUSDFXFWD2023-11-20�j(  j)  j*  j0  u}�(j&  �XXXXUSDCNYFXFWD2023-11-20�j(  j)  j*  j0  ue]�(}�(j&  �XXXXMXNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDMXNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXIDRUSDFXFWD2023-10-12�j(  j)  j*  j0  u}�(j&  �XXXXUSDIDRFXFWD2023-10-12�j(  j)  j*  j0  ue]�(}�(j&  �XXXXIDRUSDFXFWD2023-10-12�j(  j)  j*  j0  u}�(j&  �XXXXUSDIDRFXFWD2023-10-12�j(  j)  j*  j0  ue]�(}�(j&  �XXXXIDRUSDFXFWD2023-10-12�j(  j)  j*  j0  u}�(j&  �XXXXUSDIDRFXFWD2023-10-12�j(  j)  j*  j0  ue]�(}�(j&  �XXXXIDRUSDFXFWD2023-10-12�j(  j)  j*  j0  u}�(j&  �XXXXUSDIDRFXFWD2023-10-12�j(  j)  j*  j0  ue]�(}�(j&  �XXXXIDRUSDFXFWD2023-10-12�j(  j)  j*  j0  u}�(j&  �XXXXUSDIDRFXFWD2023-10-12�j(  j)  j*  j0  ue]�(}�(j&  �XXXXIDRUSDFXFWD2023-10-12�j(  j)  j*  j0  u}�(j&  �XXXXUSDIDRFXFWD2023-10-12�j(  j)  j*  j0  ue]�(}�(j&  �XXXXIDRUSDFXFWD2023-10-12�j(  j)  j*  j0  u}�(j&  �XXXXUSDIDRFXFWD2023-10-12�j(  j)  j*  j0  ue]�(}�(j&  �XXXXIDRUSDFXFWD2023-10-12�j(  j)  j*  j0  u}�(j&  �XXXXUSDIDRFXFWD2023-10-12�j(  j)  j*  j0  ue]�(}�(j&  �XXXXIDRUSDFXFWD2023-10-12�j(  j)  j*  j0  u}�(j&  �XXXXUSDIDRFXFWD2023-10-12�j(  j)  j*  j0  ue]�(}�(j&  �XXXXIDRUSDFXFWD2023-10-12�j(  j)  j*  j0  u}�(j&  �XXXXUSDIDRFXFWD2023-10-12�j(  j)  j*  j0  ue]�(}�(j&  �XXXXIDRUSDFXFWD2023-10-12�j(  j)  j*  j0  u}�(j&  �XXXXUSDIDRFXFWD2023-10-12�j(  j)  j*  j0  ue]�}�(j&  �XXXXRONUSDFXSPOT�j(  j)  j*  j0  ua]�}�(j&  �XXXXZARUSDFXSPOT�j(  j)  j*  j0  ua]�}�(j&  �XXXXPLNUSDFXSPOT�j(  j)  j*  j0  ua]�}�(j&  �XXXXHUFUSDFXSPOT�j(  j)  j*  j0  ua]�}�(j&  �XXXXCZKUSDFXSPOT�j(  j)  j*  j0  ua]�(}�(j&  �XXXXRONUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDRONFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXZARUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDZARFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXZARUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDZARFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXZARUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDZARFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXZARUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDZARFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXZARUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDZARFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXZARUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDZARFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXZARUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDZARFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXZARUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDZARFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXZARUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDZARFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXZARUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDZARFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXZARUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDZARFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXZARUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDZARFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXZARUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDZARFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXZARUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDZARFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXZARUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDZARFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXZARUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDZARFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXZARUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDZARFXFWD2023-10-31�j(  j)  j*  j0  ue�pandas._libs.missing��NA���]�}�(j&  �XXXXZARUSDFXSPOT�j(  j)  j*  j0  ua]�(}�(j&  �XXXXMXNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDMXNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXMXNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDMXNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXMXNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDMXNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXMXNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDMXNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXMXNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDMXNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXMXNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDMXNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXMXNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDMXNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXMXNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDMXNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXMXNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDMXNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXMXNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDMXNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXMXNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDMXNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXMXNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDMXNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXMXNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDMXNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXMXNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDMXNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXMXNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDMXNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXMXNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDMXNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXMXNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDMXNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXRONUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDRONFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXPLNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDPLNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXPLNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDPLNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXPLNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDPLNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXPLNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDPLNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXPLNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDPLNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXPLNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDPLNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXPLNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDPLNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXPLNUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDPLNFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXCZKUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDCZKFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXCZKUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDCZKFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXCZKUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDCZKFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXCZKUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDCZKFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXCZKUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDCZKFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXCZKUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDCZKFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXCZKUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDCZKFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXCZKUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDCZKFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXCZKUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDCZKFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXCZKUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDCZKFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXCZKUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDCZKFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXCZKUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDCZKFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXCZKUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDCZKFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXCZKUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDCZKFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXCZKUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDCZKFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXCZKUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDCZKFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXCZKUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDCZKFXFWD2023-10-31�j(  j)  j*  j0  uej�  ]�}�(j&  �XXXXHUFUSDFXSPOT�j(  j)  j*  j0  ua]�}�(j&  �XXXXRONUSDFXSPOT�j(  j)  j*  j0  ua]�(}�(j&  �XXXXBRLUSDFXFWD2023-10-03�j(  j)  j*  j0  u}�(j&  �XXXXUSDBRLFXFWD2023-10-03�j(  j)  j*  j0  ue]�(}�(j&  �XXXXBRLUSDFXFWD2023-10-03�j(  j)  j*  j0  u}�(j&  �XXXXUSDBRLFXFWD2023-10-03�j(  j)  j*  j0  ue]�(}�(j&  �XXXXBRLUSDFXFWD2023-10-03�j(  j)  j*  j0  u}�(j&  �XXXXUSDBRLFXFWD2023-10-03�j(  j)  j*  j0  ue]�(}�(j&  �XXXXBRLUSDFXFWD2023-10-03�j(  j)  j*  j0  u}�(j&  �XXXXUSDBRLFXFWD2023-10-03�j(  j)  j*  j0  ue]�}�(j&  �XXXXMXNUSDFXSPOT�j(  j)  j*  j0  ua]�(}�(j&  �XXXXPENUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDPENFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXPENUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDPENFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXPENUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDPENFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXPENUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDPENFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXPENUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDPENFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXPENUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDPENFXFWD2023-10-31�j(  j)  j*  j0  ue]�(}�(j&  �XXXXPENUSDFXFWD2023-10-31�j(  j)  j*  j0  u}�(j&  �XXXXUSDPENFXFWD2023-10-31�j(  j)  j*  j0  ue]�}�(j&  �XXXXBRLUSDFXSPOT�j(  j)  j*  j0  uaj�  j�  ]�}�(j&  �XXXXCOPUSDFXSPOT�j(  j)  j*  j0  ua]�}�(j&  �XXXXUSDEURFXSPOT�j(  j)  j*  j0  uaj�  j�  j�  j�  ]�}�(j&  �XXXXZARUSDFXSPOT�j(  j)  j*  j0  ua]�}�(j&  �XXXXCZKUSDFXSPOT�j(  j)  j*  j0  ua]�}�(j&  �XXXXHUFUSDFXSPOT�j(  j)  j*  j0  ua]�}�(j&  �XXXXMXNUSDFXSPOT�j(  j)  j*  j0  ua]�}�(j&  �XXXXZARUSDFXSPOT�j(  j)  j*  j0  uaj�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j3  j4  j5  j6  j7  j8  j9  j:  j;  j<  j=  j>  j?  j@  jA  jB  jC  jD  jE  jF  jG  jH  jI  jJ  jK  jL  jM  jN  jO  jP  jQ  jR  jS  jT  jU  jV  jW  jX  jY  jZ  j[  j\  j]  j^  j_  j`  ja  jb  jc  jd  je  jf  jg  jh  ji  jj  jk  jl  jm  jn  jo  jp  jq  jr  js  jt  ju  jv  jw  jx  jy  jz  j{  j|  j}  j~  j  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �XXXX�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bj  )��}�(j  hhK ��h��R�(KK���j  �]�(�IFXXXX�j�  �JFRXFC�j�  �JFRXFP�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �SFAXXP�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bj  j  )��ubhhK ��h��R�(KKK���h!�]�(j�  j�  �
2023-10-12��
2023-10-12��
2023-09-20��
2023-09-20��
2023-11-30�j�  j�  j�  �
2023-10-03��      �
2023-10-03��
2023-10-03��
2023-10-03��
2023-10-03��
2023-10-03��
2023-10-03��
2023-10-03��
2023-10-03��
2023-10-03��
2023-10-03��
2023-10-03��
2023-10-03��
2023-10-03��
2023-10-03��
2023-10-03��
2023-10-03��
2023-10-03�j�  j�  j�  j�  j�  �
2023-10-12��
2023-10-12��
2023-10-12��
2023-10-12��
2023-10-12�j�  �
2023-10-12��
2023-11-20��
2023-11-20��
2023-10-31��
2023-10-12��
2023-10-12��
2023-10-12��
2023-10-12��
2023-10-12��
2023-10-12��
2023-10-12��
2023-10-12��
2023-10-12��
2023-10-12��
2023-10-12�j�  j�  j�  j�  j�  �
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31�j�  j�  �
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31�j�  j�  j�  �
2023-10-03��
2023-10-03��
2023-10-03��
2023-10-03�j�  �
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31��
2023-10-31�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �2023-09-01T00:00:00�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  jK  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  jK  jK  j�  j�  jK  jK  jK  jK  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j   j  j  j  j  j  j  j  j  j	  j
  j  j  j
  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j  j   j!  j"  j#  j$  j%  j&  j'  j(  j)  j*  j+  j,  j-  j.  j/  j0  j1  j2  j�  j�  �
fx forward�jL  jL  jL  jL  j�  j�  j�  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  j�  j�  j�  j�  j�  jL  jL  jL  jL  jL  j�  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  j�  j�  j�  j�  j�  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  �fx swap�j�  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jL  jM  j�  j�  jL  jL  jL  jL  j�  jL  jL  jL  jL  jL  jL  jL  j�  jM  jM  j�  j�  jM  jM  jM  jM  j�  j�  j�  j�  j�  ]�(}�(j&  �lei:test_executing_entity�j(  �buyer�j*  j-  �ARRAY���R�u}�(j&  �lei:test_executing_entity�j(  �reportDetails.executingEntity�j*  j0  u}�(j&  �	id:stc-gb�j(  �seller�j*  jT  u}�(j&  �	id:stc-gb�j(  �counterparty�j*  j0  u}�(j&  �
id:carvera�j(  �:tradersAlgosWaiversIndicators.investmentDecisionWithinFirm�j*  j0  u}�(j&  �
id:carvera�j(  �1tradersAlgosWaiversIndicators.executionWithinFirm�j*  j0  u}�(j&  �
id:ntmulti�j(  �clientIdentifiers.client�j*  jT  u}�(j&  �
id:carvera�j(  �trader�j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:hsb-gb�j(  jZ  j*  jT  u}�(j&  �	id:hsb-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:ntmulti�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:cgm-gb�j(  jZ  j*  jT  u}�(j&  �	id:cgm-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:abp�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:cgm-gb�j(  jZ  j*  jT  u}�(j&  �	id:cgm-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:palm�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �	id:hsb-gb�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �	id:hsb-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:pme�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �
id:db-degb�j(  jZ  j*  jT  u}�(j&  �
id:db-degb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:palm�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �
id:db-degb�j(  jZ  j*  jT  u}�(j&  �
id:db-degb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:palm�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �	id:hsb-gb�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �	id:hsb-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:sicshort_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �	id:hsb-gb�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �	id:hsb-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:sicshort_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:hsb-gb�j(  jZ  j*  jT  u}�(j&  �	id:hsb-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:sicshort_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:abdlpus�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:abp�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:aemlcdf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:cittrf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:ffac�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �	id:mp83lc�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �	id:orchid�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:palm�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:pme�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:publica�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:sgil�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:sicard_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:siclcbf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:sictrf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:szz11�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:szzlc�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:uslcbf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:ustrf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �
id:db-degb�j(  jZ  j*  jT  u}�(j&  �
id:db-degb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:pme�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �
id:db-degb�j(  jZ  j*  jT  u}�(j&  �
id:db-degb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:abp�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �
id:db-degb�j(  jZ  j*  jT  u}�(j&  �
id:db-degb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:szzlc�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �
id:db-degb�j(  jZ  j*  jT  u}�(j&  �
id:db-degb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:siciglcf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �
id:bnp-fft�j(  jZ  j*  jT  u}�(j&  �
id:bnp-fft�j(  j]  j*  j0  u}�(j&  �id:aiml�j(  j`  j*  j0  u}�(j&  �id:aiml�j(  jc  j*  j0  u}�(j&  �id:wmb�j(  jf  j*  jT  u}�(j&  �id:aiml�j(  ji  j*  jT  ue]�(}�(j&  �
id:db-degb�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �
id:db-degb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:pme�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �
id:db-degb�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �
id:db-degb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:siciglcf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �
id:db-degb�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �
id:db-degb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:abp�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �
id:db-degb�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �
id:db-degb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:szzlc�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �
id:bnp-fft�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �
id:bnp-fft�j(  j]  j*  j0  u}�(j&  �id:aiml�j(  j`  j*  j0  u}�(j&  �id:aiml�j(  jc  j*  j0  u}�(j&  �id:wmb�j(  jf  j*  jT  u}�(j&  �id:aiml�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:hsb-gb�j(  jZ  j*  jT  u}�(j&  �	id:hsb-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:sgil�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �	id:hsb-gb�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �	id:hsb-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:sgil�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:palm�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:sic1cbf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �	id:hsb-gb�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �	id:hsb-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:pme�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:cit-us�j(  jZ  j*  jT  u}�(j&  �	id:cit-us�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:sgil�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:cit-us�j(  jZ  j*  jT  u}�(j&  �	id:cit-us�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:aemlcdf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:cit-us�j(  jZ  j*  jT  u}�(j&  �	id:cit-us�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:apk�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:cit-us�j(  jZ  j*  jT  u}�(j&  �	id:cit-us�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �	id:mp83lc�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:cit-us�j(  jZ  j*  jT  u}�(j&  �	id:cit-us�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:publica�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:cit-us�j(  jZ  j*  jT  u}�(j&  �	id:cit-us�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:siclcbf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:cit-us�j(  jZ  j*  jT  u}�(j&  �	id:cit-us�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:siclcbfu_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:cit-us�j(  jZ  j*  jT  u}�(j&  �	id:cit-us�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:szzlc�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:cit-us�j(  jZ  j*  jT  u}�(j&  �	id:cit-us�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:uslcbf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:cit-us�j(  jZ  j*  jT  u}�(j&  �	id:cit-us�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:siciglcf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:bnp-fr�j(  jZ  j*  jT  u}�(j&  �	id:bnp-fr�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:wmb�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:ntmulti�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:ntmulti�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:ntmulti�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:bnp-fr�j(  jZ  j*  jT  u}�(j&  �	id:bnp-fr�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:ntmulti�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:ntmulti�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �	id:ubs-ch�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �	id:ubs-ch�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:palm�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �	id:bar-gb�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �	id:bar-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:palm�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �	id:bar-gb�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �	id:bar-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:szz11�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:abdlpus�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:bar-gb�j(  jZ  j*  jT  u}�(j&  �	id:bar-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:abp�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:bar-gb�j(  jZ  j*  jT  u}�(j&  �	id:bar-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:aemlcdf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:sst-us�j(  jZ  j*  jT  u}�(j&  �	id:sst-us�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:cittrf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �	id:mp83lc�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:bnp-fr�j(  jZ  j*  jT  u}�(j&  �	id:bnp-fr�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:pennine�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:bar-gb�j(  jZ  j*  jT  u}�(j&  �	id:bar-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:publica�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:sgil�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:bar-gb�j(  jZ  j*  jT  u}�(j&  �	id:bar-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:siclcbf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:bar-gb�j(  jZ  j*  jT  u}�(j&  �	id:bar-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:siclcbfu_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:bar-gb�j(  jZ  j*  jT  u}�(j&  �	id:bar-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:sictrf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:bar-gb�j(  jZ  j*  jT  u}�(j&  �	id:bar-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:szzlc�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:bar-gb�j(  jZ  j*  jT  u}�(j&  �	id:bar-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:uslcbf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:bar-gb�j(  jZ  j*  jT  u}�(j&  �	id:bar-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:ustrf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:bnp-fr�j(  jZ  j*  jT  u}�(j&  �	id:bnp-fr�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:wmb�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:bnp-fr�j(  jZ  j*  jT  u}�(j&  �	id:bnp-fr�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:palm�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:jpm-usgb�j(  jZ  j*  jT  u}�(j&  �id:jpm-usgb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �	id:kyburg�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:abdlpus�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:abp�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:aemlcdf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:cittrf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:ffac�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �	id:mp83lc�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:palm�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:pennine�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:publica�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:cit-us�j(  jZ  j*  jT  u}�(j&  �	id:cit-us�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:sgil�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:sicigtrf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:siclcbfu_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:sictrf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:szzlc�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:ustrf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �	id:viking�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:db-de�j(  jZ  j*  jT  u}�(j&  �id:db-de�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:wmb�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:ubs-ch�j(  jZ  j*  jT  u}�(j&  �	id:ubs-ch�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:siciglcf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �id:ms-gb�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �	id:mp83lc�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �id:ml-gb�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �id:ml-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:palm�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:bnp-fr�j(  jZ  j*  jT  u}�(j&  �	id:bnp-fr�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:pennine�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ml-gb�j(  jZ  j*  jT  u}�(j&  �id:ml-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:siciglcf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ml-gb�j(  jZ  j*  jT  u}�(j&  �id:ml-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:siclcbf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ml-gb�j(  jZ  j*  jT  u}�(j&  �id:ml-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:sictrf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ml-gb�j(  jZ  j*  jT  u}�(j&  �id:ml-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:szzlc�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ml-gb�j(  jZ  j*  jT  u}�(j&  �id:ml-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:ustrf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �	id:cit-us�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �	id:cit-us�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �	id:mp83lc�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �id:ms-gb�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:palm�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �	id:bnp-fr�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �	id:bnp-fr�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:pennine�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �id:ms-gb�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:publica�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:abdlpus�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:abp�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:aemlcdf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:cittrf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ml-gb�j(  jZ  j*  jT  u}�(j&  �id:ml-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:sgil�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:siciglcf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:sicigtrf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:siclcbf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:sictrf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:szz11�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:szzlc�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:uslcbf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:ms-gb�j(  jZ  j*  jT  u}�(j&  �id:ms-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:ustrf_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:bar-gb�j(  jZ  j*  jT  u}�(j&  �	id:bar-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:palm�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:hsb-gb�j(  jZ  j*  jT  u}�(j&  �	id:hsb-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �	id:kyburg�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:hsb-gb�j(  jZ  j*  jT  u}�(j&  �	id:hsb-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �	id:kyburg�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:bar-gb�j(  jZ  j*  jT  u}�(j&  �	id:bar-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:pennine�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:bnp-fr�j(  jZ  j*  jT  u}�(j&  �	id:bnp-fr�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:siclcbfu_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:jpm-usgb�j(  jZ  j*  jT  u}�(j&  �id:jpm-usgb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �	id:viking�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:jpm-de�j(  jZ  j*  jT  u}�(j&  �	id:jpm-de�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:wmb�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �id:jpm-usgb�j(  jZ  j*  jT  u}�(j&  �id:jpm-usgb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:ntmulti�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �	id:stc-gb�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:apk�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �	id:boa-fr�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �	id:boa-fr�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:bia4�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �	id:stc-gb�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �	id:mp83lc�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �	id:stc-gb�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:pme�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �	id:stc-gb�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:publica�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �id:ml-gb�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �id:ml-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:siclcbfu_ap�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �id:db-de�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �id:db-de�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:wmb�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:jpm-br�j(  jZ  j*  jT  u}�(j&  �	id:jpm-br�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:ntmulti�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �	id:hsb-gb�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �	id:hsb-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:sicav123�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:hsb-gb�j(  jZ  j*  jT  u}�(j&  �	id:hsb-gb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:sicvfef06�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:jpm-co�j(  jZ  j*  jT  u}�(j&  �	id:jpm-co�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �
id:ntmulti�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:ubs-ch�j(  jZ  j*  jT  u}�(j&  �	id:ubs-ch�j(  j]  j*  j0  u}�(j&  �
id:salterb�j(  j`  j*  j0  u}�(j&  �
id:salterb�j(  jc  j*  j0  u}�(j&  �id:alps�j(  jf  j*  jT  u}�(j&  �
id:salterb�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:stc-gb�j(  jZ  j*  jT  u}�(j&  �	id:stc-gb�j(  j]  j*  j0  u}�(j&  �
id:salterb�j(  j`  j*  j0  u}�(j&  �
id:salterb�j(  jc  j*  j0  u}�(j&  �
id:empire2�j(  jf  j*  jT  u}�(j&  �
id:salterb�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:bar-gb�j(  jZ  j*  jT  u}�(j&  �	id:bar-gb�j(  j]  j*  j0  u}�(j&  �
id:salterb�j(  j`  j*  j0  u}�(j&  �
id:salterb�j(  jc  j*  j0  u}�(j&  �
id:ntmulti�j(  jf  j*  jT  u}�(j&  �
id:salterb�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:bar-gb�j(  jZ  j*  jT  u}�(j&  �	id:bar-gb�j(  j]  j*  j0  u}�(j&  �
id:salterb�j(  j`  j*  j0  u}�(j&  �
id:salterb�j(  jc  j*  j0  u}�(j&  �id:siccdf_ap�j(  jf  j*  jT  u}�(j&  �
id:salterb�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �	id:bar-gb�j(  jZ  j*  jT  u}�(j&  �	id:bar-gb�j(  j]  j*  j0  u}�(j&  �
id:salterb�j(  j`  j*  j0  u}�(j&  �
id:salterb�j(  jc  j*  j0  u}�(j&  �id:uscdf_ap�j(  jf  j*  jT  u}�(j&  �
id:salterb�j(  ji  j*  jT  ue]�(}�(j&  �lei:test_executing_entity�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �
id:db-degb�j(  jZ  j*  jT  u}�(j&  �
id:db-degb�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �id:apk�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �	id:cit-us�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �	id:cit-us�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �	id:kyburg�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �	id:cit-us�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �	id:cit-us�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �	id:kyburg�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �	id:cit-us�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �	id:cit-us�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �	id:kyburg�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue]�(}�(j&  �	id:cit-us�j(  jQ  j*  jT  u}�(j&  �lei:test_executing_entity�j(  jW  j*  j0  u}�(j&  �lei:test_executing_entity�j(  jZ  j*  jT  u}�(j&  �	id:cit-us�j(  j]  j*  j0  u}�(j&  �
id:carvera�j(  j`  j*  j0  u}�(j&  �
id:carvera�j(  jc  j*  j0  u}�(j&  �	id:kyburg�j(  jf  j*  jT  u}�(j&  �
id:carvera�j(  ji  j*  jT  ue�lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��	id:hsb-gb��lei:test_executing_entity��lei:test_executing_entity��	id:hsb-gb��	id:hsb-gb��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��
id:db-degb��
id:db-degb��
id:db-degb��
id:db-degb��
id:bnp-fft��lei:test_executing_entity��	id:hsb-gb��lei:test_executing_entity��lei:test_executing_entity��	id:hsb-gb��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��	id:ubs-ch��	id:bar-gb��	id:bar-gb��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��
o      �lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity�e(�lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��id:ms-gb��id:ml-gb��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��	id:cit-us��id:ms-gb��	id:bnp-fr��id:ms-gb��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��	id:stc-gb��	id:boa-fr��	id:stc-gb��	id:stc-gb��	id:stc-gb��id:ml-gb��id:db-de��lei:test_executing_entity��	id:hsb-gb��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��	id:cit-us��	id:cit-us��	id:cit-us��	id:cit-us��	id:stc-gb��	id:hsb-gb��	id:cgm-gb��	id:cgm-gb��lei:test_executing_entity��
id:db-degb��
id:db-degb��lei:test_executing_entity��lei:test_executing_entity��	id:hsb-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��
id:db-degb��
id:db-degb��
id:db-degb��
id:db-degb��
id:bnp-fft��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��	id:hsb-gb��lei:test_executing_entity��	id:stc-gb��	id:stc-gb��lei:test_executing_entity��	id:cit-us��	id:cit-us��	id:cit-us��	id:cit-us��	id:cit-us��	id:cit-us��	id:cit-us��	id:cit-us��	id:cit-us��	id:cit-us��	id:bnp-fr��	id:stc-gb��id:ms-gb��	id:stc-gb��	id:bnp-fr��id:ms-gb��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��	id:stc-gb��	id:bar-gb��	id:bar-gb��	id:sst-us��	id:stc-gb��	id:bnp-fr��	id:bar-gb��	id:stc-gb��	id:bar-gb��	id:bar-gb��	id:bar-gb��	id:bar-gb��	id:bar-gb��	id:bar-gb��	id:bnp-fr��	id:bnp-fr��id:jpm-usgb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��	id:cit-us��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:db-de��	id:ubs-ch��lei:test_executing_entity��lei:test_executing_entity��	id:bnp-fr��id:ml-gb��id:ml-gb��id:ml-gb��id:ml-gb��id:ml-gb��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ml-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��	id:bar-gb��	id:hsb-gb��	id:hsb-gb��	id:bar-gb��	id:bnp-fr��id:jpm-usgb��	id:jpm-de��id:jpm-usgb��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��	id:jpm-br��lei:test_executing_entity��	id:hsb-gb��	id:jpm-co��	id:ubs-ch��	id:stc-gb��	id:bar-gb��	id:bar-gb��	id:bar-gb��
id:db-degb��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��lei:test_executing_entity��	id:stc-gb��	id:hsb-gb��	id:cgm-gb��	id:cgm-gb��	id:hsb-gb��
id:db-degb��
id:db-degb��	id:hsb-gb��	id:hsb-gb��	id:hsb-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��	id:stc-gb��
id:db-degb��
id:db-degb��
id:db-degb��
id:db-degb��
id:bnp-fft��
id:db-degb��
id:db-degb��
id:db-degb��
id:db-degb��
id:bnp-fft��	id:hsb-gb��	id:hsb-gb��	id:stc-gb��	id:stc-gb��	id:hsb-gb��	id:cit-us��	id:cit-us��	id:cit-us��	id:cit-us��	id:cit-us��	id:cit-us��	id:cit-us��	id:cit-us��	id:cit-us��	id:cit-us��	id:bnp-fr��	id:stc-gb��id:ms-gb��	id:stc-gb��	id:bnp-fr��id:ms-gb��	id:ubs-ch��	id:bar-gb��	id:bar-gb��	id:stc-gb��	id:bar-gb��	id:bar-gb��	id:sst-us��	id:stc-gb��	id:bnp-fr��	id:bar-gb��	id:stc-gb��	id:bar-gb��	id:bar-gb��	id:bar-gb��	id:bar-gb��	id:bar-gb��	id:bar-gb��	id:bnp-fr��	id:bnp-fr��id:jpm-usgb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��	id:cit-us��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:db-de��	id:ubs-ch��id:ms-gb��id:ml-gb��	id:bnp-fr��id:ml-gb��id:ml-gb��id:ml-gb��id:ml-gb��id:ml-gb��	id:cit-us��id:ms-gb��	id:bnp-fr��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ml-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��id:ms-gb��	id:bar-gb��	id:hsb-gb��	id:hsb-gb��	id:bar-gb��	id:bnp-fr��id:jpm-usgb��	id:jpm-de��id:jpm-usgb��	id:stc-gb��	id:boa-fr��	id:stc-gb��	id:stc-gb��	id:stc-gb��id:ml-gb��id:db-de��	id:jpm-br��	id:hsb-gb��	id:hsb-gb��	id:jpm-co��	id:ubs-ch��	id:stc-gb��	id:bar-gb��	id:bar-gb��	id:bar-gb��
id:db-degb��	id:cit-us��	id:cit-us��	id:cit-us��	id:cit-us�j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  �
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��id:aiml��
id:carvera��
id:carvera��
id:carvera��
id:carvera��id:aiml��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:salterb��
id:salterb��
id:salterb��
id:salterb��
id:salterb��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��id:aiml��
id:carvera��
id:carvera��
id:carvera��
id:carvera��id:aiml��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:salterb��
id:salterb��
id:salterb��
id:salterb��
id:salterb��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:ntmulti��
id:ntmulti��id:abp��id:palm��id:pme��id:palm��id:palm��id:sicshort_ap��id:sicshort_ap��id:sicshort_ap��
id:abdlpus��id:abp��
id:aemlcdf_ap��id:cittrf_ap��id:ffac��	id:mp83lc��	id:orchid��id:palm��id:pme��
id:publica��id:sgil��id:sicard_ap��
id:siclcbf_ap��id:sictrf_ap��id:szz11��id:szzlc��id:uslcbf_ap��id:ustrf_ap��id:pme��id:abp��id:szzlc��id:siciglcf_ap��id:wmb��id:pme��id:siciglcf_ap��id:abp��id:szzlc�e(�id:wmb��id:sgil��id:sgil��id:palm��
id:sic1cbf_ap��id:pme��id:sgil��
id:aemlcdf_ap��id:apk��	id:mp83lc��
id:publica��
id:siclcbf_ap��id:siclcbfu_ap��id:szzlc��id:uslcbf_ap��id:siciglcf_ap��id:wmb��
id:ntmulti��
id:ntmulti��
id:ntmulti��
id:ntmulti��
id:ntmulti��id:palm��id:palm��id:szz11��
id:abdlpus��id:abp��
id:aemlcdf_ap��id:cittrf_ap��	id:mp83lc��
id:pennine��
id:publica��id:sgil��
id:siclcbf_ap��id:siclcbfu_ap��id:sictrf_ap��id:szzlc��id:uslcbf_ap��id:ustrf_ap��id:wmb��id:palm��	id:kyburg��
id:abdlpus��id:abp��
id:aemlcdf_ap��id:cittrf_ap��id:ffac��	id:mp83lc��id:palm��
id:pennine��
id:publica��id:sgil��id:sicigtrf_ap��id:siclcbfu_ap��id:sictrf_ap��id:szzlc��id:ustrf_ap��	id:viking��id:wmb��id:siciglcf_ap��	id:mp83lc��id:palm��
id:pennine��id:siciglcf_ap��
id:siclcbf_ap��id:sictrf_ap��id:szzlc��id:ustrf_ap��	id:mp83lc��id:palm��
id:pennine��
id:publica��
id:abdlpus��id:abp��
id:aemlcdf_ap��id:cittrf_ap��id:sgil��id:siciglcf_ap��id:sicigtrf_ap��
id:siclcbf_ap��id:sictrf_ap��id:szz11��id:szzlc��id:uslcbf_ap��id:ustrf_ap��id:palm��	id:kyburg��	id:kyburg��
id:pennine��id:siclcbfu_ap��	id:viking��id:wmb��
id:ntmulti��id:apk��id:bia4��	id:mp83lc��id:pme��
id:publica��id:siclcbfu_ap��id:wmb��
id:ntmulti��id:sicav123��id:sicvfef06��
id:ntmulti��id:alps��
id:empire2��
id:ntmulti��id:siccdf_ap��id:uscdf_ap��id:apk��	id:kyburg��	id:kyburg��	id:kyburg��	id:kyburg��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��id:aiml��
id:carvera��
id:carvera��
id:carvera��
id:carvera��id:aiml��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:salterb��
id:salterb��
id:salterb��
id:salterb��
id:salterb��
id:carvera��
id:carvera��
id:carvera��
id:carvera��
id:carvera�]�(j%  jO  jU  jX  j[  j^  ja  jd  jg  e]�(j2  jk  jm  jo  jq  js  ju  jw  jy  e]�(j5  j7  j|  j~  j�  j�  j�  j�  j�  j�  e]�(j:  j<  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j?  jA  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jD  jF  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jI  jK  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jN  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jQ  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jT  j�  j�  j�  j�  j�  j�  j�  j	  e]�(jW  jY  j	  j	  j	  j
	  j	  j	  j	  j	  e]�(j\  j^  j	  j	  j	  j	  j	  j	  j!	  j#	  e]�(ja  jc  j&	  j(	  j*	  j,	  j.	  j0	  j2	  j4	  e]�(jf  jh  j7	  j9	  j;	  j=	  j?	  jA	  jC	  jE	  e]�(jk  jm  jH	  jJ	  jL	  jN	  jP	  jR	  jT	  jV	  e]�(jp  jr  jY	  j[	  j]	  j_	  ja	  jc	  je	  jg	  e]�(ju  jw  jj	  jl	  jn	  jp	  jr	  jt	  jv	  jx	  e]�(jz  j|  j{	  j}	  j	  j�	  j�	  j�	  j�	  j�	  e]�(j  j�  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  e]�(j�  j�  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  e]�(j�  j�  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  e]�(j�  j�  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  e]�(j�  j�  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  e]�(j�  j�  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j�	  e]�(j�  j�  j�	  j�	  j�	  j�	  j�	  j�	  j�	  j 
  e]�(j�  j�  j
  j
  j
  j	
  j
  j
  j
  j
  e]�(j�  j�  j
  j
  j
  j
  j
  j
  j 
  j"
  e]�(j�  j�  j%
  j'
  j)
  j+
  j-
  j/
  j1
  j3
  e]�(j�  j6
  j8
  j:
  j<
  j>
  j@
  jB
  jD
  e]�(j�  jG
  jI
  jK
  jM
  jO
  jQ
  jS
  jU
  e]�(j�  jX
  jZ
  j\
  j^
  j`
  jb
  jd
  jf
  e]�(j�  ji
  jk
  jm
  jo
  jq
  js
  ju
  jw
  e]�(j�  jz
  j|
  j~
  j�
  j�
  j�
  j�
  j�
  e]�(j�  j�  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j�  j�  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j�  j�  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j�  j�  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j�  j�  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j�  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j�  j�  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j�  j�  j  j  j  j  j
  j  j  j  e]�(j�  j�  j  j  j  j  j  j  j  j!  e]�(j�  j�  j$  j&  j(  j*  j,  j.  j0  j2  e]�(j�  j�  j5  j7  j9  j;  j=  j?  jA  jC  e]�(j�  j�  jF  jH  jJ  jL  jN  jP  jR  jT  e]�(j�  j�  jW  jY  j[  j]  j_  ja  jc  je  e]�(j�  j  jh  jj  jl  jn  jp  jr  jt  jv  e]�(j  j  jy  j{  j}  j  j�  j�  j�  j�  e]�(j	  j  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j  j  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j  j  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j  j  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j  j  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j"  j$  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j'  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j*  j  j  j  j  j	  j  j
  j  e]�(j-  j  j  j  j  j  j  j  j   e]�(j0  j#  j%  j'  j)  j+  j-  j/  j1  e]�(j3  j4  j6  j8  j:  j<  j>  j@  jB  e]�(j6  j8  jE  jG  jI  jK  jM  jO  jQ  jS  e]�(j;  j=  jV  jX  jZ  j\  j^  j`  jb  jd  e]�(j@  jB  jg  ji  jk  jm  jo  jq  js  ju  e]�(jE  jG  jx  jz  j|  j~  j�  j�  j�  j�  e]�(jJ  jL  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jO  jQ  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jT  jV  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jY  j[  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j^  j`  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jc  je  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jh  jj  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jm  jo  j 
  j
  j
  j
  j
  j

  j
  j
  e]�(jr  jt  j
  j
  j
  j
  j
  j
  j
  j
  e]�(jw  jy  j"
  j$
  j&
  j(
  j*
  j,
  j.
  j0
  e]�(j|  j~  j3
  j5
  j7
  j9
  j;
  j=
  j?
  jA
  e]�(j�  j�  jD
  jF
  jH
  jJ
  jL
  jN
  jP
  jR
  e]�(j�  j�  jU
  jW
  jY
  j[
  j]
  j_
  ja
  jc
  e]�(j�  j�  jf
  jh
  jj
  jl
  jn
  jp
  jr
  jt
  e]�(jw
  jy
  j{
  j}
  j
  j�
  j�
  j�
  e]�(j�  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j�  j�  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j�  j�  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j�  j�  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j�  j�  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j�  j�  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j�  j�  j�
  j�
  j�
  j�
  j�
  j�
  j�
  j�
  e]�(j�  j�  j�
  j  j  j  j  j	  j  j
  e]�(j�  j�  j  j  j  j  j  j  j  j  e]�(j�  j�  j!  j#  j%  j'  j)  j+  j-  j/  e]�(j�  j�  j2  j4  j6  j8  j:  j<  j>  j@  e]�(j�  j�  jC  jE  jG  jI  jK  jM  jO  jQ  e]�(j�  j�  jT  jV  jX  jZ  j\  j^  j`  jb  e]�(j�  j�  je  jg  ji  jk  jm  jo  jq  js  e]�(j�  j�  jv  jx  jz  j|  j~  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j  j�  j   j  j  j  j  j
  j  e]�(j  j  j  j  j  j  j  j  j  j  e]�(j	  j  j   j"  j$  j&  j(  j*  j,  j.  e]�(j  j  j1  j3  j5  j7  j9  j;  j=  j?  e]�(j  j  jB  jD  jF  jH  jJ  jL  jN  jP  e]�(j  j  jS  jU  jW  jY  j[  j]  j_  ja  e]�(j  j  jd  jf  jh  jj  jl  jn  jp  jr  e]�(j"  j$  ju  jw  jy  j{  j}  j  j�  j�  e]�(j'  j)  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j,  j.  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j1  j3  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j6  j8  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j;  j=  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j@  jB  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jE  jG  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jJ  jL  j�  j�  j  j  j  j  j	  j  e]�(jO  jQ  j  j  j  j  j  j  j  j  e]�(jT  jV  j  j!  j#  j%  j'  j)  j+  j-  e]�(jY  j[  j0  j2  j4  j6  j8  j:  j<  j>  e]�(j^  j`  jA  jC  jE  jG  jI  jK  jM  jO  e]�(jc  je  jR  jT  jV  jX  jZ  j\  j^  j`  e]�(jh  jj  jc  je  jg  ji  jk  jm  jo  jq  e]�(jt  jv  jx  jz  j|  j~  j�  j�  e]�(jm  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jp  j�  j�  j�  j�  j�  j�  j�  j�  e]�(js  ju  j�  j�  j�  j�  j�  j�  j�  j�  e]�(jx  jz  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j}  j  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j   j  j  j  j  j
  e]�(j�  j�  j
  j  j  j  j  j  j  j  e]�(j�  j�  j  j   j"  j$  j&  j(  j*  j,  e]�(j�  j�  j/  j1  j3  j5  j7  j9  j;  j=  e]�(j�  j�  j@  jB  jD  jF  jH  jJ  jL  jN  e]�(j�  j�  jQ  jS  jU  jW  jY  j[  j]  j_  e]�(j�  j�  jb  jd  jf  jh  jj  jl  jn  jp  e]�(j�  js  ju  jw  jy  j{  j}  j  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j  j  j  j  j	  e]�(j�  j  j  j  j  j  j  j  j  e]�(j�  j  j  j!  j#  j%  j'  j)  j+  e]�(j�  j.  j0  j2  j4  j6  j8  j:  j<  e]�(j�  j?  jA  jC  jE  jG  jI  jK  jM  e]�(j�  jP  jR  jT  jV  jX  jZ  j\  j^  eet�bhhK ��h��R�(KKK���h�b1�����R�(Kh"NNNJ����J����K t�b�C��t�bhhK ��h��R�(KKK���j�  �C�                                                                                                                                                       �t�bhhK ��h��R�(KKK���h!�]�(�CNH/USD��IDR/USD��IDR/USD_fx forward��IDR/USD_fx forward��THB/USD_fx forward��THB/USD_fx forward��MYR/USD_fx forward��SGD/GBP��SGD/EUR��USD/EUR��BRL/USD_fx forward��BRL/USD_fx forward��BRL/USD_fx forward��BRL/USD_fx forward��BRL/USD_fx forward��BRL/USD_fx forward��BRL/USD_fx forward��BRL/USD_fx forward��BRL/USD_fx forward��BRL/USD_fx forward��BRL/USD_fx forward��BRL/USD_fx forward��BRL/USD_fx forward��BRL/USD_fx forward��BRL/USD_fx forward��BRL/USD_fx forward��BRL/USD_fx forward��BRL/USD_fx forward�j�  j�  j�  j�  j�  �IDR/USD_fx forward��IDR/USD_fx forward��IDR/USD_fx forward��IDR/USD_fx forward��IDR/USD_fx forward�j�  �IDR/USD_fx forward��CNH/USD_fx forward��CNH/USD_fx forward��MXN/USD_fx forward��IDR/USD_fx forward��IDR/USD_fx forward��IDR/USD_fx forward��IDR/USD_fx forward��IDR/USD_fx forward��IDR/USD_fx forward��IDR/USD_fx forward��IDR/USD_fx forward��IDR/USD_fx forward��IDR/USD_fx forward��IDR/USD_fx forward��RON/USD��ZAR/USD��PLN/USD��HUF/USD��CZK/USD��RON/USD_fx forward��ZAR/USD_fx forward��ZAR/USD_fx forward��ZAR/USD_fx forward��ZAR/USD_fx forward��ZAR/USD_fx forward��ZAR/USD_fx forward��ZAR/USD_fx forward��ZAR/USD_fx forward��ZAR/USD_fx forward��ZAR/USD_fx forward��ZAR/USD_fx forward��ZAR/USD_fx forward��ZAR/USD_fx forward��ZAR/USD_fx forward��ZAR/USD_fx forward��ZAR/USD_fx forward��ZAR/USD_fx forward��ZAR/USD_fx swap�j�  �MXN/USD_fx forward��MXN/USD_fx forward��MXN/USD_fx forward��MXN/USD_fx forward��MXN/USD_fx forward��MXN/USD_fx forward��MXN/USD_fx forward��MXN/USD_fx forward��MXN/USD_fx forward��MXN/USD_fx forward��MXN/USD_fx forward��MXN/USD_fx forward��MXN/USD_fx forward��MXN/USD_fx forward��MXN/USD_fx forward��MXN/USD_fx forward��MXN/USD_fx forward��RON/USD_fx forward��PLN/USD_fx forward��PLN/USD_fx forward��PLN/USD_fx forward��PLN/USD_fx forward��PLN/USD_fx forward��PLN/USD_fx forward��PLN/USD_fx forward��PLN/USD_fx forward��CZK/USD_fx forward��CZK/USD_fx forward��CZK/USD_fx forward��CZK/USD_fx forward��CZK/USD_fx forward��CZK/USD_fx forward��CZK/USD_fx forward��CZK/USD_fx forward��CZK/USD_fx forward��CZK/USD_fx forward��CZK/USD_fx forward��CZK/USD_fx forward��CZK/USD_fx forward��CZK/USD_fx forward��CZK/USD_fx forward��CZK/USD_fx forward��CZK/USD_fx forward��CZK/USD_fx swap�j�  j�  �BRL/USD_fx forward��BRL/USD_fx forward��BRL/USD_fx forward��BRL/USD_fx forward��MXN/USD��PEN/USD_fx forward��PEN/USD_fx forward��PEN/USD_fx forward��PEN/USD_fx forward��PEN/USD_fx forward��PEN/USD_fx forward��PEN/USD_fx forward��BRL/USD��CHF/USD_fx swap��USD/GBP_fx swap��COP/USD�j�  �USD/EUR_fx swap��USD/EUR_fx swap��USD/EUR_fx swap��USD/EUR_fx swap�j�  j�  j�  j=  j�  et�bj  )��}�(j  hhK ��h��R�(KK���j  �]�(j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  j�  et�bj  j  )��ube]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3h4h5h6h7h8h9h:h;et�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h<at�bhfNu��R�h
h}�(hhhK ��h��R�(KK
��h!�]�(h=h>h?h@hAhBhChDhEhFhGhHhIet�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hJat�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hKat�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hLhMhNhOet�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hPat�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(hQhRhShThUhVhWhXhYhZh[h\h]h^h_h`et�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�haat�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hbat�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hcat�bhfNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hdat�bhfNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h~�mgr_locs��builtins��slice���K KK��R�u}�(j�  j\  j�  j�  KKK��R�u}�(j�  jb  j�  j�  KK%K��R�u}�(j�  j  j�  j�  K%K&K��R�u}�(j�  j  j�  j�  K&K'K��R�u}�(j�  j!  j�  j�  K'K+K��R�u}�(j�  j�  j�  j�  K+K,K��R�u}�(j�  j�  j�  j�  K,K<K��R�u}�(j�  j�  j�  j�  K<K=K��R�u}�(j�  j�  j�  j�  K=K>K��R�u}�(j�  j�  j�  j�  K>K?K��R�u}�(j�  jN  j�  j�  K?K@K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.