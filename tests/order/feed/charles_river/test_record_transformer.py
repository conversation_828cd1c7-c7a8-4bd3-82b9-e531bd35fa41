from typing import Dict

import pandas as pd
import pytest
from prefect.engine.signals import SKIP

from swarm_tasks.order.feed.charles_river.processor.record_transformer import (
    CharlesRiverFrameTransformer,
)
from swarm_tasks.order.feed.charles_river.processor.record_transformer import Params
from swarm_tasks.order.feed.charles_river.static import SourceColumns


@pytest.fixture()
def sample_source_frame_list() -> dict:
    frame_dict = {
        "order": pd.DataFrame(
            {
                SourceColumns.ORDER_ID: [1, 2, 3],
                SourceColumns.ORDER_CREATE_DATE: [
                    "2022-04-13",
                    "2022-04-14",
                    "2022-04-15",
                ],
                SourceColumns.TRADE_DATE: [
                    "2021-04-13",
                    "2021-04-14",
                    "2021-04-15",
                ],
                SourceColumns.ORDER_STATUS: ["A", "B", "C"],
            }
        ),
        "allocation": pd.DataFrame(
            {
                SourceColumns.ORDER_ID: [1, 2, 3],
                SourceColumns.ALLO_DIRECTED_BROKER: [1.0, 2.0, 3.0],
                SourceColumns.ORDER_EXEC_QTY: [1.0, 2.0, 3.0],
                SourceColumns.ACCOUNT_ID: ["DOKTOR", "MEKONG", "TANGO"],
            }
        ),
        "fills": pd.DataFrame(
            {
                SourceColumns.ORDER_ID: [1, 2, 3],
                SourceColumns.FILL_QTY: [1.0, 2.0, 3.0],
            }
        ),
        "place": pd.DataFrame(
            {
                SourceColumns.ORDER_ID: [1, 2, 3],
                SourceColumns.PLACE_REASON: [1.0, 2.0, 3.0],
                SourceColumns.PLACE_DATE: [
                    "2021-04-13",
                    "2021-04-14",
                    "2021-04-15",
                ],
            }
        ),
    }
    return frame_dict


@pytest.fixture()
def sample_source_frame_dict() -> Dict:
    """
    Sample source frame with repeated order ids
    """
    return {
        "order": pd.DataFrame(
            {
                SourceColumns.ORDER_ID: [1, 1, 3],
                SourceColumns.ORDER_CREATE_DATE: [
                    "2022-04-13",
                    "2022-04-14",
                    "2022-04-15",
                ],
                SourceColumns.TRADE_DATE: [
                    "2021-04-13",
                    "2021-04-14",
                    "2021-04-15",
                ],
                SourceColumns.ORDER_STATUS: ["A", "B", "C"],
            }
        ),
        "allocation": pd.DataFrame(
            {
                SourceColumns.ORDER_ID: [1, 1, 3],
                SourceColumns.ALLO_DIRECTED_BROKER: [1.0, 2.0, 3.0],
                SourceColumns.ORDER_EXEC_QTY: [1.0, 2.0, 3.0],
                SourceColumns.ACCOUNT_ID: ["DOKTOR", "MEKONG", "TANGO"],
            }
        ),
        "place": pd.DataFrame(
            {
                SourceColumns.ORDER_ID: [1, 1, 3],
                SourceColumns.PLACE_REASON: [1.0, 2.0, 3.0],
                SourceColumns.PLACE_DATE: [
                    "2021-04-13",
                    "2021-04-14",
                    "2021-04-15",
                ],
            }
        ),
    }


class TestCharlesRiverRecordTransformer:
    """
    Test Charles River Record Creation Logic
    """

    @pytest.mark.parametrize(
        "file_type",
        ["orders", "allocation", ""],
    )
    def test_skip_scenario(self, sample_source_frame_list, file_type):
        # sample_source_frame_list = sample_source_frame_list[2:]
        pre_process_result = {}
        pre_process_result["order"] = sample_source_frame_list["order"]
        pre_process_result["fills"] = sample_source_frame_list["fills"]
        with pytest.raises(SKIP):
            params = Params(trigger_file_type=file_type)
            task = CharlesRiverFrameTransformer(name="test_task", params=params)
            task.execute(pre_process_result=pre_process_result, params=params)

    def test_empty_data_scenario(
        self,
    ):
        with pytest.raises(SKIP):
            params = Params(trigger_file_type="order")
            task = CharlesRiverFrameTransformer(name="test_task", params=params)
            task.execute(pre_process_result=dict(), params=params)

    def test_for_order_scenario(self, sample_source_frame_list):
        params = Params(trigger_file_type="order")
        task = CharlesRiverFrameTransformer(name="test_task", params=params)
        result = task.execute(
            pre_process_result=sample_source_frame_list,
            params=params,
            file_url="/dir/random_name.txt",
        )
        result_df = pd.read_csv(result.path).fillna(pd.NA)
        expected_df = pd.DataFrame(
            [
                {
                    "ORDERID": 1,
                    "ACCOUNTID": "DOKTOR",
                    "ORDERCREATEDATE": "2022-04-13",
                    "TRADEDATE": "2021-04-13",
                    "ORDERSTATUS": "A",
                    "__order_status__": "NEWO",
                    "FILLQTY": pd.NA,
                    "PLACEREASEON": 1.0,
                    "PLACEDATE": "2021-04-13",
                    "ALLOCATIONDIRECTEDBROKER": 1.0,
                    "ORDEREXECQTY": 1.0,
                    "__ACCOUNT_IDS__": "DOKTOR",
                    "__file_type__": "order",
                    "__source_file__": "random_name",
                },
                {
                    "ORDERID": 1,
                    "ACCOUNTID": "DOKTOR",
                    "ORDERCREATEDATE": "2022-04-13",
                    "TRADEDATE": "2021-04-13",
                    "ORDERSTATUS": "A",
                    "__order_status__": "PARF",
                    "FILLQTY": 1.0,
                    "PLACEREASEON": 1.0,
                    "PLACEDATE": "2021-04-13",
                    "ALLOCATIONDIRECTEDBROKER": 1.0,
                    "ORDEREXECQTY": 1.0,
                    "__ACCOUNT_IDS__": "DOKTOR",
                    "__file_type__": "order",
                    "__source_file__": "random_name",
                },
                {
                    "ORDERID": 2,
                    "ACCOUNTID": "MEKONG",
                    "ORDERCREATEDATE": "2022-04-14",
                    "TRADEDATE": "2021-04-14",
                    "ORDERSTATUS": "B",
                    "__order_status__": "NEWO",
                    "FILLQTY": pd.NA,
                    "PLACEREASEON": 2.0,
                    "PLACEDATE": "2021-04-14",
                    "ALLOCATIONDIRECTEDBROKER": 2.0,
                    "ORDEREXECQTY": 2.0,
                    "__ACCOUNT_IDS__": "MEKONG",
                    "__file_type__": "order",
                    "__source_file__": "random_name",
                },
                {
                    "ORDERID": 2,
                    "ACCOUNTID": "MEKONG",
                    "ORDERCREATEDATE": "2022-04-14",
                    "TRADEDATE": "2021-04-14",
                    "ORDERSTATUS": "B",
                    "__order_status__": "PARF",
                    "FILLQTY": 2.0,
                    "PLACEREASEON": 2.0,
                    "PLACEDATE": "2021-04-14",
                    "ALLOCATIONDIRECTEDBROKER": 2.0,
                    "ORDEREXECQTY": 2.0,
                    "__ACCOUNT_IDS__": "MEKONG",
                    "__file_type__": "order",
                    "__source_file__": "random_name",
                },
                {
                    "ORDERID": 3,
                    "ACCOUNTID": "TANGO",
                    "ORDERCREATEDATE": "2022-04-15",
                    "TRADEDATE": "2021-04-15",
                    "ORDERSTATUS": "C",
                    "__order_status__": "NEWO",
                    "FILLQTY": pd.NA,
                    "PLACEREASEON": 3.0,
                    "PLACEDATE": "2021-04-15",
                    "ALLOCATIONDIRECTEDBROKER": 3.0,
                    "ORDEREXECQTY": 3.0,
                    "__ACCOUNT_IDS__": "TANGO",
                    "__file_type__": "order",
                    "__source_file__": "random_name",
                },
                {
                    "ORDERID": 3,
                    "ACCOUNTID": "TANGO",
                    "ORDERCREATEDATE": "2022-04-15",
                    "TRADEDATE": "2021-04-15",
                    "ORDERSTATUS": "C",
                    "__order_status__": "PARF",
                    "FILLQTY": 3.0,
                    "PLACEREASEON": 3.0,
                    "PLACEDATE": "2021-04-15",
                    "ALLOCATIONDIRECTEDBROKER": 3.0,
                    "ORDEREXECQTY": 3.0,
                    "__ACCOUNT_IDS__": "TANGO",
                    "__file_type__": "order",
                    "__source_file__": "random_name",
                },
            ]
        )

        pd.testing.assert_frame_equal(
            result_df.sort_index(axis=1), expected_df.sort_index(axis=1)
        )

    def test_for_allocation_scenario(
        self,
        sample_source_frame_list,
    ):
        params = Params(trigger_file_type="allocation")
        task = CharlesRiverFrameTransformer(name="test_task", params=params)
        result = task.execute(
            pre_process_result=sample_source_frame_list,
            params=params,
            file_url="/dir/random_name.txt",
        )
        result_df = pd.read_csv(result.path).fillna(pd.NA)
        expected_df = pd.DataFrame(
            [
                {
                    "ORDERID": 1,
                    "ACCOUNTID": "DOKTOR",
                    "ALLOCATIONDIRECTEDBROKER": 1.0,
                    "ORDEREXECQTY": 1.0,
                    "__order_status__": "NEWO",
                    "ORDERCREATEDATE": "2022-04-13",
                    "TRADEDATE": "2021-04-13",
                    "ORDERSTATUS": "A",
                    "PLACEREASEON": 1.0,
                    "PLACEDATE": "2021-04-13",
                    "__file_type__": "allocation",
                    "__source_file__": "random_name",
                },
                {
                    "ORDERID": 1,
                    "ACCOUNTID": "DOKTOR",
                    "ALLOCATIONDIRECTEDBROKER": 1.0,
                    "ORDEREXECQTY": 1.0,
                    "__order_status__": "FILL",
                    "ORDERCREATEDATE": "2022-04-13",
                    "TRADEDATE": "2021-04-13",
                    "ORDERSTATUS": "A",
                    "PLACEREASEON": 1.0,
                    "PLACEDATE": "2021-04-13",
                    "__file_type__": "allocation",
                    "__source_file__": "random_name",
                },
                {
                    "ORDERID": 2,
                    "ACCOUNTID": "MEKONG",
                    "ALLOCATIONDIRECTEDBROKER": 2.0,
                    "ORDEREXECQTY": 2.0,
                    "__order_status__": "NEWO",
                    "ORDERCREATEDATE": "2022-04-14",
                    "TRADEDATE": "2021-04-14",
                    "ORDERSTATUS": "B",
                    "PLACEREASEON": 2.0,
                    "PLACEDATE": "2021-04-14",
                    "__file_type__": "allocation",
                    "__source_file__": "random_name",
                },
                {
                    "ORDERID": 2,
                    "ACCOUNTID": "MEKONG",
                    "ALLOCATIONDIRECTEDBROKER": 2.0,
                    "ORDEREXECQTY": 2.0,
                    "__order_status__": "FILL",
                    "ORDERCREATEDATE": "2022-04-14",
                    "TRADEDATE": "2021-04-14",
                    "ORDERSTATUS": "B",
                    "PLACEREASEON": 2.0,
                    "PLACEDATE": "2021-04-14",
                    "__file_type__": "allocation",
                    "__source_file__": "random_name",
                },
                {
                    "ORDERID": 3,
                    "ACCOUNTID": "TANGO",
                    "ALLOCATIONDIRECTEDBROKER": 3.0,
                    "ORDEREXECQTY": 3.0,
                    "__order_status__": "NEWO",
                    "ORDERCREATEDATE": "2022-04-15",
                    "TRADEDATE": "2021-04-15",
                    "ORDERSTATUS": "C",
                    "PLACEREASEON": 3.0,
                    "PLACEDATE": "2021-04-15",
                    "__file_type__": "allocation",
                    "__source_file__": "random_name",
                },
                {
                    "ORDERID": 3,
                    "ACCOUNTID": "TANGO",
                    "ALLOCATIONDIRECTEDBROKER": 3.0,
                    "ORDEREXECQTY": 3.0,
                    "__order_status__": "FILL",
                    "ORDERCREATEDATE": "2022-04-15",
                    "TRADEDATE": "2021-04-15",
                    "ORDERSTATUS": "C",
                    "PLACEREASEON": 3.0,
                    "PLACEDATE": "2021-04-15",
                    "__file_type__": "allocation",
                    "__source_file__": "random_name",
                },
            ]
        )

        pd.testing.assert_frame_equal(
            result_df.sort_index(axis=1), expected_df.sort_index(axis=1)
        )

    def test_for_scenario_where_file_name_is_not_provided(
        self, sample_source_frame_list
    ):
        params = Params(trigger_file_type="order")
        task = CharlesRiverFrameTransformer(name="test_task", params=params)
        result = task.execute(
            pre_process_result=sample_source_frame_list, params=params
        )
        expected_df = pd.DataFrame(
            [
                {
                    "ORDERID": 1,
                    "ACCOUNTID": "DOKTOR",
                    "ORDERCREATEDATE": "2022-04-13",
                    "TRADEDATE": "2021-04-13",
                    "ORDERSTATUS": "A",
                    "__order_status__": "NEWO",
                    "FILLQTY": pd.NA,
                    "PLACEREASEON": 1.0,
                    "PLACEDATE": "2021-04-13",
                    "ALLOCATIONDIRECTEDBROKER": 1.0,
                    "ORDEREXECQTY": 1.0,
                    "__ACCOUNT_IDS__": "DOKTOR",
                    "__file_type__": "order",
                    "__source_file__": pd.NA,
                },
                {
                    "ORDERID": 1,
                    "ACCOUNTID": "DOKTOR",
                    "ORDERCREATEDATE": "2022-04-13",
                    "TRADEDATE": "2021-04-13",
                    "ORDERSTATUS": "A",
                    "__order_status__": "PARF",
                    "FILLQTY": 1.0,
                    "PLACEREASEON": 1.0,
                    "PLACEDATE": "2021-04-13",
                    "ALLOCATIONDIRECTEDBROKER": 1.0,
                    "ORDEREXECQTY": 1.0,
                    "__ACCOUNT_IDS__": "DOKTOR",
                    "__file_type__": "order",
                    "__source_file__": pd.NA,
                },
                {
                    "ORDERID": 2,
                    "ACCOUNTID": "MEKONG",
                    "ORDERCREATEDATE": "2022-04-14",
                    "TRADEDATE": "2021-04-14",
                    "ORDERSTATUS": "B",
                    "__order_status__": "NEWO",
                    "FILLQTY": pd.NA,
                    "PLACEREASEON": 2.0,
                    "PLACEDATE": "2021-04-14",
                    "ALLOCATIONDIRECTEDBROKER": 2.0,
                    "ORDEREXECQTY": 2.0,
                    "__ACCOUNT_IDS__": "MEKONG",
                    "__file_type__": "order",
                    "__source_file__": pd.NA,
                },
                {
                    "ORDERID": 2,
                    "ACCOUNTID": "MEKONG",
                    "ORDERCREATEDATE": "2022-04-14",
                    "TRADEDATE": "2021-04-14",
                    "ORDERSTATUS": "B",
                    "__order_status__": "PARF",
                    "FILLQTY": 2.0,
                    "PLACEREASEON": 2.0,
                    "PLACEDATE": "2021-04-14",
                    "ALLOCATIONDIRECTEDBROKER": 2.0,
                    "ORDEREXECQTY": 2.0,
                    "__ACCOUNT_IDS__": "MEKONG",
                    "__file_type__": "order",
                    "__source_file__": pd.NA,
                },
                {
                    "ORDERID": 3,
                    "ACCOUNTID": "TANGO",
                    "ORDERCREATEDATE": "2022-04-15",
                    "TRADEDATE": "2021-04-15",
                    "ORDERSTATUS": "C",
                    "__order_status__": "NEWO",
                    "FILLQTY": pd.NA,
                    "PLACEREASEON": 3.0,
                    "PLACEDATE": "2021-04-15",
                    "ALLOCATIONDIRECTEDBROKER": 3.0,
                    "ORDEREXECQTY": 3.0,
                    "__ACCOUNT_IDS__": "TANGO",
                    "__file_type__": "order",
                    "__source_file__": pd.NA,
                },
                {
                    "ORDERID": 3,
                    "ACCOUNTID": "TANGO",
                    "ORDERCREATEDATE": "2022-04-15",
                    "TRADEDATE": "2021-04-15",
                    "ORDERSTATUS": "C",
                    "__order_status__": "PARF",
                    "FILLQTY": 3.0,
                    "PLACEREASEON": 3.0,
                    "PLACEDATE": "2021-04-15",
                    "ALLOCATIONDIRECTEDBROKER": 3.0,
                    "ORDEREXECQTY": 3.0,
                    "__ACCOUNT_IDS__": "TANGO",
                    "__file_type__": "order",
                    "__source_file__": pd.NA,
                },
            ]
        )
        result_df = pd.read_csv(result.path).fillna(pd.NA)

        pd.testing.assert_frame_equal(
            result_df.sort_index(axis=1), expected_df.sort_index(axis=1)
        )

    def test_it_can__group_account_ids_by_order_id(self):
        result: pd.Series = CharlesRiverFrameTransformer._group_account_ids_by_order_id(
            df=pd.DataFrame(
                data={
                    SourceColumns.ORDER_ID: [123, 456, 123],
                    SourceColumns.ACCOUNT_ID: ["DOKTOR", "TANGO", "MEKONG"],
                }
            ),
            allocation_account_id_delimiter=";",
        )

        pd.testing.assert_series_equal(
            left=result,
            right=pd.Series(
                data=[
                    "DOKTOR;MEKONG",
                    "TANGO",
                    "DOKTOR;MEKONG",
                ],
                name=SourceColumns.ACCOUNT_ID,
            ),
        )

    def test_it_does_not_generate_account_ids_for_allocation_files(
        self,
        sample_source_frame_dict: Dict,
    ):
        params = Params(trigger_file_type="allocation")

        task = CharlesRiverFrameTransformer(
            name="CharlesRiverFrameTransformer", params=params
        )

        result: pd.DataFrame = task.transform_allocation_file(
            source_frame_dict=sample_source_frame_dict,
        )

        assert SourceColumns.ACCOUNT_IDS not in result.columns
