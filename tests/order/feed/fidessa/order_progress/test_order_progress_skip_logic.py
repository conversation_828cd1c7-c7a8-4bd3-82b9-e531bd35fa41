import os
from unittest.mock import patch

import pandas as pd
import pytest
from prefect.engine import signals
from swarm.task.auditor import Auditor

from swarm_tasks.order.feed.fidessa.order_progress.order_progress_skip_logic import (
    OrderProgressSkipLogic,
)
from swarm_tasks.order.feed.fidessa.order_progress.order_progress_skip_logic import (
    Params,
)
from swarm_tasks.order.feed.fidessa.order_progress.order_progress_skip_logic import (
    SourceColumns,
)

os.environ["SWARM_FILE_URL"] = "ORDER_PROGRESS-202106171830.csv"


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    Creates an empty dataframe
    """
    df = pd.DataFrame()
    return df


@pytest.fixture()
def source_df_tuesday_file() -> pd.DataFrame:
    """
    Creates a data frame for a file on Tuesday
    """
    df = pd.DataFrame(
        [
            # Record 1: not skipped: trade on same day
            {
                SourceColumns.ENTERED_DATE: "20220208",
                SourceColumns.ENTERED_TIME: "09:20:15",
                SourceColumns.TOP_LEVEL: "Y",
            },
            # Record 2: skipped because the Entered_date_time < prev business day of file
            {
                SourceColumns.ENTERED_DATE: "20220207",
                SourceColumns.ENTERED_TIME: "09:15:15",
                SourceColumns.TOP_LEVEL: "Y",
            },
            # Record 3: skipped because Top_Level = N
            {
                SourceColumns.ENTERED_DATE: "20220208",
                SourceColumns.ENTERED_TIME: "19:20:15",
                SourceColumns.TOP_LEVEL: "N",
            },
        ]
    )
    return df


@pytest.fixture()
def source_df_monday_file() -> pd.DataFrame:
    """
    Creates a data frame for a file from Monday. Used for testing whether records from the previous
    business day are skipped
    """
    df = pd.DataFrame(
        [
            # Record 1: not skipped: trade on same day
            {
                SourceColumns.ENTERED_DATE: "20220207",
                SourceColumns.ENTERED_TIME: "09:20:15",
                SourceColumns.TOP_LEVEL: "Y",
            },
            # Record 2: skipped because the Entered_date_time < prev business day of file
            {
                SourceColumns.ENTERED_DATE: "20220204",
                SourceColumns.ENTERED_TIME: "09:20:15",
                SourceColumns.TOP_LEVEL: "Y",
            },
            # Record 3: skipped because Top_Level = N
            {
                SourceColumns.ENTERED_DATE: "20220207",
                SourceColumns.ENTERED_TIME: "19:20:15",
                SourceColumns.TOP_LEVEL: "N",
            },
            # Record 4: not skipped because trade on Saturday
            {
                SourceColumns.ENTERED_DATE: "20220205",
                SourceColumns.ENTERED_TIME: "19:20:15",
                SourceColumns.TOP_LEVEL: "Y",
            },
        ]
    )
    return df


@pytest.fixture()
def expected_result_tuesday_file():
    """Expected result for Tuesday file"""
    df = pd.DataFrame(
        [
            # Record 1: not skipped: trade on same day
            {
                SourceColumns.ENTERED_DATE: "20220208",
                SourceColumns.ENTERED_TIME: "09:20:15",
                SourceColumns.TOP_LEVEL: "Y",
            },
        ],
        index=[0],
    )
    return df


@pytest.fixture()
def expected_result_monday_file():
    """Expected result for Monday file"""
    df = pd.DataFrame(
        [
            # Record 1: not skipped: trade on same day
            {
                SourceColumns.ENTERED_DATE: "20220207",
                SourceColumns.ENTERED_TIME: "09:20:15",
                SourceColumns.TOP_LEVEL: "Y",
            },
            # Record 4: not skipped because trade on Saturday
            {
                SourceColumns.ENTERED_DATE: "20220205",
                SourceColumns.ENTERED_TIME: "19:20:15",
                SourceColumns.TOP_LEVEL: "Y",
            },
        ],
        index=[0, 3],
    )
    return df


@pytest.fixture()
def params_fixture() -> Params:
    params = Params(
        **{
            "date_attribute": SourceColumns.ENTERED_DATE,
            "time_attribute": SourceColumns.ENTERED_TIME,
            "timezone": "Europe/London",
        }
    )
    return params


class TestOrderProgressSkipLogic:
    """Class to hold all the test cases for OrderProgressSkipLogic"""

    def test_empty_source_frame(self, empty_source_df, params_fixture):
        """Test for empty source frame"""
        os.environ["SWARM_FILE_URL"] = "/path/ORDER_PROGRESS-202202081830.csv"
        with pytest.raises(signals.SKIP):
            task = OrderProgressSkipLogic(name="skip-rows")
            task.execute(source_frame=empty_source_df, params=params_fixture)

    @pytest.mark.parametrize(
        "file_name,day",
        [
            ("/path/ORDER_PROGRESS-202202071830.csv", "Monday"),
            ("/path/ORDER_PROGRESS-202202081830.csv", "Tuesday"),
        ],
    )
    @patch.object(OrderProgressSkipLogic, "auditor")
    def test_cases_for_monday_and_tuesday_files(
        self,
        mock_auditor,
        file_name,
        day,
        source_df_monday_file,
        expected_result_monday_file,
        source_df_tuesday_file,
        expected_result_tuesday_file,
        params_fixture,
    ):
        """Tests all possible cases for a non-empty source frame. The 2 files used here are a file
        from Tuesday (previous business day is Monday) and a file from Monday (previous business
         day is Friday)
        """
        os.environ["SWARM_FILE_URL"] = file_name
        mock_auditor.return_value = Auditor(task_name="SkipRowsByDate")
        task = OrderProgressSkipLogic(name="skip-rows")
        if day == "Monday":
            source_frame = source_df_monday_file
            expected_result = expected_result_monday_file
        else:
            # day == 'Tuesday':
            source_frame = source_df_tuesday_file
            expected_result = expected_result_tuesday_file
        result = task.execute(source_frame=source_frame, params=params_fixture)
        assert result.equals(expected_result)

    @patch.object(OrderProgressSkipLogic, "auditor")
    def test_skip_all_rows(self, mock_auditor, source_df_monday_file, params_fixture):
        """Tests all possible cases for a non-empty source frame. The file here is a file
        from Tuesday, but all the trades are from earlier. All rows should be skipped
        """
        # Tuesday file
        os.environ["SWARM_FILE_URL"] = "/path/ORDER_PROGRESS-202202091830.csv"
        mock_auditor.return_value = Auditor(task_name="SkipRowsByDate")
        task = OrderProgressSkipLogic(name="skip-rows")
        with pytest.raises(signals.SKIP):
            task.execute(source_frame=source_df_monday_file, params=params_fixture)

    @pytest.mark.parametrize(
        "file_name",
        ["/path/ORDER_PROGRESS202202091830.csv", "/path/ORDER_PROGRESS-20220.csv"],
    )
    @patch.object(OrderProgressSkipLogic, "auditor")
    def test_invalid_file_name_raises_fail(
        self, mock_auditor, file_name, source_df_monday_file, params_fixture
    ):
        """Test for 2 cases where the file name is invalid. A Fail signal should be
        raised
        """
        # Tuesday file, but all records from Monday or earlier
        os.environ["SWARM_FILE_URL"] = file_name
        mock_auditor.return_value = Auditor(task_name="SkipRowsByDate")
        task = OrderProgressSkipLogic(name="skip-rows")
        with pytest.raises(signals.FAIL):
            task.execute(source_frame=source_df_monday_file, params=params_fixture)
