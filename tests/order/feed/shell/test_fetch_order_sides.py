import pandas as pd
import pytest
from addict import addict
from se_elastic_schema.static.mifid2 import BuySellIndicator
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order.transformations.bbg.audt.static import SourceColumns
from swarm.conf import Settings
from swarm.conf import SettingsCls

from swarm_tasks.order.feed.shell import fetch_order_sides
from swarm_tasks.order.feed.shell.fetch_order_sides import FetchOrderSides
from swarm_tasks.order.feed.shell.fetch_order_sides import Resources
from swarm_tasks.order.feed.shell.static import TempColumns


@pytest.fixture
def input_source_frame():
    return pd.DataFrame({SourceColumns.AGGR_FROM: [pd.NA, "123", pd.NA, "456"]})


@pytest.fixture
def normal_es_mock_hits():
    return pd.DataFrame(
        {
            OrderColumns.ID: ["123", "456"],
            OrderColumns.TRANSACTION_DETAILS_BUY_SELL_INDICATOR: [
                BuySellIndicator.BUYI.value,
                BuySellIndicator.SELL.value,
            ],
        }
    )


@pytest.fixture
def empty_es_mock_hits():
    return pd.DataFrame()


@pytest.fixture
def normal_expected_outcome():
    return pd.DataFrame({TempColumns.ES_ORDER_SIDES: [pd.NA, "BUY", pd.NA, "SELL"]})


@pytest.fixture
def empty_expected_outcome():
    return pd.DataFrame({TempColumns.ES_ORDER_SIDES: [pd.NA, pd.NA, pd.NA, pd.NA]})


class TestFetchOrderSides:
    @pytest.mark.parametrize(
        "es_mock_hits,expected_outcome",
        [
            ("normal_es_mock_hits", "normal_expected_outcome"),
            ("empty_es_mock_hits", "empty_expected_outcome"),
        ],
    )
    def test_end_to_end(
        self, mocker, request, input_source_frame, es_mock_hits, expected_outcome
    ):
        Settings.STACK = "dev-blue"
        Settings.FLOW_ID = "dummy.dev.steeleye.co:order-feed-samco-bbg-audt-processor"
        mock_connections = mocker.patch.object(
            SettingsCls, "connections", new_callable=mocker.PropertyMock
        )
        mock_connections.return_value = addict.Dict(
            {
                "tenant-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                        "expiry": "&expiry",
                    },
                    "MAX_TERMS_SIZE": 1024,
                }
            }
        )

        mock_es_scroll = mocker.patch.object(fetch_order_sides, "es_scroll")
        mock_es_scroll.return_value = request.getfixturevalue(es_mock_hits)

        task = FetchOrderSides(name="TestOrderSides")
        result = task.process(
            source_frame=input_source_frame,
            resources=Resources(es_client_key="tenant-data"),
        )

        pd.testing.assert_frame_equal(result, request.getfixturevalue(expected_outcome))
