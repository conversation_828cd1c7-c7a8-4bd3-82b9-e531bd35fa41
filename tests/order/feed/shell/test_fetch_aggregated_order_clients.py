import pandas as pd
import pytest
from addict import addict
from se_trades_tasks.order.static import OrderColumns
from se_trades_tasks.order.transformations.bbg.audt.static import SourceColumns
from se_trades_tasks.order_and_tr.party.static import PartiesFields
from swarm.conf import Settings
from swarm.conf import SettingsCls

from swarm_tasks.order.feed.shell import fetch_aggregated_order_clients
from swarm_tasks.order.feed.shell.fetch_aggregated_order_clients import (
    FetchAggregatedOrderClients,
)
from swarm_tasks.order.feed.shell.fetch_aggregated_order_clients import Resources
from swarm_tasks.order.feed.shell.static import TempColumns


@pytest.fixture
def input_source_frame():
    return pd.DataFrame(
        {
            SourceColumns.TS_ORD_NUM: [pd.NA, "456", pd.NA, "000"],
            SourceColumns.AUDIT_ID_TKT: ["123", pd.NA, "789", pd.NA],
        }
    )


@pytest.fixture
def normal_es_mock_hits():
    return pd.DataFrame(
        {
            OrderColumns.ORDER_IDENTIFIERS_AGGREGATED_ORDER_ID: [
                "dummy",
                "456",
                "123",
                "123",
            ],
            PartiesFields.PARTIES_CLIENT_FILE_ID: [
                "client_id_1",
                "client_id_2",
                "client_id_3",
                "client_id_4",
            ],
        }
    )


@pytest.fixture
def empty_es_mock_hits():
    return pd.DataFrame()


@pytest.fixture
def normal_expected_outcome():
    return pd.DataFrame(
        {
            TempColumns.ES_TS_ORD_NUM_CLIENTS: [pd.NA, {"client_id_2"}, pd.NA, pd.NA],
            TempColumns.ES_AUDIT_ID_TKT_CLIENTS: [
                {"client_id_3", "client_id_4"},
                pd.NA,
                pd.NA,
                pd.NA,
            ],
        }
    )


@pytest.fixture
def empty_expected_outcome():
    return pd.DataFrame(
        {
            TempColumns.ES_TS_ORD_NUM_CLIENTS: [pd.NA, pd.NA, pd.NA, pd.NA],
            TempColumns.ES_AUDIT_ID_TKT_CLIENTS: [pd.NA, pd.NA, pd.NA, pd.NA],
        }
    )


class TestFetchAggregatedOrderClients:
    @pytest.mark.parametrize(
        "es_mock_hits,expected_outcome",
        [
            ("normal_es_mock_hits", "normal_expected_outcome"),
            ("empty_es_mock_hits", "empty_expected_outcome"),
        ],
    )
    def test_end_to_end(
        self, mocker, request, input_source_frame, es_mock_hits, expected_outcome
    ):
        Settings.STACK = "dev-blue"
        Settings.FLOW_ID = "dummy.dev.steeleye.co:order-feed-samco-bbg-audt-processor"
        mock_connections = mocker.patch.object(
            SettingsCls, "connections", new_callable=mocker.PropertyMock
        )
        mock_connections.return_value = addict.Dict(
            {
                "tenant-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                        "expiry": "&expiry",
                    },
                    "MAX_TERMS_SIZE": 1024,
                }
            }
        )

        mock_es_scroll = mocker.patch.object(
            fetch_aggregated_order_clients, "es_scroll"
        )
        mock_es_scroll.return_value = request.getfixturevalue(es_mock_hits)

        task = FetchAggregatedOrderClients(name="TestAggregatedOrderClients")
        result = task.process(
            source_frame=input_source_frame,
            resources=Resources(es_client_key="tenant-data"),
        )

        # doing so to be able to compare sets
        result = result.astype(str).replace(
            to_replace="{'client_id_4', 'client_id_3'}",
            value="{'client_id_3', 'client_id_4'}",
        )
        expected = (
            request.getfixturevalue(expected_outcome)
            .astype(str)
            .replace(
                to_replace="{'client_id_4', 'client_id_3'}",
                value="{'client_id_3', 'client_id_4'}",
            )
        )

        pd.testing.assert_frame_equal(result, expected)
