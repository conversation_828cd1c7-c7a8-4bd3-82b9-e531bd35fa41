from pathlib import Path

import pytest
from se_core_tasks.core.core_dataclasses import ExtractPathResult

from swarm_tasks.order.feed.enfusion.v2.execution_record_mapper import (
    ExecutionRecordMapper,
)
from swarm_tasks.order.feed.enfusion.v2.execution_record_mapper import Params
from swarm_tasks.order.feed.enfusion.v2.static import ExecMapKeys

BASE_PATH: Path = Path(__file__).parent
DATA_PATH: Path = BASE_PATH.joinpath("data")
TEST_FILE: Path = DATA_PATH.joinpath("Example_Executions.csv")
TEST_FILE_WITHOUT_REQ_COLUMNS: Path = DATA_PATH.joinpath(
    "Example_Executions_with_out_reqd_columns.csv"
)


@pytest.fixture()
def expected_result():
    return {
        ExecMapKeys.EARLIER_EXECUTION_DATE: {
            "20021965": "2022-09-21T09:57:52.000",
            "19368894": "2022-09-21T05:57:08.000",
        },
        ExecMapKeys.MEAN_PRICE: {
            "20021965": 921.75,
            "19368894": 4399.641006002776,
        },
        ExecMapKeys.TOTAL_QUANTITY: {"20021965": 7.0, "19368894": 2882.0},
        ExecMapKeys.FIRST_EXEC_ID: {"20021965": "921125557", "19368894": "92134181"},
    }


class TestExecutionRecordMapper:
    @pytest.mark.parametrize("csv_file", [TEST_FILE, TEST_FILE_WITHOUT_REQ_COLUMNS])
    def test_fill_by_fill_empty_result(self, csv_file):
        extract_result = ExtractPathResult()
        extract_result.path = csv_file

        task = ExecutionRecordMapper(name="ExecutionRecordMapper")
        params = Params(
            fill_by_fill_flag=True,
        )
        result = task.execute(file_url=extract_result, params=params)
        assert result == {}

    def test_non_fill_by_fill_result(self, expected_result):
        extract_result = ExtractPathResult()
        extract_result.path = TEST_FILE

        task = ExecutionRecordMapper(name="ExecutionRecordMapper")
        params = Params(
            fill_by_fill_flag=False,
        )
        result = task.execute(file_url=extract_result, params=params)
        assert result == expected_result
