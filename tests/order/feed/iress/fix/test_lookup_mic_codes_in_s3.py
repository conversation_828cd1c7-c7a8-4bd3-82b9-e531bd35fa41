from unittest.mock import patch

import pandas as pd
import pytest
from prefect.engine.signals import SKIP
from swarm.conf import Settings

from swarm_tasks.order.feed.iress.fix import lookup_mic_codes_in_s3
from swarm_tasks.order.feed.iress.fix.lookup_mic_codes_in_s3 import CsvColumnNames
from swarm_tasks.order.feed.iress.fix.lookup_mic_codes_in_s3 import LookupMicCodesInS3
from swarm_tasks.order.feed.iress.fix.lookup_mic_codes_in_s3 import Params

# The task reads Settings to get the realm (realm=bucket)
Settings.FLOW_ID = "ebc.uat.steeleye.co:tr-feed-onezero-dna"


@pytest.fixture
def source_frame_empty():
    data = {}
    return pd.DataFrame(data, columns=["__venue__", "__ultimate_venue__"])


@pytest.fixture
def lookup_table_empty():
    data = {}
    return pd.DataFrame(data)


@pytest.fixture
def source_frame_with_values():
    data = {
        "__venue__": ["in1", "in3", "not1", "not2", "IN8", pd.NA],
        "__ultimate_venue__": ["in2", "in3", "not3", pd.NA, "in6", "NOT4"],
    }
    return pd.DataFrame(data)


@pytest.fixture
def lookup_table_with_values():
    data = {
        CsvColumnNames.LOOKUP_COLUMN: [
            "in1",
            "IN2",
            "in3",
            "in4",
            "in5",
            "in6",
            "in7",
            "in8",
            "IN9",
            "in10",
        ],
        CsvColumnNames.LOOKUP_DATA_COLUMN: [
            "out1",
            "out2",
            pd.NA,
            "out3",
            "out4",
            "out5",
            pd.NA,
            "out6",
            "out7",
            "out8",
        ],
    }
    return pd.DataFrame(data)


@pytest.fixture()
def params_fixture() -> Params:
    params = Params(
        **{
            "lookup_file_s3_key": "mapping_tables/order-feed-iress-fix/dummyfile.csv",
            "source_venue_column": "__venue__",
            "source_ultimate_venue_column": "__ultimate_venue__",
            "target_venue_column": "executionDetails.venue",
            "target_ultimate_venue_column": "executionDetails.ultimateVenue",
        }
    )
    return params


class TestLookupMicCodesInS3:
    """Test suite for LookupDataFrame"""

    def test_source_frame_empty(
        self,
        source_frame_empty: pd.DataFrame,
        lookup_table_with_values: pd.DataFrame,
        params_fixture: Params,
    ):
        """Tests the task for the case where the source frame is empty"""
        task = LookupMicCodesInS3(name="lookup_dataframe", params=params_fixture)
        with pytest.raises(SKIP):
            task.execute(source_frame_empty, params_fixture)

    @patch.object(lookup_mic_codes_in_s3, "read_csv_from_s3_download")
    def test_lookup_table_empty(
        self,
        mock_lookup_table: pd.DataFrame,
        source_frame_with_values: pd.DataFrame,
        lookup_table_empty: pd.DataFrame,
        params_fixture: Params,
    ):
        """Tests the task for the case where the lookup table is empty. In this case, the
        values from the source frame for venue and ultimate venue are returned
        """
        task = LookupMicCodesInS3(name="lookup_dataframe", params=params_fixture)
        mock_lookup_table.return_value = pd.DataFrame()
        result = task.execute(
            source_frame=source_frame_with_values,
            params=params_fixture,
        )
        expected_result = pd.DataFrame(
            {
                params_fixture.target_venue_column: [
                    "in1",
                    "in3",
                    "not1",
                    "not2",
                    "IN8",
                    pd.NA,
                ],
                params_fixture.target_ultimate_venue_column: [
                    "in2",
                    "in3",
                    "not3",
                    pd.NA,
                    "in6",
                    "NOT4",
                ],
            }
        )
        assert result.equals(expected_result)

    @patch.object(lookup_mic_codes_in_s3, "read_csv_from_s3_download")
    def test_successful_lookup(
        self,
        mock_lookup_table: pd.DataFrame,
        source_frame_with_values: pd.DataFrame,
        lookup_table_with_values: pd.DataFrame,
        params_fixture: Params,
    ):
        """Tests the task for the case where the lookup table and source frame have values.
        Some values of the source column match the lookup column, and some don't. Some values
        get a value of pd.NA from the lookup_data_column. Also, this checks the case where the
        lookup dataframe has a duplicate value
        """
        task = LookupMicCodesInS3(name="lookup_dataframe", params=params_fixture)
        mock_lookup_table.return_value = lookup_table_with_values
        result = task.execute(
            source_frame=source_frame_with_values,
            params=params_fixture,
        )
        expected_result = pd.DataFrame(
            {
                params_fixture.target_venue_column: [
                    "out1",
                    "in3",
                    "not1",
                    "not2",
                    "out6",
                    pd.NA,
                ],
                params_fixture.target_ultimate_venue_column: [
                    "out2",
                    "in3",
                    "not3",
                    pd.NA,
                    "out5",
                    "NOT4",
                ],
            }
        )
        assert result.equals(expected_result)
