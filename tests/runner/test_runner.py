import os
from pathlib import Path
from typing import Optional

import addict
import pytest
import yaml
from elasticsearch6 import NotFoundError

from swarm.conf import Settings
from swarm.flow.static import FlowEnvVar

os.environ.setdefault(
    FlowEnvVar.SWARM_FLOW_ID, "tenant.dev.steeleye.co:flow-test-bundle-id"
)

from swarm.flow.runner import FlowRunner  # noqa: E402

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
BUNDLE_WITH_HORIZONTAL_SCALING = TEST_FILES_DIR.joinpath(
    "bundle_with_horizontal_scaling.yaml"
)
BUNDLE_WITH_HORIZONTAL_SCALING_INCORRECT_PARAMS = TEST_FILES_DIR.joinpath(
    "bundle_with_horizontal_scaling_incorrect_params.yaml"
)
BUNDLE_WITHOUT_HORIZONTAL_SCALING = TEST_FILES_DIR.joinpath(
    "bundle_without_horizontal_scaling.yaml"
)
BUNDLE_WIHTOUT_CONTROL_FLOW = TEST_FILES_DIR.joinpath(
    "bundle_without_control_flow.yaml"
)


@pytest.fixture()
def client_value():
    client_data = {
        "id": "tenant",
        "stacks": {"tenant.dev.steeleye.co": "test-stack"},
    }
    return client_data


@pytest.fixture()
def es_response() -> dict:
    flow_dict = {
        "_source": {
            "bundle": {
                "_meta": {"id": "flow-test-bundle-id"},
                "id": "flow-test-bundle-id",
                "name": "Fake Bundle",
                "platform": False,
                "image": "fakeImage",
                "infra": [{"name": "tenant-data", "type": "ELASTICSEARCH"}],
                "tasks": [
                    {"name": "Task1", "path": "swarm.task.base:BaseTask"},
                    {
                        "name": "Task2",
                        "path": "swarm.task.base:BaseTask",
                        "upstreamTasks": [{"taskName": "Task1"}],
                    },
                ],
            },
            "realm": "tenant.dev.steeleye.co",
            "taskOverrides": [{"name": "Task1", "params": {}}],
        }
    }
    return flow_dict


class TestFlowRunner:
    def test_runner_with_registry_tenant_flow(self, mocker, es_response, client_value):
        tenant_config = {}
        mocker_client = mocker.patch.object(Settings.registry, "get_client")
        mocker_client.return_value = client_value

        # For the tenant level flow, the flow would be fetch directly (in first attempt)
        mocker_flow = mocker.patch.object(Settings.registry, "es_client_get")
        mocker_flow.return_value = es_response

        with mocker.patch(
            "swarm.flow.runner.FlowRunner._fetch_tenant_config",
            return_value=tenant_config,
        ):
            FlowRunner(flow_id="tenant.dev.steeleye.co:flow-test-bundle-id")

    def test_runner_with_registry_stack_level_flow(
        self, mocker, client_value, es_response
    ):
        tenant_config = {}
        mocker_client = mocker.patch.object(Settings.registry, "get_client")
        mocker_client.return_value = client_value

        # For the stack level flow, the flow would not be found at the tenant level, hence the first NotFoundError
        mocker_flow = mocker.patch.object(Settings.registry, "es_client_get")
        mocker_flow.side_effect = [NotFoundError, es_response]

        with mocker.patch(
            "swarm.flow.runner.FlowRunner._fetch_tenant_config",
            return_value=tenant_config,
        ):
            FlowRunner(flow_id="tenant.dev.steeleye.co:flow-test-bundle-id")

    def test_runner_with_registry_platform_flow(
        self, mocker, client_value, es_response
    ):
        tenant_config = {}
        mocker_client = mocker.patch.object(Settings.registry, "get_client")
        mocker_client.return_value = client_value

        # for platform flows first two times will be NotFoundErorr and when queried with "platform" the flow
        # would be found
        mocker_flow = mocker.patch.object(Settings.registry, "es_client_get")
        mocker_flow.side_effect = [NotFoundError, NotFoundError, es_response]

        with mocker.patch(
            "swarm.flow.runner.FlowRunner._fetch_tenant_config",
            return_value=tenant_config,
        ):
            FlowRunner(flow_id="tenant.dev.steeleye.co:flow-test-bundle-id")

    @pytest.mark.parametrize(
        "bundle_path, max_prefect_tasks",
        [
            (BUNDLE_WITH_HORIZONTAL_SCALING, 5),
            (BUNDLE_WITHOUT_HORIZONTAL_SCALING, None),
            (BUNDLE_WIHTOUT_CONTROL_FLOW, None),
        ],
    )
    def test_get_max_prefect_tasks(
        self, mocker, client_value, es_response, bundle_path, max_prefect_tasks
    ):
        bundle_data = yaml.load(bundle_path.read_text(), Loader=yaml.SafeLoader)
        es_response["_source"]["bundle"] = bundle_data
        local_flow = {"bundle": bundle_data, "realm": "test.dev.steeleye.co"}

        tenant_config = {}
        mocker_client = mocker.patch.object(Settings.registry, "get_client")
        mocker_client.return_value = client_value

        # For the tenant level flow, the flow would be fetch directly (in first attempt)
        mocker_flow = mocker.patch.object(Settings.registry, "es_client_get")
        mocker_flow.return_value = es_response

        mocker_compose_flow = mocker.patch.object(FlowRunner, "compose_flow")
        mocker_compose_flow.return_value = (
            addict.Dict({"config": {"bundle": {"image": " swarm_tasks:4.0.3"}}}),
            None,
            None,
        )
        with mocker.patch(
            "swarm.flow.runner.FlowRunner._fetch_tenant_config",
            return_value=tenant_config,
        ):
            runner = FlowRunner(
                flow_id="tenant.dev.steeleye.co:flow-test-bundle-id",
                local_flow=local_flow,
            )
            output_max_prefect_tasks = runner._get_max_prefect_tasks(
                flow=local_flow, default_worker_for_horizontal_scaling=5
            )
        assert output_max_prefect_tasks == max_prefect_tasks

    @pytest.mark.parametrize(
        "bundle_path, expected_validation_result",
        [
            (BUNDLE_WITH_HORIZONTAL_SCALING, True),
            (BUNDLE_WITH_HORIZONTAL_SCALING_INCORRECT_PARAMS, False),
        ],
    )
    def test_horizontal_scaling_task_param_validation(
        self, mocker, client_value, es_response, bundle_path, expected_validation_result
    ):
        bundle_data = yaml.load(bundle_path.read_text(), Loader=yaml.SafeLoader)
        es_response["_source"]["bundle"] = bundle_data
        local_flow = {"bundle": bundle_data, "realm": "test.dev.steeleye.co"}

        tenant_config = {}
        mocker_client = mocker.patch.object(Settings.registry, "get_client")
        mocker_client.return_value = client_value

        # For the tenant level flow, the flow would be fetch directly (in first attempt)
        mocker_flow = mocker.patch.object(Settings.registry, "es_client_get")
        mocker_flow.return_value = es_response

        mocker_compose_flow = mocker.patch.object(FlowRunner, "compose_flow")
        mocker_compose_flow.return_value = (
            addict.Dict({"config": {"bundle": {"image": " swarm_tasks:4.0.3"}}}),
            None,
            None,
        )
        if expected_validation_result:
            with mocker.patch(
                "swarm.flow.runner.FlowRunner._fetch_tenant_config",
                return_value=tenant_config,
            ):
                runner = FlowRunner(
                    flow_id="tenant.dev.steeleye.co:flow-test-bundle-id",
                    local_flow=local_flow,
                )
                runner._validate_horizontal_task_params(
                    flow=local_flow,
                )
        else:
            with pytest.raises(ValueError):
                with mocker.patch(
                    "swarm.flow.runner.FlowRunner._fetch_tenant_config",
                    return_value=tenant_config,
                ):
                    runner = FlowRunner(
                        flow_id="tenant.dev.steeleye.co:flow-test-bundle-id",
                        local_flow=local_flow,
                    )
                    runner._validate_horizontal_task_params(
                        flow=local_flow,
                    )


class FakeRegistry:
    def get_flow(self, flow_id: str, stack: Optional[str] = None) -> dict:
        flow = {
            "bundle": {
                "id": flow_id.split(":")[1],
                "name": "Fake Bundle",
                "platform": False,
                "image": "fakeImage",
                "infra": [{"name": "tenant-data", "type": "ELASTICSEARCH"}],
                "tasks": [
                    {"name": "Task1", "path": "swarm.task.base:BaseTask"},
                    {
                        "name": "Task2",
                        "path": "swarm.task.base:BaseTask",
                        "upstreamTasks": [{"taskName": "Task1"}],
                    },
                ],
            },
            "realm": "tenant.dev.steeleye.co",
            "taskOverrides": [{"name": "Task1", "params": {}}],
        }
        return flow
