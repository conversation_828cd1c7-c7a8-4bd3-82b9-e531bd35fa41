import pytest as pytest
from freezegun import freeze_time

from swarm.flow.flow_meta import FlowMeta
from swarm.utilities.batch import SeBatchJobSubmitter


@pytest.fixture()
def flow_meta_with_file_url():
    flow_meta = FlowMeta(
        file_url="s3://ashwath.dev.steeleye.co/tr-order/abc.csv",
        bundle_id="tr-order",
        flow_args={},
        s3_bucket="ashwath.dev.steeleye.co",
        s3_key="flows/xyz/abc.csv",
        stack="dev-blue",
        flow_id="ashwath.dev.steeleye.co:comms-voice-natterbox",
        flow={"flow": "xyz"},
        client={
            "_meta.id": "ashwath",
            "id": "ashwath",
            "name": "ashwath",
            "stacks": {
                "ashwath.dev.steeleye.co": "dev-blue",
                "ashwath.uat.steeleye.co": "uat-blue",
            },
        },
    )
    return flow_meta


@pytest.fixture()
def flow_meta_with_prefix():
    flow_meta = FlowMeta(
        file_url="s3://ashwath.dev.steeleye.co/tr-order",
        bundle_id="tr-order",
        flow_args={"prefix": "ingress/feed/tr-order"},
        s3_bucket="ashwath.dev.steeleye.co",
        s3_key="tr-order",
        stack="dev-blue",
        flow_id="ashwath.dev.steeleye.co:comms-voice-natterbox",
        flow={"flow": "xyz"},
        client={
            "_meta.id": "ashwath",
            "id": "ashwath",
            "name": "ashwath",
            "stacks": {
                "ashwath.dev.steeleye.co": "dev-blue",
                "ashwath.uat.steeleye.co": "uat-blue",
            },
        },
    )
    return flow_meta


@pytest.fixture()
def flow_meta_with_prefix_and_delta():
    flow_meta = FlowMeta(
        file_url="s3://ashwath.dev.steeleye.co/tr-order",
        bundle_id="tr-order",
        flow_args={"prefix": "ingress/feed/tr-order", "delta_in_days": 2},
        s3_bucket="ashwath.dev.steeleye.co",
        s3_key="tr-order",
        stack="dev-blue",
        flow_id="ashwath.dev.steeleye.co:comms-voice-natterbox",
        flow={"flow": "xyz"},
        client={
            "_meta.id": "ashwath",
            "id": "ashwath",
            "name": "ashwath",
            "stacks": {
                "ashwath.dev.steeleye.co": "dev-blue",
                "ashwath.uat.steeleye.co": "uat-blue",
            },
        },
    )
    return flow_meta


@pytest.fixture()
def flow_meta_without_prefix_and_delta():
    flow_meta = FlowMeta(
        file_url="s3://ashwath.dev.steeleye.co/tr-order",
        bundle_id="tr-order",
        flow_args={"config_s3_key": "config/dubber/config.json"},
        s3_bucket="ashwath.dev.steeleye.co",
        s3_key="tr-order",
        stack="dev-blue",
        flow_id="ashwath.dev.steeleye.co:comms-voice-natterbox",
        flow={"flow": "xyz"},
        client={
            "_meta.id": "ashwath",
            "id": "ashwath",
            "name": "ashwath",
            "stacks": {
                "ashwath.dev.steeleye.co": "dev-blue",
                "ashwath.uat.steeleye.co": "uat-blue",
            },
        },
    )
    return flow_meta


class TestSeBatchJobSubmitter:
    def test_get_audit_key_with_prefix(self, flow_meta_with_prefix: FlowMeta):
        result = SeBatchJobSubmitter._get_audit_key(flow_meta=flow_meta_with_prefix)
        assert result == "ingress/feed/tr-order"

    @freeze_time(time_to_freeze="2023-07-27T13:09:33.430930Z")
    def test_get_audit_key_with_prefix_and_delta(
        self, flow_meta_with_prefix_and_delta: FlowMeta
    ):
        result = SeBatchJobSubmitter._get_audit_key(
            flow_meta=flow_meta_with_prefix_and_delta
        )
        assert result == "ingress/feed/tr-order/20230725"

    def test_get_audit_key_without_prefix_and_delta(
        self, flow_meta_without_prefix_and_delta: FlowMeta
    ):
        result = SeBatchJobSubmitter._get_audit_key(
            flow_meta=flow_meta_without_prefix_and_delta
        )
        assert result == "tr-order"

    def test_get_audit_key_with_file_url(self, flow_meta_with_file_url: FlowMeta):
        result = SeBatchJobSubmitter._get_audit_key(flow_meta=flow_meta_with_file_url)
        assert result == "tr-order/abc.csv"
