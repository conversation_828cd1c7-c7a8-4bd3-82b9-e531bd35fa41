from pathlib import Path

import pandas as pd
import pytest
from prefect.engine import signals
from se_core_tasks.core.core_dataclasses import ExtractPathResult

from swarm_tasks.steeleye.reference.instrument.data_source.eurex.xml_to_dataframe import (
    Params,
)
from swarm_tasks.steeleye.reference.instrument.data_source.eurex.xml_to_dataframe import (
    XMLToDataframe,
)

SCRIPT_PATH = Path(__file__).parent

FILE_PATH = ExtractPathResult(path=SCRIPT_PATH.joinpath("data/sample_eurex_xml.xml"))

EMPTY_FILE_PATH = ExtractPathResult(path=SCRIPT_PATH.joinpath(r"data/empty-file.xml"))


@pytest.fixture()
def expected_dataframe_from_xml() -> pd.DataFrame:
    dict_val = {
        "@MktSegID": {
            0: "351",
            1: "171770",
            2: "171778",
            3: "171769",
            4: "171766",
            5: "171768",
            6: "171767",
            7: "171765",
            8: "171777",
            9: "171776",
            10: "171775",
            11: "171774",
            12: "171773",
            13: "171772",
            14: "171771",
        },
        "@Ccy": {
            0: "CHF",
            1: "EUR",
            2: "EUR",
            3: "EUR",
            4: "EUR",
            5: "EUR",
            6: "EUR",
            7: "EUR",
            8: "EUR",
            9: "EUR",
            10: "EUR",
            11: "EUR",
            12: "EUR",
            13: "EUR",
            14: "EUR",
        },
    }

    df = pd.DataFrame(dict_val)
    return df


class TestPreProcessEurexInstrument(object):
    """
    Test cases for "TestPreProcessEurexInstrument" class
    """

    def test_xml_to_dataframe_convert(self, expected_dataframe_from_xml: pd.DataFrame):
        params = Params(data_path="FIXML.Batch.MktDef")
        mapping = XMLToDataframe(name="test-xml-file-splitter", params=params)
        outcome = mapping.execute(extractor_result=FILE_PATH, params=params)
        assert outcome.equals(expected_dataframe_from_xml)

    def test_empty_file(self):
        params = Params(data_path="FIXML.Batch.MktDef")
        mapping = XMLToDataframe(name="test-xml-file-splitter", params=params)
        with pytest.raises(signals.SKIP):
            mapping.execute(extractor_result=EMPTY_FILE_PATH, params=params)
