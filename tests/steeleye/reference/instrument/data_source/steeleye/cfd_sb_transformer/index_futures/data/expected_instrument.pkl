��
      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK*��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�
cfiAttribute1��
cfiAttribute2��__cfd.cfiAttribute3��__sb.cfiAttribute3��
cfiAttribute4��cfiCategory��cfiGroup��+commoditiesOrEmissionAllowanceDerivativeInd��__cfd.derivative.deliveryType��derivative.priceMultiplier��Ederivative.underlyingInstruments.0.underlyingInstrumentClassification��;derivative.underlyingInstruments.0.underlyingInstrumentCode��)__cfd.ext.alternativeInstrumentIdentifier��(__sb.ext.alternativeInstrumentIdentifier��ext.bestExAssetClassMain��ext.bestExAssetClassSub��ext.emirEligible��ext.instrumentIdCodeType��$__cfd.ext.instrumentUniqueIdentifier��#__sb.ext.instrumentUniqueIdentifier��ext.priceNotation��ext.pricingReferences.RIC��ext.quantityNotation��1ext.underlyingInstruments.0.derivative.expiryDate��,ext.underlyingInstruments.0.instrumentIdCode��.ext.underlyingInstruments.0.venue.tradingVenue��__cfd.instrumentClassification��__sb.instrumentClassification��&instrumentClassificationEMIRAssetClass��.__cfd.instrumentClassificationEMIRContractType��-__sb.instrumentClassificationEMIRContractType��-__cfd.instrumentClassificationEMIRProductType��,__sb.instrumentClassificationEMIRProductType��__cfd.instrumentFullName��__sb.instrumentFullName��__meta_model__��notionalCurrency1��__cfd.sourceKey��__sb.sourceKey��(__cfd.venue.financialInstrumentShortName��'__sb.venue.financialInstrumentShortName��venue.tradingVenue�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C        �t�bhP�__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KK	K��h!�]�(�Futures��Not Applicable/Undefined��Contract for difference��
Spread-bet��Cash��Forwards��Equity��False��CASH�et�bhhK ��h��R�(KKK��h]�C       �t�bhhK ��h��R�(KKK��h!�]�(�FFICSX��DE000C6A0Z70��XXXXDE000C6A0Z70CFD��XXXXDE000C6A0Z70SB��Equity Derivatives��"Swaps and other equity derivatives�et�bhhK ��h��R�(KKK��h�b1�����R�(Kh"NNNJ����J����K t�b�C�t�bhhK ��h��R�(KKK��h!�]�(�OTHR��XXXXDE000C6A0Z70KRWCFD��XXXXDE000C6A0Z70KRWSB��MONE��pandas._libs.missing��NA����UNIT��
2021-10-14�h��XEUR��JEFXCC��JEFXSC��EQ��CD��SB��CFD��
EquityForward��FMK2 SI 20211014 CS CFD��FMK2 SI 20211014 CS SB��SteelEyeInstrument��KRW��DLTINS_20211016_01of03.xml_cfd��DLTINS_20211016_01of03.xml_sb��Eurex/F 20211014 KMS2 CFD��Eurex/F 20211014 KMS2 SB�et�bhhK ��h��R�(KKK��h!�]��XXXX�at�be]�(h
h}�(hhhK ��h��R�(KK	��h!�]�(h%h&h'h(h)h*h+h,h-et�bhPNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bhPNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h/h0h1h2h3h4et�bhPNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h5at�bhPNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h6h7h8h9h:h;h<h=h>h?h@hAhBhChDhEhFhGhHhIhJhKhLhMet�bhPNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hNat�bhPNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hh�mgr_locs��builtins��slice���K K	K��R�u}�(h�hwh�h�K	K
K��R�u}�(h�h}h�h�K
KK��R�u}�(h�h�h�h�KKK��R�u}�(h�h�h�h�KK)K��R�u}�(h�h�h�h�K)K*K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.