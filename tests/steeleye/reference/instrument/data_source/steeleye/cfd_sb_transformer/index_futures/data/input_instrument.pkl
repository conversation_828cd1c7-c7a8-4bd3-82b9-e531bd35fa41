���
      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK1��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�	sourceKey��&instrumentClassificationEMIRAssetClass��&id��cfiGroup��instrumentIdCode��cfiCategory��+commoditiesOrEmissionAllowanceDerivativeInd��instrumentFullName��&key��notionalCurrency1��&model��'instrumentClassificationEMIRProductType��(instrumentClassificationEMIRContractType�� issuerOrOperatorOfTradingVenueId��
cfiAttribute1��
cfiAttribute2��
cfiAttribute3��
cfiAttribute4��
&timestamp��instrumentClassification��&user��ext.aii.mic��
ext.aii.daily��ext.mifirEligible��ext.pricingReferences.ICE��ext.bestExAssetClassSub��ext.emirEligible��ext.bestExAssetClassMain��ext.venueInEEA��#ext.alternativeInstrumentIdentifier��ext.instrumentUniqueIdentifier��ext.onFIRDS��ext.instrumentIdCodeType��(venue.admissionToTradingOrFirstTradeDate��venue.terminationDate��venue.tradingVenue��(venue.issuerRequestForAdmissionToTrading��"venue.financialInstrumentShortName��derivative.expiryDate��derivative.underlyingIndexName�� derivative.underlyingInstruments��derivative.deliveryType��derivative.priceMultiplier��ext.pricingReferences.RIC��	_meta.key��_meta.timestamp��_meta.model��_meta.id��
_meta.user�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C        �t�bhW�__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KK+K��h!�]�(�DLTINS_20211016_01of03.xml��EQ��DE000C6A0Z70KRWXEUR��Financial futures��DE000C6A0Z70��Futures��False��FMK2 SI 20211014 CS��1FirdsInstrument:DE000C6A0Z70KRWXEUR:1634384865000��KRW��FirdsInstrument��FUT��FU��529900UT4DG0LG5R9O07��Indices��Cash��Standardized��Not Applicable/Undefined��
1634384865000��FFICSX��spider-firds-task��XEUR��XEURFMK2FF2021-10-14 00:00:00��True��isin/DE000C6A0Z70/KRW��:Futures and options admitted to trading on a trading venue��True��Equity Derivatives��True��XEURFMK2FF2021-10 00:00:00��DE000C6A0Z70KRWXEUR��True��ID��2021-04-09T00:50:00��2021-10-14T23:59:59��XEUR��False��Eurex/F 20211014 KMS2��
2021-10-14��MINI KOSPI 200 INDEX��.[{'underlyingInstrumentCode': 'XC000A2DB695'}]��CASH��50000.0�et�b�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h!�]��pandas._libs.missing��NA���at�b�_dtype�h��StringDtype���)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�h�at�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�h�at�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�h�at�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�h�at�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�h�at�bh�h�)��ube]�(h
h}�(hhhK ��h��R�(KK+��h!�]�(h%h&h'h(h)h*h+h,h-h.h/h0h1h2h3h4h5h6h7h8h9h:h;h<h=h>h?h@hAhBhChDhEhFhGhHhIhJhKhLhMhNhOet�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hPat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hQat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hRat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hSat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hTat�bhWNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hUat�bhWNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�ho�mgr_locs��builtins��slice���K K+K��R�u}�(j%  h�j&  j)  K+K,K��R�u}�(j%  h�j&  j)  K,K-K��R�u}�(j%  h�j&  j)  K-K.K��R�u}�(j%  h�j&  j)  K.K/K��R�u}�(j%  h�j&  j)  K/K0K��R�u}�(j%  h�j&  j)  K0K1K��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.