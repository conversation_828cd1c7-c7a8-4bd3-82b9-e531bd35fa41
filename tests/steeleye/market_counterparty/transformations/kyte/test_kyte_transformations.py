import pandas as pd
from prefect import context

from swarm_tasks.steeleye.market_counterparty.transformations.kyte.kyte_transformations import (
    KyteTransformations,
)
from swarm_tasks.steeleye.mymarket.firm.utils import UpdateColumns


class TestKyteTransformations:

    """
    Integration Test Suite for KyteTransformations
    """

    def test_end_to_end_transformation(
        self,
        auditor,
        source_frame,
        expected_result,
    ):
        """Runs an end-to-end test for Kyte MarketCounterparty."""
        task = KyteTransformations(
            source_frame=source_frame,
            logger=context.get("logger"),
            auditor=auditor,
        )
        result = task.process()

        pd.testing.assert_frame_equal(result, expected_result)

    def test_end_to_end_transformation_update_record(
        self,
        auditor,
        source_frame,
        expected_result_update,
    ):
        """Runs an end-to-end test for Kyte MarketCounterparty using the logic to update records."""
        source_frame.loc[:, UpdateColumns.TO_UPDATE] = True

        task = KyteTransformations(
            source_frame=source_frame,
            logger=context.get("logger"),
            auditor=auditor,
        )
        result = task.process()

        pd.testing.assert_frame_equal(result, expected_result_update)
