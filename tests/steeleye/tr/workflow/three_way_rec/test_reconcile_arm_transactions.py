import pandas as pd
from addict import addict
from addict import Dict

from swarm_tasks.steeleye.tr.static import RecFields
from swarm_tasks.steeleye.tr.static import RTS22Transaction
from swarm_tasks.steeleye.tr.workflow.three_way_rec.arm_unavista_report_mechanism_reconciliation import (
    ArmUnavistaReportMechanismReconciliation,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.reconcile_report_transactions import (
    Params as ReconRptTrParams,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.reconcile_report_transactions import (
    ReconcileReportTransactions,
)
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.enums import ReportTypeEnum
from swarm_tasks.steeleye.tr.workflow.three_way_rec.utils.report_mechanism_mappings import (
    REPORT_MECHANISM_MAP,
)


class TestReconcileARMTransactions:
    def test_transaction_details_quantity_notation(
        self,
        se_transaction_details_quantity_notation_df: pd.DataFrame,
        arm_transaction_details_quantity_notation_df: pd.DataFrame,
    ):
        df = pd.concat(
            [
                se_transaction_details_quantity_notation_df,
                arm_transaction_details_quantity_notation_df,
            ],
            axis=1,
        )
        target = pd.DataFrame(index=df.index)
        target.loc[:, RecFields.FIELD_BREAKS] = pd.NA
        target.loc[:, RecFields.RECONCILIATION_ARM_FIELD_BREAK] = False

        task = ArmUnavistaReportMechanismReconciliation()
        task.reconcile_custom_general_fields(
            data_to_reconcile=df,
            target=target,
            tenant_configuration=addict.Dict({"trPIEnrichmentEnabled": False}),
        )

        na_breaks = list(target.loc[1:5, "reconciliation.fieldBreaks"].values)
        fields_not_breaking = set(
            [
                [list_item["field"] for list_item in x][0]
                for x in na_breaks
                if not pd.isna(x)
            ]
        )

        assert (
            RTS22Transaction.TRANSACTION_DETAILS_QUANTITY_NOTATION
            not in fields_not_breaking
        )

        breaks = target.loc[0, "reconciliation.fieldBreaks"][0]
        assert breaks[
            "field"
        ] == RTS22Transaction.TRANSACTION_DETAILS_QUANTITY_NOTATION and breaks[
            "value"
        ] == {
            "arm": "UNIT_VAL",
            "se": "UNIT",
        }

    def test_transaction_details_strike_price_type(
        self,
        se_transaction_details_strike_price_df: pd.DataFrame,
        arm_transaction_details_strike_price_type_df: pd.DataFrame,
    ):
        df = pd.concat(
            [
                se_transaction_details_strike_price_df,
                arm_transaction_details_strike_price_type_df,
            ],
            axis=1,
        )
        target = pd.DataFrame(index=df.index)
        target.loc[:, RecFields.FIELD_BREAKS] = pd.NA
        target.loc[:, RecFields.RECONCILIATION_ARM_FIELD_BREAK] = False

        task = ArmUnavistaReportMechanismReconciliation()
        task.reconcile_custom_general_fields(
            data_to_reconcile=df,
            target=target,
            tenant_configuration=addict.Dict({"trPIEnrichmentEnabled": False}),
        )

        na_breaks = list(target.loc[1:5, "reconciliation.fieldBreaks"].values)
        fields_not_breaking = set(
            [
                [list_item["field"] for list_item in x][0]
                for x in na_breaks
                if not pd.isna(x)
            ]
        )

        assert (
            RTS22Transaction.TRANSACTION_DETAILS_QUANTITY_NOTATION
            not in fields_not_breaking
        )

        breaks = target.loc[0, "reconciliation.fieldBreaks"][0]
        assert breaks[
            "field"
        ] == RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_EXT_STRIKE_PRICE_TYPE and breaks[
            "value"
        ] == {
            "arm": "PCTG",
            "se": "Yld",
        }

    def test_transaction_details_price_notation(
        self,
        se_transaction_details_price_notation_df: pd.DataFrame,
        arm_transaction_details_price_notation_df: pd.DataFrame,
    ):
        df = pd.concat(
            [
                se_transaction_details_price_notation_df,
                arm_transaction_details_price_notation_df,
            ],
            axis=1,
        )
        target = pd.DataFrame(index=df.index)
        target.loc[:, RecFields.FIELD_BREAKS] = pd.NA
        target.loc[:, RecFields.RECONCILIATION_ARM_FIELD_BREAK] = False

        task = ArmUnavistaReportMechanismReconciliation()
        task.reconcile_custom_general_fields(
            data_to_reconcile=df,
            target=target,
            tenant_configuration=addict.Dict({"trPIEnrichmentEnabled": False}),
        )

        na_breaks = list(target.loc[1:5, "reconciliation.fieldBreaks"].values)
        fields_not_breaking = set(
            [
                [list_item["field"] for list_item in x][0]
                for x in na_breaks
                if not pd.isna(x)
            ]
        )

        assert (
            RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOTATION
            not in fields_not_breaking
        )

        breaks = target.loc[0, "reconciliation.fieldBreaks"][0]
        assert breaks[
            "field"
        ] == RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOTATION and breaks[
            "value"
        ] == {
            "arm": "NOT_POPULATED",
            "se": "MONE",
        }

    def test_transaction_details_price_pending_not_applicable(
        self,
        se_transaction_details_price_pending_df: pd.DataFrame,
        arm_transaction_details_price_pending_df: pd.DataFrame,
    ):
        df = pd.concat(
            [
                se_transaction_details_price_pending_df,
                arm_transaction_details_price_pending_df,
            ],
            axis=1,
        )
        target = pd.DataFrame(index=df.index)
        target.loc[:, RecFields.FIELD_BREAKS] = pd.NA
        target.loc[:, RecFields.RECONCILIATION_ARM_FIELD_BREAK] = False

        task = ArmUnavistaReportMechanismReconciliation()
        task.reconcile_custom_general_fields(
            data_to_reconcile=df,
            target=target,
            tenant_configuration=addict.Dict({"trPIEnrichmentEnabled": False}),
        )

        na_breaks = list(target.loc[:3, "reconciliation.fieldBreaks"].values)
        fields_not_breaking = set(
            [
                [list_item["field"] for list_item in x][0]
                for x in na_breaks
                if not pd.isna(x)
            ]
        )

        assert (
            RTS22Transaction.TRANSACTION_DETAILS_PRICE_PENDING
            not in fields_not_breaking
        )
        assert (
            RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOT_APPLICABLE
            not in fields_not_breaking
        )

        breaks = list(target.loc[4, "reconciliation.fieldBreaks"])
        fields_breaking = set([list_item["field"] for list_item in breaks])

        assert RTS22Transaction.TRANSACTION_DETAILS_PRICE_PENDING in fields_breaking
        assert (
            RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOT_APPLICABLE in fields_breaking
        )

        assert breaks[0][
            "field"
        ] == RTS22Transaction.TRANSACTION_DETAILS_PRICE_PENDING and breaks[0][
            "value"
        ] == {
            "arm": "false",
            "se": "true",
        }

        assert breaks[1][
            "field"
        ] == RTS22Transaction.TRANSACTION_DETAILS_PRICE_NOT_APPLICABLE and breaks[1][
            "value"
        ] == {
            "arm": "true",
            "se": "false",
        }

    def test_seller_id(
        self,
        se_parties_seller_id: pd.DataFrame,
        arm_seller_id: pd.DataFrame,
    ):
        df = pd.concat(
            [se_parties_seller_id, arm_seller_id],
            axis=1,
        )
        target = pd.DataFrame(index=df.index)
        target.loc[:, RecFields.FIELD_BREAKS] = pd.NA

        task = ArmUnavistaReportMechanismReconciliation()
        task.reconcile_list_party_fields_with_model_variations(
            data_to_reconcile=df,
            es=addict.Dict({"meta": {"key": "&key"}}),
            target=target,
        )

        # Assert that for the test cases there are no field breaks
        assert target.dropna().empty

    def test_arm_dates_only_field_break(self):
        """
        When only a datetime field break is found, it is dropped.
        In such cases, the fieldbreak indicator must be set to False.
        :return:
        """
        se_series = pd.Series(["2021-06-30T00:02:01Z"])
        report_series = pd.Series(["2021-06-30T00:02:01.000000Z"])
        steeleye_field_names = pd.Series(
            ["transactionDetails.tradingDateTime|Trading Date Time"]
        )
        df = pd.concat(
            [se_series, report_series],
            axis=1,
        )
        target = pd.DataFrame(index=df.index)
        target.loc[:, RecFields.FIELD_BREAKS] = pd.NA
        task = ArmUnavistaReportMechanismReconciliation()
        task.compare_report_and_steeleye_fields(
            report_series=report_series,
            steeleye_series=se_series,
            target=target,
            steeleye_field_names=steeleye_field_names,
        )
        assert target[RecFields.RECONCILIATION_ARM_FIELD_BREAK][0] is False

    def test_firm_execution_id(
        self,
        se_firm_execution_id_df: pd.DataFrame,
        arm_firm_execution_id_df: pd.DataFrame,
        tenant_config_w_tr_pi_enrichment_enabled_false: Dict,
    ):
        df = pd.concat(
            [se_firm_execution_id_df, arm_firm_execution_id_df],
            axis=1,
        )
        target = pd.DataFrame(index=df.index)
        target.loc[:, RecFields.FIELD_BREAKS] = pd.NA

        task = ArmUnavistaReportMechanismReconciliation()
        task.reconcile_compound_party_fields(
            data_to_reconcile=df,
            target=target,
            tenant_configuration=tenant_config_w_tr_pi_enrichment_enabled_false,
        )

        breaks = list(target.loc[:, "reconciliation.fieldBreaks"].values)

        fields_breaking = set(
            [
                [list_item["field"] for list_item in x][0]
                for x in breaks
                if not pd.isna(x)
            ]
        )

        # Assert first row of df does not have field breaks, and the other rows do
        assert breaks.count(pd.NA) == 1
        assert breaks[0] is pd.NA

        assert (
            RTS22Transaction.MARKET_IDENTIFIERS_PARTIES_EXECUTION_WITHIN_FIRM
            and RTS22Transaction.PARTIES_EXECUTION_WITHIN_FIRM_MIFIR_ID
            in fields_breaking
        )

    def test_ul_index_name(
        self,
        se_ul_index_name_df: pd.DataFrame,
        arm_ul_index_name_df: pd.DataFrame,
        tenant_config_w_tr_pi_enrichment_enabled_false: Dict,
    ):
        df = pd.concat(
            [se_ul_index_name_df, arm_ul_index_name_df],
            axis=1,
        )
        target = pd.DataFrame(index=df.index)
        target.loc[:, RecFields.FIELD_BREAKS] = pd.NA

        task = ReconcileReportTransactions(
            name="ReconcileReportTransactions",
            params=ReconRptTrParams(
                **{"report_type": ReportTypeEnum.ARM_UNAVISTA.value}
            ),
        )
        task.report_mechanism_reconciliation = REPORT_MECHANISM_MAP[
            ReportTypeEnum.ARM_UNAVISTA.value
        ]()
        task._reconcile_underlying_instruments_mapping_fields(
            data_to_reconcile=df,
            target=target,
        )

        breaks = list(target.loc[:, "reconciliation.fieldBreaks"].values)

        fields_breaking = set(
            [
                [list_item["field"] for list_item in x][0]
                for x in breaks
                if not pd.isna(x)
            ]
        )

        assert breaks.count(pd.NA) == 2
        assert breaks[0] is pd.NA
        assert breaks[1] is pd.NA

        assert (
            RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_DERIVATIVE_UL_INDEX_NAME
            and f"{RTS22Transaction.INSTRUMENT_DETAILS_INSTRUMENT_EXT_UL_INSTRUMENTS}[].{RTS22Transaction.DERIVATIVE_UDERLYING_INDEX_NAME}"
            in fields_breaking
        )

    def test_ul_inst_partial(
        self,
        se_ul_inst_partial_df: pd.DataFrame,
        arm_ul_index_name_empty_df: pd.DataFrame,
        tenant_config_w_tr_pi_enrichment_enabled_false: Dict,
    ):
        df = pd.concat(
            [se_ul_inst_partial_df, arm_ul_index_name_empty_df],
            axis=1,
        )
        target = pd.DataFrame(index=df.index)
        target.loc[:, RecFields.FIELD_BREAKS] = pd.NA

        task = ReconcileReportTransactions(
            name="ReconcileReportTransactions",
            params=ReconRptTrParams(
                **{"report_type": ReportTypeEnum.ARM_UNAVISTA.value}
            ),
        )
        task.report_mechanism_reconciliation = REPORT_MECHANISM_MAP[
            ReportTypeEnum.ARM_UNAVISTA.value
        ]()
        task._reconcile_underlying_instruments_mapping_fields(
            data_to_reconcile=df,
            target=target,
        )

        breaks = list(target.loc[:, "reconciliation.fieldBreaks"].values)

        fields_breaking = set(
            [
                [list_item["field"] for list_item in x][0]
                for x in breaks
                if not pd.isna(x)
            ]
        )

        assert breaks.count(pd.NA) == 4
        assert breaks[0] is pd.NA
        assert breaks[1] is pd.NA
        assert breaks[2] is pd.NA
        assert breaks[3] is pd.NA

        assert fields_breaking == set()

    def test_ul_inst_name_partial(
        self,
        se_ul_inst_name_partial_df: pd.DataFrame,
        arm_ul_index_name_empty_df: pd.DataFrame,
        tenant_config_w_tr_pi_enrichment_enabled_false: Dict,
    ):
        df = pd.concat(
            [se_ul_inst_name_partial_df, arm_ul_index_name_empty_df],
            axis=1,
        )
        target = pd.DataFrame(index=df.index)
        target.loc[:, RecFields.FIELD_BREAKS] = pd.NA

        task = ReconcileReportTransactions(
            name="ReconcileReportTransactions",
            params=ReconRptTrParams(
                **{"report_type": ReportTypeEnum.ARM_UNAVISTA.value}
            ),
        )
        task.report_mechanism_reconciliation = REPORT_MECHANISM_MAP[
            ReportTypeEnum.ARM_UNAVISTA.value
        ]()
        task._reconcile_underlying_instruments_mapping_fields(
            data_to_reconcile=df,
            target=target,
        )

        breaks = list(target.loc[:, "reconciliation.fieldBreaks"].values)

        fields_breaking = set(
            [
                [list_item["field"] for list_item in x][0]
                for x in breaks
                if not pd.isna(x)
            ]
        )

        assert breaks.count(pd.NA) == 4
        assert breaks[0] is pd.NA
        assert breaks[1] is pd.NA
        assert breaks[2] is pd.NA
        assert breaks[3] is pd.NA

        assert fields_breaking == set()
