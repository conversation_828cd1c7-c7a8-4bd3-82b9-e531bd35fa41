import pandas as pd
import pytest

from swarm_tasks.steeleye.tr.workflow.three_way_rec.arm_unavista_report_mechanism_reconciliation import (
    ArmUnavistaReportMechanismReconciliation,
)


@pytest.fixture
def report_series_sample_for_waiver_indicator() -> pd.Series:
    return pd.Series(["true", False, True, 5.55000, "5.55", "2", 3, ["ILQD"], pd.NA])


class TestArmUnavistaReportMechanismReconciliation:
    def test_transaction_details_quantity_notation(
        self,
        arm_transaction_details_quantity_notation_df: pd.DataFrame,
        report_series_sample_for_waiver_indicator: pd.Series,
    ):
        self.report_series = report_series_sample_for_waiver_indicator.copy()
        task = ArmUnavistaReportMechanismReconciliation()
        task._sanitize_report_series(report_series=self.report_series)
        expected_result = pd.Series(
            ["true", "false", "true", "5.55", "5.55", "2", "3", ["ILQD"], pd.NA]
        )
        assert self.report_series.compare(expected_result).empty

    def test_validate_strike_price_type(
        self,
        se_validate_strike_price_type_custom_mapping_series: pd.Series,
        se_validate_strike_price_type_series: pd.Series,
    ):
        task = ArmUnavistaReportMechanismReconciliation()
        task.validate_strike_price_type(
            custom_mapping_result_series=se_validate_strike_price_type_custom_mapping_series,
            steeleye_series=se_validate_strike_price_type_series,
        )
        assert pd.isna(se_validate_strike_price_type_series[2])
        assert pd.isna(se_validate_strike_price_type_series[3])
