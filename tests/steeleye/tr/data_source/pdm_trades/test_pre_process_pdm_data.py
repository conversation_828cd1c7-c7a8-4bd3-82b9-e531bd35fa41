import pandas as pd
import pytest
from prefect.engine.signals import SKIP

from swarm_tasks.steeleye.tr.data_source.pdm_trades.pre_process_pdm_data import Params
from swarm_tasks.steeleye.tr.data_source.pdm_trades.pre_process_pdm_data import (
    PreProcessPDMData,
)


@pytest.fixture()
def empty_source_df() -> pd.DataFrame:
    """
    creates empty dataframe
    """
    df = pd.DataFrame()
    return df


@pytest.fixture()
def all_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "Trade_SettleDate": [
                "2021-01-01",
                "2021-04-01",
                "2021-05-01",
                "2021-01-01",
                "2021-01-01",
                "2021-01-01",
                pd.NA,
            ],
            "Trade_TradeGroup_ID": ["1", "1", "2", "2", "3", "4", pd.NA],
            "Position_ID": ["1", "2", "3", "4", "5", "6", pd.NA],
            "TotalAmount": [30, 40, 50, 50, 10, 10, pd.NA],
            "Trade_ID": ["1", "2", "3", "4", "5", "6", pd.NA],
            "Trade_ParAmount": [10, 10, 20, 20, 30, 30, pd.NA],
            "TradeGroup_mifirtimestamp": [
                "2021-01-01",
                "2021-04-01",
                "2021-05-01",
                "2021-01-01",
                "2021-01-01",
                "2021-01-01",
                pd.NA,
            ],
            "Asset_Name": [
                "name_1",
                "name_2",
                "name_3",
                "name_4",
                "name_5",
                "name_6",
                pd.NA,
            ],
            "Asset_Rate1": [1, 2, 3, 4, 5, 6, pd.NA],
        }
    )
    return df


@pytest.fixture()
def missing_some_col_in_source_df() -> pd.DataFrame:
    df = pd.DataFrame(
        {
            "Trade_SettleDate": [
                "2021-01-01",
                "2021-04-01",
                "2021-05-01",
                "2021-01-01",
                "2021-01-01",
                "2021-01-01",
                pd.NA,
            ],
            "Position_ID": ["1", "2", "3", "4", "5", "6", pd.NA],
            "TotalAmount": [30, 40, 50, 50, 10, 10, pd.NA],
            "Trade_ID": ["1", "2", "3", "4", "5", "6", pd.NA],
            "Trade_ParAmount": [10, 10, 20, 20, 30, 30, pd.NA],
            "TradeGroup_mifirtimestamp": [
                "2021-01-01",
                "2021-04-01",
                "2021-05-01",
                "2021-01-01",
                "2021-01-01",
                "2021-01-01",
                pd.NA,
            ],
            "Asset_Name": [
                "name_1",
                "name_2",
                "name_3",
                "name_4",
                "name_5",
                "name_6",
                pd.NA,
            ],
            "Asset_Rate1": [1, 2, 3, 4, 5, 6, pd.NA],
        }
    )
    return df


@pytest.fixture()
def input_params() -> Params:
    params = Params(
        group_by_columns=["Trade_TradeGroup_ID"],
        group_by_aggregate_dict={
            "Trade_TradeGroup_ID": "first",
            "Trade_SettleDate": "first",
            "Position_ID": "first",
            "TotalAmount": "sum",
            "Trade_ID": "first",
            "Trade_ParAmount": "sum",
            "TradeGroup_mifirtimestamp": "first",
            "Asset_Name": "first",
            "Asset_Rate1": "first",
        },
    )
    return params


class TestPreProcessPDMData(object):
    """
    Test cases for "TestPreProcessPDMData" class
    """

    def test_empty_input_df(self, empty_source_df, input_params: Params):
        task = PreProcessPDMData(name="pre_process_pdm_data", params=input_params)
        with pytest.raises(SKIP) as e:
            task.execute(source_frame=empty_source_df, params=input_params)
        assert e.type == SKIP

    def test_all_col_in_source_df(
        self, all_col_in_source_df: pd.DataFrame, input_params: Params
    ):
        task = PreProcessPDMData(name="pre_process_pdm_data", params=input_params)
        result = task.execute(source_frame=all_col_in_source_df, params=input_params)
        expected_result = pd.DataFrame(
            {
                "Trade_TradeGroup_ID": ["1", "2", "3", "4"],
                "Trade_SettleDate": [
                    "2021-01-01",
                    "2021-05-01",
                    "2021-01-01",
                    "2021-01-01",
                ],
                "Position_ID": ["1", "3", "5", "6"],
                "TotalAmount": [70.00000, 100.00000, 10.00000, 10.00000],
                "Trade_ID": ["1", "3", "5", "6"],
                "Trade_ParAmount": [20.00000, 40.00000, 30.00000, 30.00000],
                "TradeGroup_mifirtimestamp": [
                    "2021-01-01",
                    "2021-05-01",
                    "2021-01-01",
                    "2021-01-01",
                ],
                "Asset_Name": ["name_1", "name_3", "name_5", "name_6"],
                "Asset_Rate1": [1, 3, 5, 6],
            }
        )
        assert result.reset_index(drop=True).equals(
            expected_result.reset_index(drop=True)
        )

    def test_missing_some_col_in_source_df(
        self, missing_some_col_in_source_df, input_params: Params
    ):
        task = PreProcessPDMData(name="pre_process_pdm_data", params=input_params)
        with pytest.raises(SKIP) as e:
            task.execute(
                source_frame=missing_some_col_in_source_df, params=input_params
            )
        assert e.type == SKIP
