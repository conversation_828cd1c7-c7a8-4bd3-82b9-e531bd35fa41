��Z      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�dataSourceName��date��__meta_model__��reportDetails.reportStatus��sourceIndex��	sourceKey��=tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator��#transactionDetails.buySellIndicator��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��transactionDetails.quantity��#transactionDetails.quantityNotation��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime��transactionDetails.venue�� transactionDetails.ultimateVenue��(transactionDetails.outgoingOrderAddlInfo��.transmissionDetails.orderTransmissionIndicator��reportDetails.transactionRefNo��+reportDetails.tradingVenueTransactionIdCode��marketIdentifiers.instrument��marketIdentifiers.parties��marketIdentifiers�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C(                                    �t�bh>�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�Ps3://puneeth.uat.steeleye.co/temp_dir/tr-feed-lme-controller/SIG_230614_EODM.csv�hchchchcet�b�_dtype�hT�StringDtype���)��ubhV)��}�(hYhhK ��h��R�(KK��h`�]�(�9��3��32��20��10�et�bhehg)��ubhhK ��h��R�(KKK��h�b1�����R�(Kh"NNNJ����J����K t�b�C
          �t�bhhK ��h��R�(KKK��hK�C(                                    �t�bhhK ��h��R�(KKK��h!�]�(�LME�h�h�h�h��
2023-06-14��
2023-06-14��
2023-06-14��
2023-06-14��
2023-06-14��RTS22Transaction�h�h�h�h��NEWT�h�h�h�h��SELL��BUYI�h�h�h��pandas._libs.missing��NA���h�h�h�h��USD�h�h�h�h��MONE�h�h�h�h��UNIT�h�h�h�h��DEAL�h�h�h�h��2023-06-14T10:03:11.844000Z��2023-06-14T00:00:00.000000Z��2023-06-14T14:21:08.895000Z��2023-06-14T00:00:00.000000Z��2023-06-14T15:52:32.134000Z��XLME�h�h�h�h�h�h�h�h�h��%Trade Category: NORMAL, Venue: Select��#Trade Category: NORMAL, Venue: Ring��%Trade Category: NORMAL, Venue: Select��#Trade Category: NORMAL, Venue: Ring��%Trade Category: NORMAL, Venue: Select��103673123421314062023��105393915004614062023��109083328384514062023��105447915096914062023��1121911210996614062023��TANI20230614X0001911��
5004610539391��TANI20230614X0003614��
5096910544791��TANI20230614X0004566�]�(}�(�labelId��XLMENIUSDFF2023-06-15 00:00:00��path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�u}�(h��XLMENIUSDFF2023-06 00:00:00�h�h�h�h�ue]�(}�(h��XLMENIUSDFF2023-06-16 00:00:00�h�h�h�h�u}�(h��XLMENIUSDFF2023-06 00:00:00�h�h�h�h�ue]�(}�(h��XLMENIUSDFF2023-06-16 00:00:00�h�h�h�h�u}�(h��XLMENIUSDFF2023-06 00:00:00�h�h�h�h�ue]�(}�(h��XLMENIUSDFF2023-06-16 00:00:00�h�h�h�h�u}�(h��XLMENIUSDFF2023-06 00:00:00�h�h�h�h�ue]�(}�(h��XLMENIUSDFF2023-06-16 00:00:00�h�h�h�h�u}�(h��XLMENIUSDFF2023-06 00:00:00�h�h�h�h�ue]�(}�(h��lei:875500pnfzenvo437436�h��parties.executingEntity�h�h�u}�(h��id:ad�h��parties.executionWithinFirm�h�h�u}�(h��id:ad�h��$parties.investmentDecisionWithinFirm�h�h�u}�(h��id:xlme�h��
parties.buyer�h�h��ARRAY���R�u}�(h��lei:875500pnfzenvo437436�h��parties.seller�h�h�ue]�(}�(h��lei:875500pnfzenvo437436�h�h�h�h�u}�(h��id:1200�h�h�h�h�u}�(h��id:1200�h�h�h�h�u}�(h��lei:875500pnfzenvo437436�h�h�h�h�u}�(h��id:xlme�h�h�h�h�ue]�(}�(h��lei:875500pnfzenvo437436�h�h�h�h�u}�(h��id:di�h�h�h�h�u}�(h��id:di�h�h�h�h�u}�(h��id:xlme�h�h�h�h�u}�(h��lei:875500pnfzenvo437436�h�h�h�h�ue]�(}�(h��lei:875500pnfzenvo437436�h�h�h�h�u}�(h��id:1200�h�h�h�h�u}�(h��id:1200�h�h�h�h�u}�(h��lei:875500pnfzenvo437436�h�h�h�h�u}�(h��id:xlme�h�h�h�h�ue]�(}�(h��lei:875500pnfzenvo437436�h�h�h�h�u}�(h��id:di�h�h�h�h�u}�(h��id:di�h�h�h�h�u}�(h��id:xlme�h�h�h�h�u}�(h��lei:875500pnfzenvo437436�h�h�h�h�ue]�(h�h�h�h�h�h�h�e]�(h�h�h�h�h�h�h�e]�(h�h�h�h�h�h�h�e]�(h�h�h�j  j  j  j  e]�(h�h�j
  j  j  j  j  eet�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bh>Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h0at�bh>Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h+h7et�bh>Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h)at�bh>Nu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h(h,h-h.h/h1h2h3h4h5h6h8h9h:h;h<et�bh>Nu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hW�mgr_locs��builtins��slice���KKK��R�u}�(jO  hijP  jS  KKK��R�u}�(jO  hyjP  jS  KKK��R�u}�(jO  h�jP  jS  KKK��R�u}�(jO  h�jP  hhK ��h��R�(KK��h�i8�����R�(KhLNNNJ����J����K t�b�C�                                           	       
              
                                                                      �t�bueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.