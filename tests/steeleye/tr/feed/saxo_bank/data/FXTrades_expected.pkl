��      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�dataSourceName��date��__meta_model__��,reportDetails.investmentFirmCoveredDirective��reportDetails.reportStatus��sourceIndex��	sourceKey��=tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator��#transactionDetails.buySellIndicator��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime��transactionDetails.venue�� transactionDetails.ultimateVenue��.transmissionDetails.orderTransmissionIndicator��reportDetails.transactionRefNo��__asset_class__��__currency__��	__venue__��marketIdentifiers.instrument��marketIdentifiers.parties��marketIdentifiers��__expiry_date__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�CX                                                                	       
       �t�bhB�__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KKK��h�b1�����R�(Kh"NNNJ����J����K t�b�C!           �t�bhhK ��h��R�(KKK��h�f8�����R�(KhPNNNJ����J����K t�b�C��HP|�?.�!��u�?('�UHy�?���?�y�?\���(�T@�"��?&ǝ��z�?�d��?{�/L�
�?��{��?c���&��?     p�@     p�@     p�@     p�@     j�@     L�@     ��@     j�@     @�@     L�@     ��@�t�bhhK ��h��R�(KKK��hO�CX                                                                	       
       �t�bhhK ��h��R�(KKK��h!�]�(�Saxobank�hwhwhwhwhwhwhwhwhwhw�
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��
2021-10-12��RTS22Transaction�h�h�h�h�h�h�h�h�h�h��NEWT�h�h�h�h�h�h�h�h�h�h��t/Users/<USER>/Desktop/steeleye/se-prefect-dir/swarm-tasks/tests/steeleye/tr/feed/saxo_bank/data/FXTrades_test.pkl�h�h�h�h�h�h�h�h�h�h��BUYI�h��SELL�h�h�h�h�h�h�h�h��USD�h�h�h��JPY��CAD�h��AUD�h�h�h��MONE�h�h�h�h�h�h�h�h�h�h��EUR�h�h�h�h�h�h�h�h�h�h��UNIT�h�h�h�h�h�h�h�h�h�h��AOTC�h�h�h�h�h�h�h�h�h�h��2021-10-12T12:24:11.603000Z��2021-10-12T15:43:03.617000Z��2021-10-12T14:57:34.103000Z��2021-10-12T15:01:42.063000Z��2021-10-12T11:29:19.020000Z��2021-10-12T11:25:50.917000Z��2021-10-12T11:27:01.403000Z��2021-10-12T11:24:24.893000Z��2021-10-12T11:52:01.477000Z��2021-10-12T05:53:56.547000Z��2021-10-12T15:13:26.957000Z��XOFF�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��$80000000002021101212241120211012BUYI��$80000000012021101215430320211012BUYI��$80000000022021101214573420211012SELL��$80000000032021101215014220211012SELL��$80000000042021101211291920211012BUYI��$80000000052021101211255020211012SELL��$80000000062021101211270120211012SELL��$80000000072021101211242420211012SELL��$80000000082021101211520120211012BUYI��$80000000092021101205535620211012BUYI��$80000000102021101215132620211012BUYI��fx spot�h�h�h�h�h�h�h�h��
fx forward�h�h�h�h�h�h�h�h�h�h�h�h��pandas._libs.missing��NA���h�h�h�h�h�h�h�h�h�h�]�}�(�labelId��XXXXUSDEURFXSPOT��path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�ua]�}�(h��XXXXUSDEURFXSPOT�h�h�h�h�ua]�}�(h��XXXXUSDEURFXSPOT�h�h�h�h�ua]�}�(h��XXXXUSDEURFXSPOT�h�h�h�h�ua]�}�(h��XXXXJPYEURFXSPOT�h�h�h�h�ua]�}�(h��XXXXCADEURFXSPOT�h�h�h�h�ua]�}�(h��XXXXUSDEURFXSPOT�h�h�h�h�ua]�}�(h��XXXXAUDEURFXSPOT�h�h�h�h�ua]�}�(h��XXXXCADEURFXSPOT�h�h�h�h�uah�h�]�(}�(h��lei:549300tl5406ic1xkd09�h��parties.counterparty�h�h�u}�(h��
id:1000001�h��parties.trader�h�h��ARRAY���R�u}�(h��lei:549300tl5406ic1xkd09�h��parties.seller�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��
id:1000001�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��
id:1000001�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h��
parties.buyer�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��
id:1000001�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��
id:1000001�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��
id:1000001�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��
id:1000001�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��
id:1000001�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��
id:1000001�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��
id:1000001�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�ue]�(}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�u}�(h��
id:1000001�h�h�h�h�u}�(h��lei:549300tl5406ic1xkd09�h�h�h�h�ue]�(h�h�h�h�e]�(h�h�h�h�e]�(h�h�h�h�e]�(h�h�h�h�e]�(h�h�h�h�e]�(h�h�h�j   e]�(h�j  j  j  e]�(h�j
  j  j  e]�(h�j  j  j  e]�(j  j  j  e]�(j  j!  j#  eet�bhhK ��h��R�(KKK��h!�]�(h�h�h�h�h�h�h�h�h�h�h�et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h(h,h8et�bhBNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h.h1et�bhBNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bhBNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h)h+h-h/h0h2h3h4h5h6h7h9h:h;h<h=h>h?et�bhBNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h@at�bhBNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hZ�mgr_locs�hhK ��h��R�(KK��h�i8�����R�(KhPNNNJ����J����K t�b�C                     �t�bu}�(jl  hdjm  �builtins��slice���K	KK��R�u}�(jl  hnjm  j{  KKK��R�u}�(jl  htjm  hhK ��h��R�(KK��jt  �C�                                           
              
                                                                                           �t�bu}�(jl  j3  jm  j{  KKK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.