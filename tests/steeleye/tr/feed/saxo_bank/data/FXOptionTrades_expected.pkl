���
      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KK��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�dataSourceName��date��__meta_model__��,reportDetails.investmentFirmCoveredDirective��reportDetails.reportStatus��sourceIndex��	sourceKey��=tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator��#transactionDetails.buySellIndicator��transactionDetails.price�� transactionDetails.priceCurrency�� transactionDetails.priceNotation��transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime��transactionDetails.venue�� transactionDetails.ultimateVenue��.transmissionDetails.orderTransmissionIndicator��reportDetails.transactionRefNo��__asset_class__��__currency__��	__venue__��marketIdentifiers.instrument��marketIdentifiers.parties��marketIdentifiers��__expiry_date__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(hhhK ��h��R�(KK��h�i8�����R�(K�<�NNNJ����J����K t�b�C               �t�bhB�__swarm_raw_index__�u��R�e]�(hhK ��h��R�(KKK��h�b1�����R�(Kh"NNNJ����J����K t�b�C  �t�bhhK ��h��R�(KKK��h�f8�����R�(KhPNNNJ����J����K t�b�C #��~j��?�|?5^��?     j(A     j(A�t�bhhK ��h��R�(KKK��hO�C               �t�bhhK ��h��R�(KKK��h!�]�(�Saxobank�hw�
2021-10-12��
2021-10-12��RTS22Transaction�hz�NEWT�h{�z/Users/<USER>/Desktop/steeleye/se-prefect-dir/swarm-tasks/tests/steeleye/tr/feed/saxo_bank/data/FXOptionTrades_test.pkl�h|�BUYI��SELL��JPY�h�MONE�h��EUR�h��UNIT�h��AOTC�h��2021-10-12T10:02:07.737000Z��2021-10-12T10:02:42.050000Z��XXXX�h�h�h��$9********02021101210020720211012BUYI��$9********02021101210024220211012SELL��	fx option�h�hh�pandas._libs.missing��NA���h�]�(}�(�labelId��XXXXEURJPYOC2021-10-29��path��instrumentDetails.instrument��type��se_schema.static.market��IdentifierType����OBJECT���R�u}�(h��"XXXXEURJPYOC2021-10-29113.********�h�h�h�h�ue]�(}�(h��XXXXEURJPYOC2021-10-13�h�h�h�h�u}�(h��"XXXXEURJPYOC2021-10-13112.********�h�h�h�h�ue]�(}�(h��id:101000zz1010ic1zzd09�h��parties.counterparty�h�h�u}�(h��
id:9142297�h��parties.trader�h�h��ARRAY���R�u}�(h��id:101000zz1010ic1zzd09�h��parties.seller�h�h�ue]�(}�(h��id:101000zz1010ic1zzd09�h�h�h�h�u}�(h��
id:9142297�h�h�h�h�u}�(h��id:101000zz1010ic1zzd09�h��
parties.buyer�h�h�ue]�(h�h�h�h�h�e]�(h�h�h�h�h�eet�bhhK ��h��R�(KKK��h!�]�(�
2021-10-29��
2021-10-13�et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�(h(h,h8et�bhBNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h.h1et�bhBNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bhBNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h%h&h'h)h+h-h/h0h2h3h4h5h6h7h9h:h;h<h=h>h?et�bhBNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h@at�bhBNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�hZ�mgr_locs�hhK ��h��R�(KK��h�i8�����R�(KhPNNNJ����J����K t�b�C                     �t�bu}�(h�hdh��builtins��slice���K	KK��R�u}�(h�hnh�j  KKK��R�u}�(h�hth�hhK ��h��R�(KK��h��C�                                           
              
                                                                                           �t�bu}�(h�h�h�j  KKK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.