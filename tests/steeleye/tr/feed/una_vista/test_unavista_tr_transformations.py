import os
from pathlib import Path

import pandas as pd
import pytest
from prefect import context
from swarm.conf import SettingsCls
from swarm.task.auditor import Auditor

from swarm_tasks.steeleye.tr.transformations.rts22_transform_maps import (
    una_vista_transform_map,
)

SCRIPT_PATH = Path(__file__).parent
TEST_FILES_DIR = SCRIPT_PATH.joinpath("data")
TEST_FILE_PATH = TEST_FILES_DIR.joinpath(r"source_df.pkl")
EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath(r"expected_result.pkl")
NO_COUNTERPARTY_TEST_FILE_PATH = TEST_FILES_DIR.joinpath(
    r"no_counterparty_source_df.pkl"
)
NO_COUNTERPARTY_EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath(
    r"no_counterparty_expected_result.pkl"
)
FALLBACK_UNDERLYING_TEST_FILE_PATH = TEST_FILES_DIR.joinpath(
    r"source_df_fallback_underlying.pkl"
)
FALLBACK_UNDERLYING_EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath(
    r"expected_result_fallback_underlying.pkl"
)
UNDERLYING_INDEX_TERM_TEST_FILE_PATH = TEST_FILES_DIR.joinpath(
    r"test_underlying_index_term_mapping.pkl"
)
UNDERLYING_INDEX_TERM_EXPECTED_FILE_PATH = TEST_FILES_DIR.joinpath(
    r"expected_result_underlying_index_term.pkl"
)


@pytest.fixture()
def auditor() -> Auditor:
    return Auditor(task_name="dummy", batch_id=1)


class TestUnaVistaTransformations:
    """
    Text UnaVistaTransformations
    """

    @pytest.mark.parametrize(
        "test_file_path, expected_file_path",
        [
            (TEST_FILE_PATH, EXPECTED_FILE_PATH),
            (NO_COUNTERPARTY_TEST_FILE_PATH, NO_COUNTERPARTY_EXPECTED_FILE_PATH),
            (
                FALLBACK_UNDERLYING_TEST_FILE_PATH,
                FALLBACK_UNDERLYING_EXPECTED_FILE_PATH,
            ),
            (
                # test that UNDERLYING_INDEX_TERM is mapped correctly.
                # There are some rows where both TERM and INSTRUMENT_ID are null -> no output term
                # Some, where only TERM is null, but INSTRUMENT_ID is present -> no output term
                # Some, where only INSTRUMENT_ID is null, but TERM is present -> output term is populated
                # Some, where both are present -> output term is populated, only with the TERM value
                UNDERLYING_INDEX_TERM_TEST_FILE_PATH,
                UNDERLYING_INDEX_TERM_EXPECTED_FILE_PATH,
            ),
        ],
    )
    def test_end_to_end_transformations(
        self, mocker, test_file_path, expected_file_path, auditor
    ):
        os.environ["SWARM_FILE_URL"] = str(test_file_path)

        # mock realm to chose default transformation
        mock_realm = mocker.patch.object(
            SettingsCls, "realm", new_callable=mocker.PropertyMock
        )
        mock_realm.return_value = "default.dev.steeleye.co"

        expected = pd.read_pickle(expected_file_path)

        source_frame = pd.read_pickle(test_file_path)
        task = una_vista_transform_map.transformation(tenant="foo")(
            source_frame=source_frame, logger=context.get("logger"), auditor=auditor
        )
        result = task.process()

        pd.testing.assert_frame_equal(
            result.drop(["sourceKey"], axis=1),
            expected.drop(["sourceKey"], axis=1),
            check_dtype=False,
        )
