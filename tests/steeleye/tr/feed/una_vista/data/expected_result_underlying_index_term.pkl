��+-      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKE��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�dataSourceName��date��__meta_model__��reportDetails.reportStatus��+reportDetails.tradingVenueTransactionIdCode��sourceIndex��	sourceKey��:tradersAlgosWaiversIndicators.commodityDerivativeIndicator��3tradersAlgosWaiversIndicators.otcPostTradeIndicator��=tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator��3tradersAlgosWaiversIndicators.shortSellingIndicator��-tradersAlgosWaiversIndicators.waiverIndicator��*transactionDetails.branchMembershipCountry��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��+transactionDetails.derivativeNotionalChange��transactionDetails.netAmount��transactionDetails.price�� transactionDetails.priceCurrency��%transactionDetails.priceNotApplicable�� transactionDetails.priceNotation��transactionDetails.pricePending��transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime��transactionDetails.venue�� transactionDetails.ultimateVenue��!transactionDetails.upFrontPayment��)transactionDetails.upFrontPaymentCurrency��.transmissionDetails.orderTransmissionIndicator��reportDetails.transactionRefNo��marketIdentifiers.instrument��#instrument_classification_attribute��expiry_date_attribute��isin_attribute��underlying_index_name_attribute��%underlying_index_term_value_attribute��venue_attribute��option_strike_price_attribute��notional_currency_1_attribute��notional_currency_2_attribute��option_type_attribute��underlying_isin_attribute��marketIdentifiers.parties��marketIdentifiers��__strike_price_currency__��__notional_currency_1__��__instrument_full_name__��__instrument_classification__��__maturity_date__��__instrument_id_code_type__��__strike_price_type__��__instrument_id_code__��__notional_currency_2__��__delivery_type__��__option_exercise_style__��__strike_price__��__price_multiplier__��__notional_currency_2_type__��__data_category__��__is_created_through_fallback__��1parties.buyerTransmittingFirm.firmIdentifiers.lei��2parties.sellerTransmittingFirm.firmIdentifiers.lei��'transactionDetails.swapDirectionalities��__underlying_index_name__��__underlying_index_term__�et�b�name�Nu��R�h
�pandas.core.indexes.range��
RangeIndex���}�(hkN�start�K �stop�K
�step�Ku��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK
��h!�]�(�191��22313��4��3��5��2��5��1��6��29�et�b�_dtype�hx�StringDtype���)��ubhz)��}�(h}hhK ��h��R�(KK
��h!�]�(�pandas._libs.missing��NA���h�h�h�h�h��US78378X1072�h�h�h�et�bh�h�)��ub�numpy.core.numeric��_frombuffer���(�                 �h�b1�����R�(Kh"NNNJ����J����K t�bKK
���C�t�R�h�(�0      >�٬�Ky@��6PJ@���QL�@�z�GE�@\���(F�@q=
ףH�@���QP�@�(\��_�@fffffZ�@     ��@      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      I@      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      ��h�f8�����R�(K�<�NNNJ����J����K t�bKK
��h�t�R�h�(�P                                                                       	       �h�i8�����R�(Kh�NNNJ����J����K t�bKK
��h�t�R�hhK ��h��R�(KK7K
��h!�]�(�UnaVista Transaction Report�h�h�h�h�h�h�h�h�h��
2024-11-19��
2024-11-19��
2024-11-19��
2024-11-19��
2024-11-19��
2024-11-19��
2024-11-19��
2024-11-19��
2024-11-19��
2024-11-19��RTS22Transaction�h�h�h�h�h�h�h�h�ȟNEWT�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��z/Users/<USER>/Desktop/steeleye/swarm-tasks/tests/steeleye/tr/feed/una_vista/data/test_underlying_index_term_mapping.pkl�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��BUYI�hόSELL�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �EUR�hьUSD�h�h�h�h�h�h�h������������MONE�h�h�h�h�h�h�h�hӌBAPO�����������h�h�h�h�h�h�h�h�h�h��UNIT�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��AOTC�h�h�h�h�h�h�h�h�h֌2024-11-19T16:40:56.000000Z��2024-11-19T16:40:56.000000Z��2024-11-19T15:04:39.000000Z��2024-11-19T15:24:15.000000Z��2024-11-19T15:24:15.000000Z��2024-11-19T15:24:15.000000Z��2024-11-19T15:04:39.000000Z��2024-11-19T15:04:39.000000Z��2024-11-19T15:13:10.000000Z��2024-11-19T14:57:00.000000Z��XOFF�h�h�h�h�h�h�h�h�XXXX�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��32050703X62247903XUKXMP��31101268X67072368XUKXMP��32350427X78842127XUKXMP��32350427X78846727XUKXMP��32350427X78846627XUKXMP��32350427X78846527XUKXMP��32350427X78842327XUKXMP��32350427X78842227XUKXMP��32350427X78844627XUKXMP��31050848X65512648XUKXMP�]�}�(�labelId��NL0011585146��path��instrumentDetails.instrument��type��se_elastic_schema.static.market��IdentifierType����OBJECT���R�ua]�}�(h�DE0007100000�h�h�h�h�ua]�}�(h�US3846371041�h�h�h�h�ua]�}�(h�h�h�h�h�h�ua]�}�(h�h�h�h�h�h�ua]�}�(h�h�h�h�h�h�ua]�}�(h�h�h�h�h�h�ua]�}�(h�h�h�h�h�h�ua]�}�(h�h�h�h�h�h�uah�G�      G�      G�      G�      G�      G�      G�      G�      G�      �FFICSX�G�      G�      G�      G�      G�      G�      G�      G�      G�      �
20/12/2024�h�h�h�h�h�h�h�h�h�G�      G�      G�      G�      G�      G�      G�      G�      G�      G�      �
S&P 500 INDEX�G�      G�      �TERM_X�G�      G�      �TERM_Y��TERM�G�      G�      G�      h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��USD�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�]�(}�(h�id:549300gk9kwvqkhqoooo�h�parties.executingEntity�h�h�u}�(h�id:bfm8t61ct2l1qcemoooo�h�parties.counterparty�h�h�u}�(h�id:j0dflana�h�parties.executionWithinFirm�h�h�u}�(h�id:j0sstore�h�$parties.investmentDecisionWithinFirm�h�h�u}�(h�id:549300opt1kllme7oooo�h�
parties.buyer�h�h��ARRAY���R�u}�(h�id:bfm8t61ct2l1qcemoooo�h�parties.seller�h�j%  u}�(h�lei:5493006xrzfhf1kwty04�h�parties.buyerDecisionMaker�h�j%  ue]�(}�(h�id:549300gk9kwvqkhqoooo�h�j  h�h�u}�(h�id:bfm8t61ct2l1qcemoooo�h�j  h�h�u}�(h�id:j0dflana�h�j  h�h�u}�(h�id:j0sstore�h�j  h�h�u}�(h�id:254900otm8gqugekoooo�h�j"  h�j%  u}�(h�id:bfm8t61ct2l1qcemoooo�h�j(  h�j%  u}�(h�lei:5493006xrzfhf1kwty04�h�j+  h�j%  ue]�(}�(h�id:549300gk9kwvqkhqoooo�h�j  h�h�u}�(h�id:k6q0w1ps1l1o4iqloooo�h�j  h�h�u}�(h�id:j0dflana�h�j  h�h�u}�(h�id:j0bmccor�h�j  h�h�u}�(h�id:k6q0w1ps1l1o4iqloooo�h�j"  h�j%  u}�(h�id:549300hfkiqslh3coooo�h�j(  h�j%  u}�(h�id:549300gk9kwvqkhqoooo�h�parties.sellerDecisionMaker�h�j%  ue]�(}�(h�id:549300gk9kwvqkhqoooo�h�j  h�h�u}�(h�id:k6q0w1ps1l1o4iqloooo�h�j  h�h�u}�(h�id:j0dflana�h�j  h�h�u}�(h�id:j0bmccor�h�j  h�h�u}�(h�id:k6q0w1ps1l1o4iqloooo�h�j"  h�j%  u}�(h�id:549300hfkiqslh3coooo�h�j(  h�j%  u}�(h�id:549300gk9kwvqkhqoooo�h�jJ  h�j%  ue]�(}�(h�id:549300gk9kwvqkhqoooo�h�j  h�h�u}�(h�id:k6q0w1ps1l1o4iqloooo�h�j  h�h�u}�(h�id:j0dflana�h�j  h�h�u}�(h�id:j0bmccor�h�j  h�h�u}�(h�id:k6q0w1ps1l1o4iqloooo�h�j"  h�j%  u}�(h�id:549300hfkiqslh3coooo�h�j(  h�j%  u}�(h�id:549300gk9kwvqkhqoooo�h�jJ  h�j%  ue]�(}�(h�id:549300gk9kwvqkhqoooo�h�j  h�h�u}�(h�id:k6q0w1ps1l1o4iqloooo�h�j  h�h�u}�(h�id:j0dflana�h�j  h�h�u}�(h�id:j0bmccor�h�j  h�h�u}�(h�id:k6q0w1ps1l1o4iqloooo�h�j"  h�j%  u}�(h�id:549300hfkiqslh3coooo�h�j(  h�j%  u}�(h�id:549300gk9kwvqkhqoooo�h�jJ  h�j%  ue]�(}�(h�id:549300gk9kwvqkhqoooo�h�j  h�h�u}�(h�id:k6q0w1ps1l1o4iqloooo�h�j  h�h�u}�(h�id:j0dflana�h�j  h�h�u}�(h�id:j0bmccor�h�j  h�h�u}�(h�id:k6q0w1ps1l1o4iqloooo�h�j"  h�j%  u}�(h�id:549300hfkiqslh3coooo�h�j(  h�j%  u}�(h�id:549300gk9kwvqkhqoooo�h�jJ  h�j%  ue]�(}�(h�id:549300gk9kwvqkhqoooo�h�j  h�h�u}�(h�id:k6q0w1ps1l1o4iqloooo�h�j  h�h�u}�(h�id:j0dflana�h�j  h�h�u}�(h�id:j0bmccor�h�j  h�h�u}�(h�id:k6q0w1ps1l1o4iqloooo�h�j"  h�j%  u}�(h�id:549300hfkiqslh3coooo�h�j(  h�j%  u}�(h�id:549300gk9kwvqkhqoooo�h�jJ  h�j%  ue]�(}�(h�id:549300gk9kwvqkhqoooo�h�j  h�h�u}�(h�id:k6q0w1ps1l1o4iqloooo�h�j  h�h�u}�(h�id:j0dflana�h�j  h�h�u}�(h�id:j0bmccor�h�j  h�h�u}�(h�id:k6q0w1ps1l1o4iqloooo�h�j"  h�j%  u}�(h�id:549300hfkiqslh3coooo�h�j(  h�j%  u}�(h�id:549300gk9kwvqkhqoooo�h�jJ  h�j%  ue]�(}�(h�id:549300gk9kwvqkhqoooo�h�j  h�h�u}�(h�id:ggdzp1uygu9stuhroooo�h�j  h�h�u}�(h�id:j0dflana�h�j  h�h�u}�(h�id:j0sstore�h�j  h�h�u}�(h�id:ggdzp1uygu9stuhroooo�h�j"  h�j%  u}�(h�lei:213800sfegauym8lcn35�h�j(  h�j%  u}�(h�lei:5493006xrzfhf1kwty04�h�jJ  h�j%  ue]�(h�j  j  j  j  j   j&  j)  e]�(h�j-  j/  j1  j3  j5  j7  j9  e]�(h�j<  j>  j@  jB  jD  jF  jH  e]�(j  jL  jN  jP  jR  jT  jV  jX  e]�(j  j[  j]  j_  ja  jc  je  jg  e]�(j  jj  jl  jn  jp  jr  jt  jv  e]�(j  jy  j{  j}  j  j�  j�  j�  e]�(j	  j�  j�  j�  j�  j�  j�  j�  e]�(j  j�  j�  j�  j�  j�  j�  j�  e]�(j�  j�  j�  j�  j�  j�  j�  eh�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�j  h�h�h�h�h�h�h�h�h��S&P500 EMINI DEC 24�h�h�h�h�h�h�h�h�h�j  h�h�h�h�h�h�h�h�h�h��ID�j�  j�  j�  j�  j�  j�  j�  j�  h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�G�      G�      G�      G�      G�      G�      G�      G�      G�      �CASH�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h�h��US78378X1072�h�h��S&P500INDEX;US78378X1072�et�bhhK ��h��R�(KKK
��h!�]�(G�      G�      j  G�      G�      j  j  G�      G�      G�      et�be]�(h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h\at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h.hEhdet�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h6hNh_h`hahbhcet�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bhkNu��R�h
h}�(hhhK ��h��R�(KK7��h!�]�(h%h&h'h(h)h+h,h-h/h0h1h2h3h4h5h7h8h9h:h<h=h>h?h@hAhBhChDhFhGhHhIhJhKhLhMhOhPhQhRhShThUhVhWhXhYhZh[h]h^hehfhghhet�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hiat�bhkNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h{�mgr_locs��builtins��slice���KKK��R�u}�(j  h�j  j  K7K8K��R�u}�(j  h�j  h�(�       	               ?       �h�K��h�t�R�u}�(j  h�j  h�(�8              )       :       ;       <       =       >       �h�K��h�t�R�u}�(j  h�j  j  KKK��R�u}�(j  h�j  h�(��                                                               
                     
                                                                                                                       !       "       #       $       %       &       '       (       *       +       ,       -       .       /       0       1       2       3       4       5       6       8       9       @       A       B       C       �h�K7��h�t�R�u}�(j  j�  j  j  KDKEK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.