��G>      �pandas.core.frame��	DataFrame���)��}�(�_mgr��pandas.core.internals.managers��BlockManager���)��(]�(�pandas.core.indexes.base��
_new_Index���h�Index���}�(�data��numpy.core.multiarray��_reconstruct����numpy��ndarray���K ��Cb���R�(KKE��h�dtype����O8�����R�(K�|�NNNJ����J����K?t�b�]�(�dataSourceName��date��__meta_model__��reportDetails.reportStatus��+reportDetails.tradingVenueTransactionIdCode��sourceIndex��	sourceKey��:tradersAlgosWaiversIndicators.commodityDerivativeIndicator��3tradersAlgosWaiversIndicators.otcPostTradeIndicator��=tradersAlgosWaiversIndicators.securitiesFinancingTxnIndicator��3tradersAlgosWaiversIndicators.shortSellingIndicator��-tradersAlgosWaiversIndicators.waiverIndicator��*transactionDetails.branchMembershipCountry��#transactionDetails.buySellIndicator��*transactionDetails.complexTradeComponentId��+transactionDetails.derivativeNotionalChange��transactionDetails.netAmount��transactionDetails.price�� transactionDetails.priceCurrency��%transactionDetails.priceNotApplicable�� transactionDetails.priceNotation��transactionDetails.pricePending��transactionDetails.quantity��#transactionDetails.quantityCurrency��#transactionDetails.quantityNotation��transactionDetails.recordType��"transactionDetails.tradingCapacity��"transactionDetails.tradingDateTime��transactionDetails.venue�� transactionDetails.ultimateVenue��!transactionDetails.upFrontPayment��)transactionDetails.upFrontPaymentCurrency��.transmissionDetails.orderTransmissionIndicator��reportDetails.transactionRefNo��marketIdentifiers.instrument��#instrument_classification_attribute��expiry_date_attribute��isin_attribute��underlying_index_name_attribute��%underlying_index_term_value_attribute��venue_attribute��option_strike_price_attribute��notional_currency_1_attribute��notional_currency_2_attribute��option_type_attribute��underlying_isin_attribute��marketIdentifiers.parties��marketIdentifiers��__strike_price_currency__��__notional_currency_1__��__instrument_full_name__��__instrument_classification__��__maturity_date__��__instrument_id_code_type__��__strike_price_type__��__instrument_id_code__��__notional_currency_2__��__delivery_type__��__option_exercise_style__��__strike_price__��__price_multiplier__��__notional_currency_2_type__��__data_category__��__is_created_through_fallback__��1parties.buyerTransmittingFirm.firmIdentifiers.lei��2parties.sellerTransmittingFirm.firmIdentifiers.lei��'transactionDetails.swapDirectionalities��__underlying_index_name__��__underlying_index_term__�et�b�name�Nu��R�h
�pandas.core.indexes.numeric��
Int64Index���}�(h�numpy.core.numeric��_frombuffer���(�@                                                                �h�i8�����R�(K�<�NNNJ����J����K t�bK���C�t�R�hk�__swarm_raw_index__�u��R�e]�(�pandas.core.arrays.string_��StringArray���)��}�(�_ndarray�hhK ��h��R�(KK��h!�]�(�NEWT�h�h�h�h�h�h�h�et�b�_dtype�h��StringDtype���)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�False�h�h�h��FALSE�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�5��102�h��128��1��4��40�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�AOTC�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�XXXX�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�True�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�31850907X56750407XUKXMP��31851007X56750307XUKXMP��31850807X56750507XUKXMP��31851107X56750207XUKXMP��30900559X84750059XUKXMP��30900459X84749959XUKXMP��30700648X62998248XUKXMP��30700748X62998348XUKXMP�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�O8�����R�(Kh"NNNJ����J����K?t�b�]�(�FFDCSX�h�h�h�FFICSX�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(�
2024-12-16�h�h�h��
2024-12-20�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(�pandas._libs.missing��NA���j	  j	  j	  j	  j	  j	  j	  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(j	  j	  j	  j	  �S&P 500 Price Return Inde�j  �
S&P 500 INDEX�j  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(j	  j	  j	  j	  j	  j	  j	  j	  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(�AUST 10YR BOND DEC 24�j0  j0  j0  �MICRO EMINI S&P 500 DEC 24�j1  �S&P500 EMINI DEC 24�j2  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(h�h�h�h�h�h�h�h�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(�DAU3TB0000192, AU0000249302, AU0000217101, AU0000274706, AU000XCLWAM0��DAU3TB0000192, AU0000249302, AU0000217101, AU0000274706, AU000XCLWAM0��DAU3TB0000192, AU0000249302, AU0000217101, AU0000274706, AU000XCLWAM0��DAU3TB0000192, AU0000249302, AU0000217101, AU0000274706, AU000XCLWAM0�j	  j	  �US78378X1072��US78378X1072�et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(�CASH�jT  jT  jT  jT  jT  jT  jT  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(j	  j	  j	  j	  j	  j	  j	  j	  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(�100000.0�jg  jg  jg  �5.0�jh  �50.0�ji  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(j	  j	  j	  j	  j	  j	  j	  j	  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(j	  j	  j	  j	  j	  j	  j	  j	  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(j	  j	  j	  j	  j	  j	  j	  j	  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h�]�(j	  j	  j	  j	  j	  j	  j	  j	  et�bh�h�)��ubh�)��}�(h�hhK ��h��R�(KK��h!�]�(j	  j	  j	  j	  �S&P500PriceReturnInde��S&P500PriceReturnInde��S&P500INDEX;US78378X1072��S&P500INDEX;US78378X1072�et�bh�h�)��ubht(�       �h�b1�����R�(Kh"NNNJ����J����K t�bKK��h|t�R�ht(��       �Q���W@�Q���W@�Q���W@�Q���W@     �@    ��@qZ��@qZ��@      �      �      �      �      �      �      �      �      �      �      �      �      �      �      �      ��h�f8�����R�(KhyNNNJ����J����K t�bKK��h|t�R�ht(�@                                                                �hxKK��h|t�R�hhK ��h��R�(KK&K��h!�]�(�UnaVista Transaction Report�j�  j�  j�  j�  j�  j�  j�  �
2024-10-16��
2024-10-16��
2024-10-16��
2024-10-16��
2024-10-22��
2024-10-22��
2024-10-22��
2024-10-22��RTS22Transaction�j�  j�  j�  j�  j�  j�  j�  j	  j	  j	  j	  j	  j	  j	  j	  �u/Users/<USER>/Desktop/steeleye/swarm-tasks/tests/steeleye/tr/feed/una_vista/data/source_df_fallback_underlying.pkl�j�  j�  j�  j�  j�  j�  j�  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  �BUYI�j�  j�  j�  j�  �SELL�j�  j�  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  G�      G�      G�      G�      G�      G�      G�      G�      j	  j	  j	  j	  j	  j	  j	  j	  ���������PERC�j�  j�  j�  �BAPO�j�  j�  j�  ��������j	  j	  j	  j	  j	  j	  j	  j	  �UNIT�j�  j�  j�  j�  j�  j�  j�  j	  j	  j	  j	  j	  j	  j	  j	  �2024-10-16T08:19:12.035000Z��2024-10-16T08:19:12.035000Z��2024-10-16T08:19:12.035000Z��2024-10-16T08:19:12.035000Z��2024-10-22T13:48:11.000000Z��2024-10-22T13:48:00.000000Z��2024-10-22T13:48:10.000000Z��2024-10-22T13:48:10.000000Z�j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  ]�(}�(�labelId��eXXXX'AU0000217101'|'AU0000249302'|'AU0000274706'|'AU000XCLWAM0']|['AU3TB0000192'FF2024-12-16 00:00:00��path��instrumentDetails.instrument��type��se_elastic_schema.static.market��IdentifierType����OBJECT���R�u}�(j�  �bXXXX'AU0000217101'|'AU0000249302'|'AU0000274706'|'AU000XCLWAM0']|['AU3TB0000192'FF2024-12 00:00:00�j�  j�  j�  j�  ue]�(}�(j�  �eXXXX'AU0000217101'|'AU0000249302'|'AU0000274706'|'AU000XCLWAM0']|['AU3TB0000192'FF2024-12-16 00:00:00�j�  j�  j�  j�  u}�(j�  �bXXXX'AU0000217101'|'AU0000249302'|'AU0000274706'|'AU000XCLWAM0']|['AU3TB0000192'FF2024-12 00:00:00�j�  j�  j�  j�  ue]�(}�(j�  �eXXXX'AU0000217101'|'AU0000249302'|'AU0000274706'|'AU000XCLWAM0']|['AU3TB0000192'FF2024-12-16 00:00:00�j�  j�  j�  j�  u}�(j�  �bXXXX'AU0000217101'|'AU0000249302'|'AU0000274706'|'AU000XCLWAM0']|['AU3TB0000192'FF2024-12 00:00:00�j�  j�  j�  j�  ue]�(}�(j�  �eXXXX'AU0000217101'|'AU0000249302'|'AU0000274706'|'AU000XCLWAM0']|['AU3TB0000192'FF2024-12-16 00:00:00�j�  j�  j�  j�  u}�(j�  �bXXXX'AU0000217101'|'AU0000249302'|'AU0000274706'|'AU000XCLWAM0']|['AU3TB0000192'FF2024-12 00:00:00�j�  j�  j�  j�  uej	  j	  ]�(}�(j�  �%XXXXUS78378X1072FF2024-12-20 00:00:00�j�  j�  j�  j�  u}�(j�  �"XXXXUS78378X1072FF2024-12 00:00:00�j�  j�  j�  j�  ue]�(}�(j�  �%XXXXUS78378X1072FF2024-12-20 00:00:00�j�  j�  j�  j�  u}�(j�  �"XXXXUS78378X1072FF2024-12 00:00:00�j�  j�  j�  j�  ue�AUD�j�  j�  j�  �USD�j�  j�  j�  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  �P['AU3TB0000192', 'AU0000249302', 'AU0000217101', 'AU0000274706', 'AU000XCLWAM0']��P['AU3TB0000192', 'AU0000249302', 'AU0000217101', 'AU0000274706', 'AU000XCLWAM0']��P['AU3TB0000192', 'AU0000249302', 'AU0000217101', 'AU0000274706', 'AU000XCLWAM0']��P['AU3TB0000192', 'AU0000249302', 'AU0000217101', 'AU0000274706', 'AU000XCLWAM0']�j	  j	  �US78378X1072��US78378X1072�]�(}�(j�  �lei:549300gk9kwvqkhq5s16�j�  �parties.executingEntity�j�  j�  u}�(j�  �lei:ggdzp1uygu9stuhrdp48�j�  �parties.counterparty�j�  j�  u}�(j�  �id:j0ibarne�j�  �parties.executionWithinFirm�j�  j�  u}�(j�  �
id:j0mnash�j�  �$parties.investmentDecisionWithinFirm�j�  j�  u}�(j�  �lei:549300bduoffmn21s916�j�  �
parties.buyer�j�  j�  �ARRAY���R�u}�(j�  �lei:ggdzp1uygu9stuhrdp48�j�  �parties.seller�j�  j  u}�(j�  �lei:549300gk9kwvqkhq5s16�j�  �parties.buyerDecisionMaker�j�  j  ue]�(}�(j�  �lei:549300gk9kwvqkhq5s16�j�  j  j�  j�  u}�(j�  �lei:ggdzp1uygu9stuhrdp48�j�  j  j�  j�  u}�(j�  �id:j0ibarne�j�  j  j�  j�  u}�(j�  �
id:j0mnash�j�  j
  j�  j�  u}�(j�  �lei:34ia63uzg38woynjks74�j�  j
  j�  j  u}�(j�  �lei:ggdzp1uygu9stuhrdp48�j�  j  j�  j  u}�(j�  �lei:5493006xrzfhf1kwty04�j�  j  j�  j  ue]�(}�(j�  �lei:549300gk9kwvqkhq5s16�j�  j  j�  j�  u}�(j�  �lei:ggdzp1uygu9stuhrdp48�j�  j  j�  j�  u}�(j�  �id:j0ibarne�j�  j  j�  j�  u}�(j�  �
id:j0mnash�j�  j
  j�  j�  u}�(j�  �lei:635400vtzaxdojwf8g75�j�  j
  j�  j  u}�(j�  �lei:ggdzp1uygu9stuhrdp48�j�  j  j�  j  u}�(j�  �lei:5493006xrzfhf1kwty04�j�  j  j�  j  ue]�(}�(j�  �lei:549300gk9kwvqkhq5s16�j�  j  j�  j�  u}�(j�  �lei:ggdzp1uygu9stuhrdp48�j�  j  j�  j�  u}�(j�  �id:j0ibarne�j�  j  j�  j�  u}�(j�  �
id:j0mnash�j�  j
  j�  j�  u}�(j�  �lei:549300eqb4zufbshye93�j�  j
  j�  j  u}�(j�  �lei:ggdzp1uygu9stuhrdp48�j�  j  j�  j  u}�(j�  �lei:5493006xrzfhf1kwty04�j�  j  j�  j  ue]�(}�(j�  �lei:549300gk9kwvqkhq5s16�j�  j  j�  j�  u}�(j�  �lei:ggdzp1uygu9stuhrdp48�j�  j  j�  j�  u}�(j�  �id:j0dflana�j�  j  j�  j�  u}�(j�  �id:j0sstore�j�  j
  j�  j�  u}�(j�  �lei:213800a8ra7syo6e6t15�j�  j
  j�  j  u}�(j�  �lei:ggdzp1uygu9stuhrdp48�j�  j  j�  j  u}�(j�  �lei:5493006xrzfhf1kwty04�j�  j  j�  j  ue]�(}�(j�  �lei:549300gk9kwvqkhq5s16�j�  j  j�  j�  u}�(j�  �lei:ggdzp1uygu9stuhrdp48�j�  j  j�  j�  u}�(j�  �id:j0dflana�j�  j  j�  j�  u}�(j�  �lei:ggdzp1uygu9stuhrdp48�j�  j
  j�  j  u}�(j�  �lei:intc�j�  j  j�  j  ue]�(}�(j�  �lei:549300gk9kwvqkhq5s16�j�  j  j�  j�  u}�(j�  �lei:ggdzp1uygu9stuhrdp48�j�  j  j�  j�  u}�(j�  �id:j0dflana�j�  j  j�  j�  u}�(j�  �lei:ggdzp1uygu9stuhrdp48�j�  j
  j�  j  u}�(j�  �lei:intc�j�  j  j�  j  ue]�(}�(j�  �lei:549300gk9kwvqkhq5s16�j�  j  j�  j�  u}�(j�  �lei:ggdzp1uygu9stuhrdp48�j�  j  j�  j�  u}�(j�  �id:j0dflana�j�  j  j�  j�  u}�(j�  �lei:ggdzp1uygu9stuhrdp48�j�  j
  j�  j  u}�(j�  �lei:intc�j�  j  j�  j  ue]�(j�  j�  j�  j  j  j  j  j  j  e]�(j�  j�  j  j  j  j  j   j"  j$  e]�(j�  j�  j'  j)  j+  j-  j/  j1  j3  e]�(j�  j�  j6  j8  j:  j<  j>  j@  jB  e]�(jE  jG  jI  jK  jM  jO  jQ  e]�(jT  jV  jX  jZ  j\  e]�(j�  j�  j_  ja  jc  je  jg  e]�(j�  j�  jj  jl  jn  jp  jr  ej	  j	  j	  j	  j	  j	  j	  j	  j�  j�  j�  j�  j�  j�  j�  j�  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  j	  ]��Other�a]�j}  a]�j}  a]�j}  aj	  j	  j	  j	  et�bh�)��}�(h�hhK ��h��R�(KK��h�]�(j	  j	  j	  j	  j	  j	  j	  j	  et�bh�h�)��ube]�(h
h}�(hhhK ��h��R�(KK��h!�]�h(at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h.at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h;at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h?at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hAat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hBat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hEat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hFat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hHat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hIat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hJat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hKat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hLat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hMat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hWat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hXat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h\at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h^at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h_at�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�haat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hbat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hcat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�heat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hfat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hhat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hdat�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�(h6hNh`et�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�h*at�bhkNu��R�h
h}�(hhhK ��h��R�(KK&��h!�]�(h%h&h'h)h+h,h-h/h0h1h2h3h4h5h7h8h9h:h<h=h>h@hChDhGhOhPhQhRhShThUhVhYhZh[h]hget�bhkNu��R�h
h}�(hhhK ��h��R�(KK��h!�]�hiat�bhkNu��R�e}��0.14.1�}�(�axes�h
�blocks�]�(}�(�values�h��mgr_locs��builtins��slice���KKK��R�u}�(j�  h�j�  j�  K	K
K��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  KKK��R�u}�(j�  h�j�  j�  K K!K��R�u}�(j�  h�j�  j�  K!K"K��R�u}�(j�  h�j�  j�  K#K$K��R�u}�(j�  h�j�  j�  K$K%K��R�u}�(j�  j   j�  j�  K%K&K��R�u}�(j�  j  j�  j�  K&K'K��R�u}�(j�  j  j�  j�  K'K(K��R�u}�(j�  j   j�  j�  K(K)K��R�u}�(j�  j)  j�  j�  K2K3K��R�u}�(j�  j5  j�  j�  K3K4K��R�u}�(j�  j>  j�  j�  K7K8K��R�u}�(j�  jM  j�  j�  K9K:K��R�u}�(j�  jW  j�  j�  K:K;K��R�u}�(j�  j`  j�  j�  K<K=K��R�u}�(j�  jl  j�  j�  K=K>K��R�u}�(j�  ju  j�  j�  K>K?K��R�u}�(j�  j~  j�  j�  K@KAK��R�u}�(j�  j�  j�  j�  KAKBK��R�u}�(j�  j�  j�  j�  KCKDK��R�u}�(j�  j�  j�  j�  K?K@K��R�u}�(j�  j�  j�  ht(�              )       ;       �h�i8�����R�(KhyNNNJ����J����K t�bK��h|t�R�u}�(j�  j�  j�  j�  KKK��R�u}�(j�  j�  j�  ht(�0                                                        
                     
                                                                                                  "       *       +       ,       -       .       /       0       1       4       5       6       8       B       �j�  K&��h|t�R�u}�(j�  j�  j�  j�  KDKEK��R�ueust�b�_typ��	dataframe��	_metadata�]��attrs�}�ub.