import logging

import pandas as pd
import pytest
from addict import addict
from prefect.engine import signals
from se_core_tasks.abstractions.transformations.abstract_market_counterparty_transformations import (
    MarketColumns,
)
from swarm.conf import SettingsCls
from swarm.task.io.read import FrameProducerResult

from swarm_tasks.steeleye.market_counterparty.transformations.aladdin.static import (
    AladdinBrokerDerivedColumns,
)
from swarm_tasks.steeleye.mymarket.firm.aladdin import split_and_update_trade_file_ids
from swarm_tasks.steeleye.mymarket.firm.aladdin.split_and_update_trade_file_ids import (
    ActionType,
)

logger = logging.getLogger(__name__)


class TestSplitAndUpdateTradeFileIds:
    def test_empty_frame(self, empty_frame: FrameProducerResult):
        """Test for empty frame. Skip should be raised"""
        task = self._init_task()
        with pytest.raises(signals.SKIP):
            task.execute(source_frame=empty_frame)

    def test_cpty_desk_id_missing(
        self, cpty_desk_id_missing_frame: FrameProducerResult
    ):
        """Test where 1 required col is missing. Skip should be raised"""
        task = self._init_task()
        with pytest.raises(signals.SKIP):
            task.execute(source_frame=cpty_desk_id_missing_frame)

    def test_issuer_long_name_missing(
        self, issuer_long_name_missing_frame: FrameProducerResult
    ):
        """Test where 1 required col is missing. Skip should be raised"""
        task = self._init_task()
        with pytest.raises(signals.SKIP):
            task.execute(source_frame=issuer_long_name_missing_frame)

    def test_all_creates(
        self,
        mocker,
        source_frame: FrameProducerResult,
        expected_frame_creates_only: pd.DataFrame,
    ):
        """Test for the case where there are no records in Elastic"""
        task = self._init_task(mocker=mocker, elastic_df=pd.DataFrame())
        result = task.execute(source_frame=source_frame)
        # Assert there are no updates
        with pytest.raises(KeyError):
            logger.info(result[ActionType.UPDATE.value])
        # Assert Creates
        create_result_frame = result[ActionType.CREATE.value].target
        self._sort_create_list_columns(
            result_df=create_result_frame, expected_result=expected_frame_creates_only
        )
        pd.testing.assert_frame_equal(
            left=create_result_frame, right=expected_frame_creates_only
        )

    def test_all_updates_no_new_values(
        self, mocker, source_frame: FrameProducerResult, elastic_df: pd.DataFrame
    ):
        """Test for the case all the records are in Elastic, and the same
        cptyDeskIds are in the input file as those that are in Elastic"""

        task = self._init_task(mocker=mocker, elastic_df=elastic_df)
        result = task.execute(source_frame=source_frame)
        # Assert there are no creates
        with pytest.raises(KeyError):
            logger.info(result[ActionType.CREATE.value])
        # Assert Updates
        update_result_frame = result[ActionType.UPDATE.value].target
        self._sort_elastic_list_columns(
            result_df=update_result_frame, expected_result=elastic_df
        )
        pd.testing.assert_frame_equal(
            left=update_result_frame, right=elastic_df, check_dtype=False
        )

    def test_all_updates_some_new_values(
        self,
        mocker,
        source_frame_with_new_values_in_existing_firms: FrameProducerResult,
        expected_result_new_values_updated: pd.DataFrame,
        elastic_df: pd.DataFrame,
    ):
        """Test for the case all the records are in Elastic, and there are some
        new cptyDeskIds are in the input file which are not in Elastic"""

        task = self._init_task(mocker=mocker, elastic_df=elastic_df)
        result = task.execute(
            source_frame=source_frame_with_new_values_in_existing_firms
        )
        # Assert there are no creates
        with pytest.raises(KeyError):
            logger.info(result[ActionType.CREATE.value])
        # Assert Updates
        update_result_frame = result[ActionType.UPDATE.value].target
        self._sort_elastic_list_columns(
            result_df=update_result_frame,
            expected_result=expected_result_new_values_updated,
        )
        pd.testing.assert_frame_equal(
            left=update_result_frame,
            right=expected_result_new_values_updated,
            check_dtype=False,
        )

    def test_creates_and_updates(
        self,
        mocker,
        source_frame_with_some_new_firms_and_updates: FrameProducerResult,
        elastic_df: pd.DataFrame,
        expected_result_creates_and_updates_updates_df: pd.DataFrame,
        expected_result_creates_and_updates_creates_df: pd.DataFrame,
    ):
        """Test for the case where there are both new records and updates to
        existing records"""
        task = self._init_task(mocker=mocker, elastic_df=elastic_df)
        result = task.execute(source_frame=source_frame_with_some_new_firms_and_updates)
        # Assert Creates
        create_result_frame = result[ActionType.CREATE.value].target
        self._sort_create_list_columns(
            result_df=create_result_frame,
            expected_result=expected_result_creates_and_updates_creates_df,
        )
        pd.testing.assert_frame_equal(
            left=create_result_frame,
            right=expected_result_creates_and_updates_creates_df,
        )
        # Assert Updates
        update_result_frame = result[ActionType.UPDATE.value].target
        self._sort_elastic_list_columns(
            result_df=update_result_frame,
            expected_result=expected_result_creates_and_updates_updates_df,
        )
        pd.testing.assert_frame_equal(
            left=update_result_frame,
            right=expected_result_creates_and_updates_updates_df,
            check_dtype=False,
        )

    @staticmethod
    def _init_task(mocker=None, elastic_df: pd.DataFrame = pd.DataFrame()):
        task = split_and_update_trade_file_ids.SplitAndUpdateTradeFileIds(
            name="SplitAndUpdateTradeFileIds"
        )
        if not mocker:
            return task
        mock_connections = mocker.patch.object(
            SettingsCls, "connections", new_callable=mocker.PropertyMock
        )
        mock_connections.return_value = addict.Dict(
            {
                "tenant-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                        "unique_props": "&uniqueProps",
                    },
                    "MAX_TERMS_SIZE": 1024,
                }
            }
        )

        mocker.patch.object(
            SettingsCls, "tenant", new_callable=mocker.PropertyMock, return_value="test"
        )

        mock_es_scroll = mocker.patch.object(
            split_and_update_trade_file_ids, "es_scroll"
        )
        mock_es_scroll.return_value = elastic_df

        return task

    @staticmethod
    def _sort_elastic_list_columns(
        result_df: pd.DataFrame, expected_result: pd.DataFrame
    ):
        unique_props = "&uniqueProps"
        result_df[MarketColumns.UNIQUE_IDS] = result_df[MarketColumns.UNIQUE_IDS].apply(
            sorted
        )
        expected_result[MarketColumns.UNIQUE_IDS] = expected_result[
            MarketColumns.UNIQUE_IDS
        ].apply(sorted)
        result_df[unique_props] = result_df[unique_props].apply(sorted)
        expected_result[unique_props] = expected_result[unique_props].apply(sorted)

        result_df[MarketColumns.SINK_IDENTIFIERS_TRADE_FILE_IDENTIFIERS] = result_df[
            MarketColumns.SINK_IDENTIFIERS_TRADE_FILE_IDENTIFIERS
        ].apply(
            lambda list_of_dicts: sorted(
                list_of_dicts, key=lambda dict_: int(dict_["id"])
            )
        )
        expected_result[
            MarketColumns.SINK_IDENTIFIERS_TRADE_FILE_IDENTIFIERS
        ] = expected_result[
            MarketColumns.SINK_IDENTIFIERS_TRADE_FILE_IDENTIFIERS
        ].apply(
            lambda list_of_dicts: sorted(
                list_of_dicts, key=lambda dict_: int(dict_["id"])
            )
        )

    @staticmethod
    def _sort_create_list_columns(
        result_df: pd.DataFrame, expected_result: pd.DataFrame
    ):
        for col in {
            AladdinBrokerDerivedColumns.ALL_CPTY_IDS,
            AladdinBrokerDerivedColumns.CPTY_PREFIX_IDS,
        }:
            result_df[col] = result_df[col].apply(sorted)
            expected_result[col] = expected_result[col].apply(sorted)
