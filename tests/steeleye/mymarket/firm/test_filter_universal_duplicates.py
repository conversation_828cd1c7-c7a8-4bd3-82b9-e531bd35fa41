from unittest.mock import patch

import addict
import pandas as pd
import pytest
from prefect.engine import signals
from swarm.conf import SettingsCls
from swarm.task.auditor import Auditor

from swarm_tasks.steeleye.mymarket.firm import filter_universal_duplicates
from swarm_tasks.steeleye.mymarket.firm.filter_universal_duplicates import (
    Params,
)


@pytest.fixture
def empty_df():
    data = {}
    return pd.DataFrame(data)


@pytest.fixture()
def params_fixture() -> Params:
    params = Params(
        **{
            "source_name_column": "NAME",
            "source_lei_column": "LEI",
            "elastic_lei_lookup_column": "firmIdentifiers.lei",
            "elastic_name_lookup_column": "name",
        }
    )
    return params


@pytest.fixture()
def source_df() -> pd.DataFrame:
    """Source data frame containing 11 records"
    Row 1: is a new record (lei and name not present in Elastic), should end up in target
    Row 2: new record that does not have an lei
    Row 3: record that matches on name but not lei (Marketcounterparty record does not have an lei)
    Row 4: record that matches on name but not lei (Marketcounterparty record does have an lei)
    Row 5: record that matches on lei
    Row 6: record that does not have either name or lei '
    """

    df = pd.DataFrame(
        [
            # Row 1: new record
            {"LEI": "lei1", "NAME": "Barclays", "row": 1},
            # Row 2: new record that does not have an lei
            {"LEI": pd.NA, "NAME": "Barclays", "row": 2},
            # Row 3: record that matches on name but not lei (Marketcounterparty record does not have an lei)
            {"LEI": pd.NA, "NAME": "Naam Electronics FZe", "row": 3},
            # Row 4: record that matches on name but not lei (Marketcounterparty record does have an lei)
            {"LEI": "21380059SYMJSEUGVP15", "NAME": "P G R Gold Trading LLC", "row": 4},
            # Row 5: record that matches on lei
            {"LEI": "213800YH7GA2XMC8OU59", "NAME": "Commerzbank", "row": 5},
            # Row 6: LEI null, NAME null
            {"LEI": pd.NA, "NAME": pd.NA, "row": 6},
        ]
    )
    return df


@pytest.fixture()
def expected_df_no_elastic_results() -> pd.DataFrame:
    """Source data frame containing 2 records, both of which are in Elastic" """
    df = pd.DataFrame(
        [
            {"action": "create", "LEI": "lei1", "NAME": "Barclays", "row": 1.0},
            {"action": "create", "LEI": pd.NA, "NAME": "Barclays", "row": 2.0},
            {
                "action": "create",
                "LEI": pd.NA,
                "NAME": "Naam Electronics FZe",
                "row": 3.0,
            },
            {
                "action": "create",
                "LEI": "21380059SYMJSEUGVP15",
                "NAME": "P G R Gold Trading LLC",
                "row": 4.0,
            },
            {
                "action": "create",
                "LEI": "213800YH7GA2XMC8OU59",
                "NAME": "Commerzbank",
                "row": 5.0,
            },
        ]
    )
    return df


@pytest.fixture()
def expected_df_some_elastic_results() -> pd.DataFrame:
    df = pd.DataFrame(
        [
            {
                "LEI": "213800YH7GA2XMC8OU59",
                "NAME": "Commerzbank",
                "row": 5,
                "sourceKey": "feeds/task/mymarket-sink/onefinancial/crm-batch/Client_MiFID_Data__SteelEye__Daily-************.csv",
                "&id": "402538|1249712",
                "&uniqueProps": [
                    "id:1249712",
                    "account:1249712",
                    "lei:213800yh7ga2xmc8ou59",
                ],
                "uniqueIds": [
                    "id:1249712",
                    "account:1249712",
                    "lei:213800yh7ga2xmc8ou59",
                ],
                "&model": "MarketCounterparty",
                "name": "Solaris Emea Limited",
                "details.clientMandate": "NON-DISCRETIONARY",
                "firmIdentifiers.lei": "213800YH7GA2XMC8OU59",
                "firmIdentifiers.branchCountry": "GB",
                "sinkIdentifiers.tradeFileIdentifiers": [
                    {"id": "1249712", "label": "ID"},
                    {"id": "1249712", "label": "Account"},
                    {"id": "213800YH7GA2XMC8OU59", "label": "LEI"},
                ],
                "sinkIdentifiers.orderFileIdentifiers": [
                    {"id": "1249712", "label": "ID"},
                    {"id": "1249712", "label": "Account"},
                    {"id": "213800YH7GA2XMC8OU59", "label": "LEI"},
                ],
                "action": "update",
            },
            {
                "LEI": pd.NA,
                "NAME": "Naam Electronics FZe",
                "row": 3,
                "sourceKey": "feeds/task/mymarket-sink/onefinancial/crm-batch/Client_MiFID_Data__SteelEye__Daily-************.csv",
                "&id": "402538|111465",
                "&uniqueProps": ["id:111465", "account:111465"],
                "uniqueIds": ["id:111465", "account:111465"],
                "&model": "MarketCounterparty",
                "name": "Naam Electronics FZe",
                "details.clientMandate": "NON-DISCRETIONARY",
                "firmIdentifiers.lei": pd.NA,
                "firmIdentifiers.branchCountry": "GB",
                "sinkIdentifiers.tradeFileIdentifiers": [
                    {"id": "111465", "label": "ID"},
                    {"id": "111465", "label": "Account"},
                ],
                "sinkIdentifiers.orderFileIdentifiers": [
                    {"id": "111465", "label": "ID"},
                    {"id": "111465", "label": "Account"},
                ],
                "action": "update",
            },
            {
                "LEI": "21380059SYMJSEUGVP15",
                "NAME": "P G R Gold Trading LLC",
                "row": 4,
                "sourceKey": "feeds/task/mymarket-sink/onefinancial/crm-batch/Client_MiFID_Data__SteelEye__Daily-************.csv",
                "&id": "614135|1221206",
                "&uniqueProps": [
                    "id:1221206",
                    "account:1221206",
                    "lei:21380059symjseugvp15",
                ],
                "uniqueIds": [
                    "id:1221206",
                    "account:1221206",
                    "lei:21380059symjseugvp15",
                ],
                "&model": "MarketCounterparty",
                "name": "P G R Gold Trading LLC",
                "details.clientMandate": "NON-DISCRETIONARY",
                "firmIdentifiers.lei": "21380059SYMJSEUGVP15",
                "firmIdentifiers.branchCountry": "GB",
                "sinkIdentifiers.tradeFileIdentifiers": [
                    {"id": "1221206", "label": "ID"},
                    {"id": "1221206", "label": "Account"},
                    {"id": "21380059SYMJSEUGVP15", "label": "LEI"},
                ],
                "sinkIdentifiers.orderFileIdentifiers": [
                    {"id": "1221206", "label": "ID"},
                    {"id": "1221206", "label": "Account"},
                    {"id": "21380059SYMJSEUGVP15", "label": "LEI"},
                ],
                "action": "update",
            },
            {
                "LEI": "lei1",
                "NAME": "Barclays",
                "row": 1,
                "sourceKey": pd.NA,
                "&id": pd.NA,
                "&uniqueProps": pd.NA,
                "uniqueIds": pd.NA,
                "&model": pd.NA,
                "name": pd.NA,
                "details.clientMandate": pd.NA,
                "firmIdentifiers.lei": pd.NA,
                "firmIdentifiers.branchCountry": pd.NA,
                "sinkIdentifiers.tradeFileIdentifiers": pd.NA,
                "sinkIdentifiers.orderFileIdentifiers": pd.NA,
                "action": "create",
            },
        ],
        index=[4, 2, 3, 0],
    )
    return df


@pytest.fixture()
def elastic_results_lei() -> pd.DataFrame:
    df = pd.DataFrame(
        [
            {
                "sourceKey": "feeds/task/mymarket-sink/onefinancial/crm-batch/Client_MiFID_Data__SteelEye__Daily-************.csv",
                "&id": "402538|1249712",
                "&uniqueProps": [
                    "id:1249712",
                    "account:1249712",
                    "lei:213800yh7ga2xmc8ou59",
                ],
                "uniqueIds": [
                    "id:1249712",
                    "account:1249712",
                    "lei:213800yh7ga2xmc8ou59",
                ],
                "&model": "MarketCounterparty",
                "name": "Solaris Emea Limited",
                "details.clientMandate": "NON-DISCRETIONARY",
                "firmIdentifiers.lei": "213800YH7GA2XMC8OU59",
                "firmIdentifiers.branchCountry": "GB",
                "sinkIdentifiers.tradeFileIdentifiers": [
                    {"id": "1249712", "label": "ID"},
                    {"id": "1249712", "label": "Account"},
                    {"id": "213800YH7GA2XMC8OU59", "label": "LEI"},
                ],
                "sinkIdentifiers.orderFileIdentifiers": [
                    {"id": "1249712", "label": "ID"},
                    {"id": "1249712", "label": "Account"},
                    {"id": "213800YH7GA2XMC8OU59", "label": "LEI"},
                ],
            },
        ]
    )
    return df


@pytest.fixture()
def elastic_results_name() -> pd.DataFrame:
    df = pd.DataFrame(
        [
            {
                "sourceKey": "feeds/task/mymarket-sink/onefinancial/crm-batch/Client_MiFID_Data__SteelEye__Daily-************.csv",
                "&id": "402538|111465",
                "&uniqueProps": [
                    "id:111465",
                    "account:111465",
                ],
                "uniqueIds": [
                    "id:111465",
                    "account:111465",
                ],
                "&model": "MarketCounterparty",
                "name": "Naam Electronics FZe",
                "details.clientMandate": "NON-DISCRETIONARY",
                "firmIdentifiers.lei": pd.NA,
                "firmIdentifiers.branchCountry": "GB",
                "sinkIdentifiers.tradeFileIdentifiers": [
                    {"id": "111465", "label": "ID"},
                    {"id": "111465", "label": "Account"},
                ],
                "sinkIdentifiers.orderFileIdentifiers": [
                    {"id": "111465", "label": "ID"},
                    {"id": "111465", "label": "Account"},
                ],
            },
            {
                "sourceKey": "feeds/task/mymarket-sink/onefinancial/crm-batch/Client_MiFID_Data__SteelEye__Daily-************.csv",
                "&id": "614135|1221206",
                "&uniqueProps": [
                    "id:1221206",
                    "account:1221206",
                    "lei:21380059symjseugvp15",
                ],
                "uniqueIds": [
                    "id:1221206",
                    "account:1221206",
                    "lei:21380059symjseugvp15",
                ],
                "&model": "MarketCounterparty",
                "name": "P G R Gold Trading LLC",
                "details.clientMandate": "NON-DISCRETIONARY",
                "firmIdentifiers.lei": "21380059SYMJSEUGVP15",
                "firmIdentifiers.branchCountry": "GB",
                "sinkIdentifiers.tradeFileIdentifiers": [
                    {"id": "1221206", "label": "ID"},
                    {"id": "1221206", "label": "Account"},
                    {"id": "21380059SYMJSEUGVP15", "label": "LEI"},
                ],
                "sinkIdentifiers.orderFileIdentifiers": [
                    {"id": "1221206", "label": "ID"},
                    {"id": "1221206", "label": "Account"},
                    {"id": "21380059SYMJSEUGVP15", "label": "LEI"},
                ],
            },
        ]
    )
    return df


class TestFilterUniversalDuplicates:
    """Test suite for FilterOutDuplicateFirms"""

    def test_empty_source_df(self, mocker, empty_df, params_fixture):
        """Test for the case where the source data frame is empty"""
        task = self._init_task(mocker=mocker, params=params_fixture)
        with pytest.raises(signals.SKIP):
            task.execute(source_frame=empty_df, params=params_fixture)

    def test_with_some_duplicate_rows_some_in_elastic(
        self,
        mocker,
        source_df,
        elastic_results_lei,
        elastic_results_name,
        expected_df_some_elastic_results,
        params_fixture,
    ):
        task = self._init_task(mocker=mocker, params=params_fixture)
        mock_elastic_results = mocker.patch.object(
            filter_universal_duplicates, "fetch_counterparty_records_from_elastic"
        )
        mock_elastic_results.side_effect = [elastic_results_lei, elastic_results_name]
        result = task.execute(source_frame=source_df, params=params_fixture)

        pd.testing.assert_frame_equal(
            left=result,
            right=expected_df_some_elastic_results,
        )

    @patch.object(
        filter_universal_duplicates,
        "fetch_counterparty_records_from_elastic",
    )
    def test_with_no_rows_in_elastic(
        self,
        mock_elastic_results,
        mocker,
        source_df,
        expected_df_no_elastic_results,
        params_fixture,
    ):
        """Test for the case where 8 records are present in the input. 2 are duplicates
        in the file, and 1 has null NAME and LEI. Expected result should have 5 records
        """
        task = self._init_task(mocker=mocker, params=params_fixture)

        mock_elastic_results.return_value = pd.DataFrame()

        result = task.execute(
            source_frame=source_df,
            params=params_fixture,
        )

        pd.testing.assert_frame_equal(left=result, right=expected_df_no_elastic_results)

    @staticmethod
    def _init_task(mocker, params):
        mock_connections = mocker.patch.object(
            SettingsCls, "connections", new_callable=mocker.PropertyMock
        )
        mock_connections.return_value = addict.Dict(
            {
                "tenant-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                    },
                    "MAX_TERMS_SIZE": 1024,
                },
                "reference-data": {
                    "meta": {
                        "id": "&id",
                        "model": "&model",
                        "hash": "&hash",
                        "prefix": "&",
                        "key": "&key",
                    },
                    "MAX_TERMS_SIZE": 1024,
                },
            }
        )
        mock_auditor = mocker.patch.object(
            filter_universal_duplicates.FilterUniversalDuplicates,
            "auditor",
        )
        mock_auditor.return_value = Auditor(task_name="FilterUniversalDuplicates")
        task = filter_universal_duplicates.FilterUniversalDuplicates(
            name="FilterUniversalDuplicates", params=params
        )

        return task
