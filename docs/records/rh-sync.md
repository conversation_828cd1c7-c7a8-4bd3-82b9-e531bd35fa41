# Synchronous record management

Swarm supplies a Elasticsearch-based storage abstraction layer that takes care of versioning, conflict management and generally simplifies and structures Elasticsearch operations.

## Data

All methods support managing records as dictionaries or _pandas_ dataframes. Swarm relies on metadata stored alongside the records. If those are missing, then they will be dynamically added when records are created.

## Basic CRUD

The following basic CRUD operations are supported:

* `get`
* `search`
* `create`
* `update`
* `delete`

## Advanced operations

### get_by_meta_version

Retrieve a record by the version number as stored in its `meta` properties, instead of the default Elasticsearch versioning.

### update_in_place

Partially updates properties of a record.

### bulk_create

Creates multiple records in a single Elasticsearch call. Takes an _Iterable_ (list, generator etc.) of records.


