from typing import List

import click
from se_elastic_schema.models import RTS22Transaction
from se_elasticsearch.repository import get_repository_by_cluster_version
from se_elasticsearch.repository import ResourceConfig
from swarm.flow.static import RegistryCluster
from swarm.platform.registry import Registry
from swarm.utilities.indict import Indict


REGISTRY_CONFIG = ResourceConfig(
    host=RegistryCluster.HOST,
    scheme=RegistryCluster.SCHEME,
    port=9701,
)


@click.command("apply_manual_hash")
@click.option(
    "--tenant",
    required=True,
    type=str,
    prompt="Name of tenant to apply change",
)
@click.option(
    "--stack",
    required=True,
    type=str,
    prompt="Name of stack where this should be applied for tenant",
)
def apply_manual_hash(tenant: str, stack: str):

    registry = Registry(config=REGISTRY_CONFIG)

    config = registry.get_resource(
        resource_id="tenant-data", resource_type="ELASTICSEARCH", stack=stack
    )
    config = ResourceConfig.validate(config)

    es = get_repository_by_cluster_version(resource_config=config)

    hash_query = needs_hash_query()

    target = es.scroll(
        query=hash_query,
        index=RTS22Transaction.get_elastic_index_alias(tenant=tenant),
        include_elastic_meta=True,
    )

    # flatten records
    target["records"] = target.apply(
        lambda x: Indict(obj=x).flatten().remove_empty().to_dict(),
        axis=1,
    )

    # meta hash
    hash_properties_fields = RTS22Transaction.hash_props()
    target[es.meta.hash] = target["records"].apply(
        lambda x: Indict(x).hash(fields=hash_properties_fields)
    )

    target = target.drop(columns=["records"])

    for i in range(len(target)):
        series = target.loc[i]
        body = {"doc": {es.meta.hash: series[es.meta.hash]}}
        index = series["__elastic__._index"]
        doc_id = series["__elastic__._id"]
        result = es.client.update(index=index, id=doc_id, body=body)
        print(result)


def needs_hash_query():
    body = {
        "query": {
            "bool": {
                "must": {"terms": {"&model": ["RTS22Transaction"]}},
                "must_not": [
                    {"exists": {"field": "&hash"}},
                    {"exists": {"field": "&expiry"}},
                ],
            }
        },
    }
    return body


def rts22_match_query(ids: List[str]):
    body = {
        "size": len(ids),
        "_source": ["&hash", "&id"],
        "query": {
            "bool": {
                "must": [
                    {"term": {"&model": "RTS22Transaction"}},
                    {"terms": {"&id": ids}},
                ]
            }
        },
    }
    return body


if __name__ == "__main__":
    apply_manual_hash()
