import json
import os
import warnings
from pathlib import Path
from typing import Optional

import click
import yaml
from pandas.core.common import SettingWithCopyWarning
from swarm.flow.runner import FlowRunner
from swarm.flow.static import FlowEnvVar
from urllib3.connectionpool import InsecureRequestWarning

from swarm_flows.tools.run_flow.schema import RunFlowConfig
from swarm_flows.tools.run_flow.static import Bundle

warnings.filterwarnings(action="ignore", category=InsecureRequestWarning)
warnings.filterwarnings(action="ignore", category=UserWarning)
warnings.filterwarnings(action="ignore", category=FutureWarning)
warnings.filterwarnings(action="ignore", category=SettingWithCopyWarning)


def get_bundle_path(flow_runner) -> Path:
    """
    Iterates through paths to bundle.yaml files under the bundle's
    directory until a match with the running bundle is found

    :return: PosixPath object to the running bundle.yaml file
    """
    target_bundle_id = flow_runner.workflow.config.bundle.id
    for root, dirs, files in os.walk(Bundle.BUNDLES_PATH):
        root_path = Path(root)
        bundle_paths = [root_path.joinpath(f) for f in files if f == Bundle.BUNDLE_FILE]
        for bundle_path in bundle_paths:

            with open(bundle_path, "r", encoding="utf-8") as file:
                bundle_yaml = yaml.safe_load(file)

            if bundle_yaml[Bundle.ID] != target_bundle_id:
                continue

            return bundle_path
    raise RuntimeError("Bundle path not found")


@click.command("run_flow")
@click.option("--visualise", type=bool)
@click.option("--config", default="run_flow_config.yaml", type=str)
def run_flow(
    visualise: bool = False,
    config: Optional[str] = None,
):

    settings = yaml.load(Path(config).read_text(), Loader=yaml.SafeLoader)
    runflow_config = RunFlowConfig.validate(settings)
    local_ports = runflow_config.flow_runner.local_ports
    os.environ.setdefault(FlowEnvVar.SWARM_LOCAL_PORTS, json.dumps(local_ports))
    # construct flow runner
    flow_runner = FlowRunner(**runflow_config.flow_runner.dict())

    # Visualise DAG
    bundle_path = get_bundle_path(flow_runner)
    pdf_name = flow_runner.workflow.config.bundle.id
    bundle_path.parent.joinpath(f"{pdf_name}")
    # flow_runner.workflow.flow.visualize(filename=pdf_path, format="pdf")
    # if visualise:
    #     flow_runner.workflow.flow.visualize()

    # execute flow router proxy
    flow_runner.execute()


if __name__ == "__main__":
    run_flow()
