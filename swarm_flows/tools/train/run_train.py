import logging
import os
import re
import warnings
from pathlib import Path
from typing import Any
from typing import Dict
from typing import List
from typing import Named<PERSON><PERSON><PERSON>
from typing import Optional

import addict
import boto3
import click
import pandas as pd
import yaml
from elasticsearch6 import NotFoundError
from git import Repo
from se_elasticsearch.repository import AnyElasticsearchRepositoryType
from se_elasticsearch.repository import get_repository_by_cluster_version
from swarm.client.record_handler.abstract_record_handler import <PERSON>bs<PERSON>R<PERSON>ordHandler
from swarm.client.record_handler_helper import init_record_handler
from swarm.flow.static import RegistryCluster
from swarm.schema.flow.bundle.components import ResourceConfig
from urllib3.exceptions import InsecureRequestWarning

from swarm_flows.tools.static import BUNDLES_PATH
from swarm_flows.tools.static import REPO_PATH
from swarm_flows.tools.train.schema import Bundle
from swarm_flows.tools.train.schema import Manifest
from swarm_flows.tools.train.static import BundleFileName
from swarm_flows.tools.train.static import Environments
from swarm_flows.tools.train.static import <PERSON>SB<PERSON>leFields
from swarm_flows.tools.train.static import ESFields
from swarm_flows.tools.train.static import <PERSON><PERSON><PERSON><PERSON>ields
from swarm_flows.tools.train.static import FlowId
from swarm_flows.tools.train.static import GitFields
from swarm_flows.tools.train.static import Schema
from swarm_flows.tools.train.static import TaskOverrides
from swarm_flows.tools.train.static import TenantBundle
from swarm_flows.tools.train.static import UpdatedBundle
from swarm_flows.tools.train.static import UpdatedFlow

"""
ASSUMPTION: The Registry is an ES6 cluster. If it is not anymore,
this script must be slightly modified to import
the NotFoundError from the correct ElasticSearch lib.
"""

warnings.filterwarnings(action="ignore", category=InsecureRequestWarning)
warnings.filterwarnings(action="ignore", category=UserWarning)

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s %(message)s",
    datefmt="%Y-%m-%dT%H:%M:%S",
)

logger = logging.getLogger(__name__)
logging.getLogger("elasticsearch").setLevel(logging.ERROR)

IMAGE_TAG = os.environ.get("SWARM_IMAGE_OVERRIDE", "swarm-tasks")
AWS_PROFILE_NAME = os.environ["AWS_PROFILE"].lower()

# use local registry port
REGISTRY_CONFIG = ResourceConfig(
    host=RegistryCluster.HOST,
    scheme=RegistryCluster.SCHEME,
    port=443,
)

FLEET_DICT = {
    "poc-shared-1": "prod-us-ea-1",
    "prod-shared-4": "prod-us-ea-1",
    "prod-bellpotter": "prod-ap-hk-1",
    "prod-citic": "prod-ap-hk-1",
    "uat-citic": "prod-ap-hk-1",
}


class Train:
    """
    Updates or creates bundles and flows. Using the `manifest_path` defined in the script args
     it creates the file path and converts the yaml to dict to update/create flow in the bundle
     and flow indices.
    """

    REFERENCE_META = (
        "id",
        "key",
        "version",
    )
    IMAGE_VERSION_PREFIX = f"{IMAGE_TAG.replace('-', '_')}:"

    def __init__(self, manifest_path: str):
        self.manifest_path = manifest_path
        # create elastic client and registry record handler
        self.es, self.registry = self.create_registry()
        # validate manifest
        self.manifest = Manifest.validate(
            value=yaml.load(
                Path(self.manifest_path).read_text(), Loader=yaml.SafeLoader
            )
        )
        # connect to local repo
        self.repo = Repo(REPO_PATH)

        # assign git user to email
        reader = self.repo.config_reader()
        self.git_user = reader.get_value("user", "email")

        # Map of bundle ids to their paths
        self.bundle_path_map: Dict[str, Path] = {}
        self.env_stacks: List[str] = []

    def run(self):
        """
        Iterates through bundles and update functions, updating `updated_bundle` and `self.updated_flows`
         with information defined in the manifest to update or create bundles and flows
        :return:
        """

        if self.manifest.updates.all_bundles:
            click.confirm(
                f"You are about to update ALL Flows in the {self.manifest.environment} environment "
                f"for the following tenants/stacks: {self.manifest.updates.all_bundles.tenants}. Run_train will use your "
                f"local bundle.yaml files and the swarm-tasks version {self.manifest.swarm_tasks.version}. Are you sure?",
                abort=True,
            )

        if not self.git_user:
            raise AttributeError("Please login to GitHub to proceed")

        logger.info(
            f"Checking if any commits from {GitFields.TO_BE_CHECKED_UPSTREAM} are not in {self.repo.active_branch}"
        )

        # Block deployment if we have uncommitted changes from remote master branch
        commits_diff_from_master = len(
            list(
                self.repo.iter_commits(
                    f"{self.repo.active_branch}..{GitFields.TO_BE_CHECKED_UPSTREAM}"
                )
            )
        )

        if commits_diff_from_master > 0:
            raise ValueError(
                f"Current branch: {self.repo.active_branch} is not synced "
                f"with remote: {GitFields.TO_BE_CHECKED_UPSTREAM}. "
                f"Please do a git pull of master and then run the script."
            )

        # Check local profile aligns with manifest env
        self._check_aws_profile()

        logger.info(f"Logged in as: {self.git_user}")

        if not self.manifest.updates.bundles and not self.manifest.updates.all_bundles:
            logger.info("no bundles to update")
            return

        self._create_bundle_paths()
        self._get_environment_stacks()

        bundles_to_update = []

        if self.manifest.updates.all_bundles:
            bundles_to_update = [
                Bundle(id=x, tenants=self.manifest.updates.all_bundles.tenants)
                for x in self.bundle_path_map.keys()
            ]
        elif self.manifest.updates.bundles:
            bundles_to_update = self.manifest.updates.bundles

        try:
            for bundle in bundles_to_update:
                updated_bundle: dict = self._update_bundle(bundle=bundle)

                self._batch_definitions(
                    version=updated_bundle[UpdatedBundle.BUNDLE_DOC][
                        ESBundleFields.BUNDLE_IMAGE
                    ].replace(Train.IMAGE_VERSION_PREFIX, "")
                )

                updated_flows: Dict[str, addict.Dict] = self._update_flows(
                    updated_bundle=updated_bundle
                )

                self._publish_flows(
                    updated_flows=updated_flows, updated_bundle=updated_bundle
                )

        except RuntimeError:
            logger.error("Train cancelled")

        logger.info(f"Train complete for {self.manifest_path}")

    def _update_bundle(self, bundle: Bundle) -> dict:
        """
        Creates `updated_bundle` and updates it with the following bundle info from manifest
        1. bundle_path - taken from `bundle_path_map`
        2. version - version of bundle if we are rolling back or updating prod
        3. tenants - tenants to update, taken from tenants in bundle and if absent global tenants
        :param bundle:
        :return:
        """

        updated_bundle = {
            UpdatedBundle.BUNDLE_ID: bundle.id,
            UpdatedBundle.BUNDLE_PATH: self.bundle_path_map.get(bundle.id),
            UpdatedBundle.VERSION: bundle.version,
            UpdatedBundle.TENANTS: self._get_bundle_tenants(bundle=bundle),
        }

        # if version for a bundle is present query bundle index for it
        if updated_bundle.get(UpdatedBundle.VERSION):
            updated_bundle[UpdatedBundle.BUNDLE_DOC] = self._fetch_bundle(
                updated_bundle=updated_bundle
            )

            self._validate_prod_reference_data(
                bundle=updated_bundle[UpdatedBundle.BUNDLE_DOC]
            )

            return updated_bundle

        # if bundle version not present get local bundle
        bundle = addict.Dict(
            yaml.load(
                updated_bundle[UpdatedBundle.BUNDLE_PATH].read_text(),
                Loader=yaml.SafeLoader,
            )
        )

        self._validate_prod_reference_data(bundle=bundle)

        # update bundle image
        bundle.image = (
            f"{Train.IMAGE_VERSION_PREFIX}{self.manifest.swarm_tasks.version}"
        )

        # set schema path
        schema_path = updated_bundle[UpdatedBundle.BUNDLE_PATH].parent.joinpath(
            Schema.SCHEMA_CSV
        )

        # read schema if exists
        schema = pd.read_csv(schema_path) if schema_path.exists() else None

        # assign schema to bundle if exists (intentionally post file write)
        if schema is not None and not schema.empty:
            schema = dict(zip(schema.column, schema.get(Schema.PYTHON_TYPE)))
            bundle[Schema.SCHEMA] = schema

        # assign comment
        bundle[ESFields.META][ESFields.COMMENT] = self.manifest.comment

        updated_bundle[UpdatedBundle.BUNDLE] = bundle.to_dict()

        updated_bundle = self._publish_bundle(updated_bundle=updated_bundle)

        return updated_bundle

    def _resolve_flow_overrides(self, bundle: Bundle) -> List[str]:
        """
        Check if the current flow has overrides defined and return the tenants for which they have been defined.

        :param bundle: Bundle definition for which flow overrides is to be resolved.
        :returns: List of tenants for which overrides have been defined.
        """
        tenants_with_override = list()
        bundle_path = self.bundle_path_map.get(bundle.id)
        folders_in_bundle_path = [
            item.stem for item in bundle_path.parent.iterdir() if item.is_dir()
        ]
        if TaskOverrides.FLOW_OVERRIDES_FOLDER in folders_in_bundle_path:
            tenants_with_override.extend(
                [
                    tenant.stem
                    for tenant in Path(bundle_path.parent)
                    .joinpath(TaskOverrides.FLOW_OVERRIDES_FOLDER)
                    .iterdir()
                ]
            )

        return tenants_with_override

    def _get_bundle_tenants(self, bundle: Bundle) -> Optional[List[str]]:
        """
        Returns the list of tenants for the bundle based on the ranking:
        1. bundle.tenants field in manifest
        2. if bundle.all_tenants, fetch the tenants with flows registered for the bundle in env
        3. manifest.updates.tenants
        """

        if bundle.tenants:
            tenants = bundle.tenants
            # Check if a stack level deployment is being done.
            if (
                any(
                    [
                        set(tenant.split("-")).intersection(set(Environments.list()))
                        for tenant in tenants
                    ]
                )
                or "platform" in tenants
            ):
                tenants_with_override = self._resolve_flow_overrides(bundle=bundle)
                if tenants_with_override:
                    if click.confirm(
                        f"Bundle has override defined for {tenants_with_override} tenants. "
                        f"Do you wish to perform a tenant-level deployment for the tenants, "
                        f"including its flow override? "
                        f"PS: If you opt out, you will have to deploy them manually again as stack level "
                        f"deployments do not accommodate for flow-overrides",
                    ):
                        tenants.extend(tenants_with_override)

        elif bundle.all_tenants:
            tenants = self._fetch_tenants_from_flows_ids(bundle_id=bundle.id)
        else:
            tenants = []

        return tenants

    def _fetch_tenants_from_flows_ids(self, bundle_id: str) -> List[str]:
        """
        Returns the list of tenants found with flows registered for the `bundle_id`

        :param bundle_id:
        :return:
        """
        flow_meta_id_field = "_meta.id"

        query = {
            "query": {
                "bool": {
                    "filter": [{"term": {"bundle._meta.id": bundle_id}}],
                    "must_not": {"exists": {"field": "_meta.expiry"}},
                }
            },
            "_source": {"includes": [flow_meta_id_field]},
        }

        # search for archive doc
        results = self.es.scroll(index=ESFlowFields.FLOW_INDEX, query=query)

        if results.empty:
            raise ValueError(f"No flows registered for `{bundle_id}` found in Registry")

        env = (
            f".{self.manifest.environment}"
            if self.manifest.environment != "prod"
            else ""
        )

        realm_pattern = re.compile(rf"^([\w-]+){env}.steeleye.co")
        tenants = (
            results[flow_meta_id_field].str.extract(realm_pattern).dropna().squeeze()
        )

        # single tenant scenario
        if isinstance(tenants, str):
            tenants = pd.Series(tenants)

        if tenants.empty:
            raise ValueError(
                f"No flows registered for `{bundle_id}` found in Registry for env {self.manifest.environment}"
            )
        tenants = sorted(tenants.unique().tolist())

        return tenants

    def _fetch_bundle(self, updated_bundle: dict) -> dict:
        # query to locate bundle by meta version
        bundle_query = {
            "query": {
                "bool": {
                    "filter": [
                        {"term": {"_meta.id": updated_bundle[UpdatedBundle.BUNDLE_ID]}},
                        {
                            "term": {
                                "_meta.version": updated_bundle[UpdatedBundle.VERSION]
                            }
                        },
                    ]
                }
            }
        }

        # search for archive doc
        results = self.es.client.search(
            index=ESBundleFields.INDEX, doc_type="_doc", body=bundle_query
        )

        total_hits = results["hits"]["total"]

        if total_hits != 1:
            raise ValueError(
                f"Expected 1 result for bundle version: {updated_bundle[UpdatedBundle.VERSION]} "
                f"bundle id: {updated_bundle[UpdatedBundle.BUNDLE_ID]} "
                f"but found {total_hits}"
            )

        bundle = results["hits"]["hits"][0]["_source"]

        logger.info(
            f"BUNDLE REGISTRY -> Fetched bundle {updated_bundle[UpdatedBundle.BUNDLE_ID]} \n"
            f"Version: {updated_bundle[UpdatedBundle.VERSION]}"
        )
        return bundle

    def _publish_bundle(self, updated_bundle: dict) -> dict:
        """
        Updates/Publishes bundles from `updated_bundles`
        :param updated_bundle:
        :return:
        """
        try:
            # get current bundle for its version
            doc = self.registry.get(
                index=ESBundleFields.ALIAS,
                model=ESBundleFields.MODEL,
                doc_id=updated_bundle[UpdatedBundle.BUNDLE_ID],
            )

            # assign version
            version = doc[ESFields.META][ESFields.VERSION]

            # update bundle
            response = self.registry.update(
                doc=updated_bundle[UpdatedBundle.BUNDLE],
                alias=ESBundleFields.ALIAS,
                model=ESBundleFields.MODEL,
                version=version,
                merge_previous_version=False,
                user=self.git_user,
            )
            if response.status == ESFields.DUPLICATE:
                logger.warning(
                    f"BUNDLE REGISTRY -> Publishing {updated_bundle[UpdatedBundle.BUNDLE_ID]} \n"
                    f"Bundle version: {version} \n"
                    f"Status: {response.status}"
                )
            else:
                logger.info(
                    f"BUNDLE REGISTRY -> Publishing {updated_bundle[UpdatedBundle.BUNDLE_ID]} \n "
                    f"Bundle version: {version + 1} \n"
                    f"Status: {response.status}"
                )

        except NotFoundError:
            if click.confirm(
                f"{updated_bundle[UpdatedBundle.BUNDLE_ID]} does not exist."
                f"Are you sure you wish to create it?",
                abort=True,
            ):
                # create bundle when `version` is None
                response = self.registry.create(
                    doc=updated_bundle[UpdatedBundle.BUNDLE],
                    alias=ESBundleFields.ALIAS,
                    model=ESBundleFields.MODEL,
                    user=self.git_user,
                )
                logger.info(
                    f"BUNDLE REGISTRY -> Publishing {updated_bundle[UpdatedBundle.BUNDLE_ID]} \n"
                    f"Bundle version: 1"
                    f"Status: {response.status}"
                )

        # retrieve created version of bundle (for meta)
        doc = self.registry.get(
            index=ESBundleFields.ALIAS,
            model=ESBundleFields.MODEL,
            doc_id=updated_bundle[UpdatedBundle.BUNDLE_ID],
        )

        updated_bundle[UpdatedBundle.BUNDLE_DOC] = doc

        return updated_bundle

    def _update_flows(self, updated_bundle: dict) -> Dict[str, addict.Dict]:
        """
        Creates `self.updated_flows`
        key: flow id created from bundle id
        value: the flow
        :param updated_bundle:
        :return:
        """
        if not updated_bundle[UpdatedBundle.TENANTS]:
            return updated_bundle

        updated_flows: Dict[str, addict.Dict] = dict()

        env = self.manifest.environment

        for tenant in updated_bundle[UpdatedBundle.TENANTS]:
            flow_details = NamedTuple(
                "FlowDetails", [(FlowId.FLOW_ID, str), (FlowId.REALM, str)]
            )
            flow_details.realm = (
                FlowId.NON_PROD_REALM_TEMPLATE.format(tenant=tenant, env=env)
                if env != Environments.PROD
                else FlowId.PROD_REALM_TEMPLATE.format(tenant=tenant)
            )
            flow_details.flow_id = ":".join(
                [flow_details.realm, updated_bundle[UpdatedBundle.BUNDLE_ID]]
            )

            bundle_doc = updated_bundle[UpdatedBundle.BUNDLE_DOC]

            # constrain bundle meta
            bundle_doc[ESFields.META] = self.prune_meta(meta=bundle_doc[ESFields.META])

            flow = {
                UpdatedFlow.REALM: flow_details.realm,
                UpdatedFlow.BUNDLE: bundle_doc,
            }

            # Create flow config path
            flow_config_path = Path(
                updated_bundle[UpdatedBundle.BUNDLE_PATH].parent.joinpath(
                    TaskOverrides.FLOW_OVERRIDES_FOLDER,
                    TenantBundle.TENANT_YAML_TEMPLATE.format(tenant=tenant),
                )
            )
            # Create list of task overrides if present for the tenant
            task_overrides = self._get_task_overrides(
                tenant_config_path=flow_config_path, env=env
            )
            if task_overrides:
                flow[TaskOverrides.TASK_OVERRIDES] = task_overrides
                logger.warning(
                    f"Task overrides have been found locally and will be published to the flow:{task_overrides}"
                )

            updated_flows[flow_details.flow_id] = addict.Dict(flow)

        return updated_flows

    def _publish_flows(
        self, updated_flows: Dict[str, addict.Dict], updated_bundle: dict
    ):
        """
        Publishes flows with `self.updated_flows`
        key: flow id (realm + bundle id)
        value: flow (actual flow)
        :param updated_flows:
        :param updated_bundle:
        :return:
        """
        if not updated_bundle[UpdatedBundle.TENANTS]:
            return

        for flow_id, flow in updated_flows.items():
            try:
                # retrieve current version of flow
                doc = self.registry.get(
                    index=ESFlowFields.FLOW_ALIAS,
                    model=ESFlowFields.MODEL,
                    doc_id=flow_id,
                )

                current_version = doc[ESFields.META][ESFields.VERSION]

                # assign comment
                flow[ESFields.META][ESFields.COMMENT] = self.manifest.comment

                response = self.registry.update(
                    doc=flow,
                    alias=ESFlowFields.FLOW_ALIAS,
                    model=ESFlowFields.MODEL,
                    version=current_version,
                    merge_previous_version=False,
                    user=self.git_user,
                )
                if response.status == "DUPLICATE":
                    logger.warning(
                        f"FLOW REGISTRY -> Publishing {flow_id} \n"
                        f"Flow version: {doc[ESFields.META][ESFields.VERSION]} \n"
                        f"Current bundle version: {doc[ESBundleFields.BUNDLE_FIELD][ESFields.META][ESFields.VERSION]} \n"
                        f"Updated bundle version: {flow[ESBundleFields.BUNDLE_FIELD][ESFields.META][ESFields.VERSION]} \n"
                        f"Updated bundle image: {flow[ESBundleFields.BUNDLE_FIELD][ESBundleFields.BUNDLE_IMAGE]} \n"
                        f"Status: {response.status}"
                    )
                else:
                    logger.info(
                        f"FLOW REGISTRY -> Publishing {flow_id} \n"
                        f"Flow version: {doc[ESFields.META][ESFields.VERSION] + 1} \n"
                        f"Current bundle version: {doc[ESBundleFields.BUNDLE_FIELD][ESFields.META][ESFields.VERSION]} \n"
                        f"Updated bundle version: "
                        f"{flow[ESBundleFields.BUNDLE_FIELD][ESFields.META][ESFields.VERSION]} \n"
                        f"Updated bundle image: {flow[ESBundleFields.BUNDLE_FIELD][ESBundleFields.BUNDLE_IMAGE]} \n"
                        f"Status: {response.status}"
                    )

            except NotFoundError:
                # create flow when not found
                click.confirm(
                    f"{flow_id} does not currently exist, are you sure you wish to create it?",
                    abort=True,
                )
                response = self.registry.create(
                    doc=flow,
                    alias=ESFlowFields.FLOW_ALIAS,
                    model=ESFlowFields.MODEL,
                    user=self.git_user,
                )
                logger.info(
                    f"FLOW REGISTRY -> Publishing {flow_id} \n flow version: 1 \n "
                    f"bundle version: {flow[ESBundleFields.BUNDLE_FIELD][ESFields.META][ESFields.VERSION]}) \n"
                    f"Status: {response.status}"
                )

    def _create_bundle_paths(self):
        """
        Updates `self.bundle_path_map` with a map of bundle.id to the bundles path for use
        downstream
        :return:
        """
        bundle_ids = []
        deploy_all_bundles = True

        if self.manifest.updates.bundles:
            bundle_ids = [bundle.id for bundle in self.manifest.updates.bundles]
            deploy_all_bundles = False

        for root, dirs, files in os.walk(BUNDLES_PATH):
            root_path = Path(root)
            bundle_paths = [
                root_path.joinpath(f) for f in files if f == BundleFileName.BUNDLE
            ]
            for bundle_path in bundle_paths:
                bundle = addict.Dict(
                    yaml.load(
                        bundle_path.read_text(encoding="utf-8"), Loader=yaml.SafeLoader
                    )
                )
                if not deploy_all_bundles and bundle.id not in bundle_ids:
                    continue

                self.bundle_path_map[bundle.id] = bundle_path

        if not deploy_all_bundles:
            bundle_diff = set(bundle_ids).difference(
                set(list(self.bundle_path_map.keys()))
            )

            if bundle_diff:
                click.confirm(
                    f"Bundles {bundle_diff} do not exist locally, do you wish to continue?",
                    abort=True,
                )

    def _validate_prod_reference_data(self, bundle: dict):
        """
        If the flow is being published into PROD, this validates that the bundle
        infra resource reference-data (if any) is pointing to reference-data prod
        (not reference-data-uat or reference-data-dev).

        :param bundle: Bundle record
        :type bundle: dict
        """
        if self.manifest.environment != "prod":
            return

        for resource in bundle["infra"]:
            if resource["type"] == "ELASTICSEARCH" and resource["name"].startswith(
                "reference-data"
            ):
                if resource["name"] != "reference-data":
                    raise ValueError(
                        "ValueError: In PROD env, a flow can only connect to reference-data in prod srp. "
                        "Please fix the reference-data defined in infra section of the bundle to point to prod `reference-data`."
                    )

    @staticmethod
    def _get_task_overrides(tenant_config_path: Path, env: str) -> Optional[List[Any]]:
        """
        Creates list of task overrides from tenant config path and env
        :param tenant_config_path:
        :param env:
        :return:
        """
        if not os.path.exists(tenant_config_path):
            return

        flow_config = yaml.load(tenant_config_path.read_text(), Loader=yaml.SafeLoader)
        task_overrides = flow_config.get(TaskOverrides.TASK_OVERRIDES)

        if not task_overrides or not (
            task_overrides.get(TaskOverrides.ALL_ENVIRONMENTS)
            or task_overrides.get(env)
        ):
            return

        all_overrides = list()
        if TaskOverrides.ALL_ENVIRONMENTS in task_overrides:
            all_overrides.extend(task_overrides.get(TaskOverrides.ALL_ENVIRONMENTS))
        if env in task_overrides:
            all_overrides.extend(task_overrides.get(env))

        return all_overrides

    @staticmethod
    def prune_meta(meta: dict) -> dict:
        """
        Removes unwanted meta key value pairs
        :param meta:
        :return:
        """
        item = dict([item for item in meta.items() if item[0] in Train.REFERENCE_META])
        return item

    def _get_environment_stacks(self):
        """
        Update class var `stacks` with the environments stacks from client elastic index
        :return:
        """
        env = self.manifest.environment

        clients = self.es.scroll(index="client", query=dict(query=dict(match_all={})))

        stacks = (
            clients[[col for col in clients.columns if col.startswith("stacks.")]]
            .stack()
            .unique()
            .tolist()
        )

        self.stacks = (
            [
                stack
                for stack in stacks
                if stack.startswith(f"{env}-")
                or (env == "prod" and stack.startswith("srp"))
                or (env == "prod" and stack.startswith("uat-fidelity"))
            ]
            # Check for citic to ensure that we don't deploy the task with other stacks name
            # which doesn't exist in citic account
            if "citic" not in AWS_PROFILE_NAME
            else ["prod-citic" if env == "prod" else "uat-citic"]
        )

    def _check_aws_profile(self):
        """
        Checks manifest environment against active local aws profile
        :return:
        """
        sts = boto3.client("sts")

        # Citic tenant has a separate account so check for citic in the profile name.
        # Some uses the name as citic_infra and some citic.
        env_profile_acc = (
            "************"
            if self.manifest.environment == "prod"
            else "************"
            if "citic" in AWS_PROFILE_NAME
            else "************"
        )
        active_profile_acc = sts.get_caller_identity().get("Account")

        if env_profile_acc != active_profile_acc:
            raise AttributeError("AWS_PROFILE and manifest environment do not align")

    def _create_job_definition(
        self,
        job_definition_name: str,
        version: str,
        stack: str,
    ):
        """
        Creates batch job definition
        """
        ecr_repo = "************.dkr.ecr.eu-west-1.amazonaws.com"

        # Citic tenant has a separate account.
        ein = (
            "************"
            if self.manifest.environment == "prod"
            else "************"
            if "citic" in AWS_PROFILE_NAME
            else "************"
        )

        is_prod = self.manifest.environment not in [
            "dev",
            "uat",
            "sit",
            "benchmark",
            "poc",
        ]
        is_dev = self.manifest.environment == "dev"

        # All non-prod environments use the same UAT SFTP proxy.
        sftp_proxy_host = (
            "proxy.enterprise.steeleye.co"
            if is_prod
            else "proxy.uat-enterprise.steeleye.co"
        )

        try:
            fleet = FLEET_DICT[stack]
        except KeyError:
            fleet = "prod-eu-ie-1" if is_prod else "nonprod-eu-ie-1"

        # The dev environment is the only environment that uses the dev master/market data APIs and creds
        cognito_auth_url = (
            "https://prod-master-data.auth.eu-west-1.amazoncognito.com/oauth2/token"
            if not is_dev
            else "https://dev-master-data.auth.eu-west-1.amazoncognito.com/oauth2/token"
        )
        cognito_client_id = (
            "1c7hapqudk6ab5npoubfttc0ma" if not is_dev else "627uaclfb5188f6ckimiup83i4"
        )
        cognito_client_secret = (
            "1lakej5lmkro0nv2nohuibnv83ki38qij2vrldlsv2enl4r85v3s"
            if not is_dev
            else "13likcf06r341hnlr6fp8qp1gd550pm7jdkq71p3oj5rh34i2ouo"
        )
        market_data_api_url = (
            "https://api.market-data.steeleye.co"
            if not is_dev
            else "https://api.dev-market-data.steeleye.co"
        )
        master_data_host = (
            "https://api.master-data.steeleye.co"
            if not is_dev
            else "https://api.dev-master-data.steeleye.co"
        )
        master_data_api_host = (
            "https://api.master-data.steeleye.co"
            if not is_dev
            else "https://api.dev-master-data.steeleye.co"
        )

        env_vars = [
            {"name": "AWS_DEFAULT_REGION", "value": "eu-west-1"},
            {"name": "PREFECT__FLOWS__CHECKPOINTING", "value": "true"},
            {"name": "STACK", "value": stack},
            {
                "name": "OBD_DATA_STORAGE_BUCKET",
                "value": "s3://master-data.eu-west-1.steeleye.co",
            },
            {
                "name": "COGNITO_AUTH_URL",
                "value": cognito_auth_url,
            },
            {
                "name": "COGNITO_CLIENT_ID",
                "value": cognito_client_id,
            },
            {
                "name": "COGNITO_CLIENT_SECRET",
                "value": cognito_client_secret,
            },
            {
                "name": "MARKET_DATA_API_URL",
                "value": market_data_api_url,
            },
            {
                "name": "MASTER_DATA_HOST",
                "value": master_data_host,
            },
            {
                "name": "MASTER_DATA_API_HOST",
                "value": master_data_api_host,
            },
            {
                "name": "KAFKA_SECURITY_PROTOCOL",
                "value": "PLAINTEXT",
            },
            {
                "name": "SFTP_PROXY_HOST",
                "value": sftp_proxy_host,
            },
            {
                "name": "SFTP_PROXY_PORT",
                "value": "8080",
            },
            {
                "name": "VAULT_ELASTIC_PATH",
                "value": "se/elasticsearch",
            },
            {
                "name": "VAULT_URL",
                "value": f"https://vault.{fleet}.steeleye.co/",
            },
        ]

        if self.manifest.environment == "prod":
            bundle_names = self.bundle_path_map.keys()
            if bundle_names and next(iter(bundle_names)) == "order-feed-enfusion-v2":
                env_vars += [
                    {
                        "name": "MAX_PREFECT_TASKS",
                        "value": "8",
                    },
                ]

        if self.manifest.environment in ["dev", "uat", "sit", "benchmark", "poc"]:

            bundle_names = self.bundle_path_map.keys()
            if bundle_names and next(iter(bundle_names)) == "order-bbg-emsi-controller":
                env_vars += [
                    {
                        "name": "INSTRUMENT_STORE_BUCKET",
                        "value": "uat-srp.uat.steeleye.co",
                    },
                ]
            if self.manifest.environment == "uat":
                bundle_names = self.bundle_path_map.keys()
                if (
                    bundle_names
                    and next(iter(bundle_names)) == "order-feed-lme-select-fix"
                ):
                    env_vars += [
                        {
                            "name": "USE_TENANT_DATA_IN_VDI",
                            "value": "1",
                        },
                    ]

        if self.manifest.updates.env_vars:
            for bundle_vars in self.manifest.updates.env_vars:
                env_var_dict = {
                    "name": next(iter(bundle_vars.keys())),
                    "value": next(iter(bundle_vars.values())),
                }
                env_vars.append(env_var_dict)

        job_definition = {
            "jobDefinitionName": job_definition_name,
            "type": "container",
            "containerProperties": {
                "image": f"{ecr_repo}/{IMAGE_TAG}:{version}",
                "vcpus": 1,
                "memory": 1024 * 7,
                "command": ["flow_runner"],
                "jobRoleArn": f"arn:aws:iam::{ein}:role/{stack}-tasks",
                "environment": env_vars,
            },
            "tags": {"se_flow": "1"},
        }

        return job_definition

    def _batch_definitions(self, version: str):
        """
        Check batch job definition exists and if not, create it. Log what stacks had job def and what stacks registered
        a job def.
        :param version: version of image
        :return:
        """

        batch_eu_west_1 = boto3.client("batch")
        batch_us_east_1 = boto3.client("batch", region_name="us-east-1")

        stack_with_job_def = []
        stacks_without_job_def = []
        for stack in self.stacks:
            # Select the right Batch client
            batch_client = (
                batch_us_east_1
                if stack in ["prod-shared-4", "poc-shared-1"]
                else batch_eu_west_1
            )
            job_definition_name = f"{stack}-{IMAGE_TAG}-v{version.replace('.', '-')}"
            response = batch_client.describe_job_definitions(
                maxResults=1, jobDefinitionName=job_definition_name, status="ACTIVE"
            )
            job_definition = self._create_job_definition(
                job_definition_name=job_definition_name,
                stack=stack,
                version=version,
            )

            # Check if we have to update the existing batch job definition with new env vars
            if response.get("jobDefinitions"):
                deployed_env_vars_list = (
                    next(iter(response.get("jobDefinitions")))
                    .get("containerProperties")
                    .get("environment")
                )
                deployed_env_vars = {
                    i.get("name"): i.get("value") for i in deployed_env_vars_list
                }
                to_be_deployed_env_var_list = job_definition.get(
                    "containerProperties"
                ).get("environment")
                to_be_deployed_env_vars = {
                    i.get("name"): i.get("value") for i in to_be_deployed_env_var_list
                }
                update_flag = 0
                for key, value in to_be_deployed_env_vars.items():
                    if deployed_env_vars.get(key) != value:
                        update_flag = 1
                        break

                if not update_flag:
                    stack_with_job_def.append(stack)
                    continue

            batch_client.register_job_definition(**job_definition)

            stacks_without_job_def.append(stack)

        if stack_with_job_def:
            logger.info(
                f"BATCH JOB VERSION -> {version} (stacks: {stack_with_job_def} | env: {self.manifest.environment})"
                f" | Status: EXISTS"
            )
        if stacks_without_job_def:
            logger.info(
                f"BATCH JOB VERSION -> {version} (stacks: {stacks_without_job_def} | env: {self.manifest.environment})"
                f" | Status: REGISTERED"
            )

    @staticmethod
    def create_registry() -> (AnyElasticsearchRepositoryType, AbstractRecordHandler):
        """
        Creates connection to registry for flow and bundle update/publishing
        """

        # create elastic client for registry
        es = get_repository_by_cluster_version(resource_config=REGISTRY_CONFIG)

        # create record handler for registry
        registry = init_record_handler(client=es.client)

        return es, registry


@click.command("run_train")
@click.option("--manifest-path", default="manifests/manifest.yaml", type=str)
def run_train(manifest_path: str):
    """
    Run function called when script is executed
    :param manifest_path: File path where the manifest of bundle updates is located
    :return:
    """
    logger.info(f"Running train using manifest: {manifest_path}")
    train = Train(manifest_path=manifest_path)
    train.run()


if __name__ == "__main__":
    run_train()
