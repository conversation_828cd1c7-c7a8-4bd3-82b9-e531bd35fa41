# Confluence page: https://steeleye.atlassian.net/wiki/spaces/IN/pages/1436909577/Order+Mercury+Beacon
id: order-feed-mercury-beacon
name: Order Mercury Beacon
infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: reference-data
  type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
- name: file_url
  envVar: SWARM_FILE_URL
  description: url of the file to process
controlFlows:
- name: S3OrLocalFile
  conditionTaskName: ParametersFlowController
  trueTaskName: S3DownloadFile
  falseTaskName: LocalFile
  merge: true
- name: NeedsQuarantine
  conditionTaskName: QuarantineCondition
  trueTaskName: QuarantinedElasticBulkTransformer
  falseTaskName: Noop
  mapped: true
  merge: false
tasks:
- path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
  name: ParametersFlowController
  params:
    arg_name: file_url
    starts_with: s3://
    condition: AND
    ignore_case: true
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
  name: S3DownloadFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.local_file:LocalFile
  name: LocalFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.xls_to_csv_converter:XlsToCsvConverter
  name: XlsToCsvConverter
  upstreamTasks:
    - taskName: S3OrLocalFile
      mapped: false
      key: extractor_result
- path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
  name: CsvFileSplitter
  params:
    chunksize: 25000
  upstreamTasks:
    - taskName: XlsToCsvConverter
      mapped: false
      key: extractor_result
- path: swarm_tasks.io.read.batch_producer:BatchProducer
  name: BatchProducer
  upstreamTasks:
  - taskName: CsvFileSplitter
    mapped: true
    flatten: true
    key: file_splitter_result
  # primary transformations
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: MapExpiryDate
  paramsList:
    - target_attribute: __expirydate_ifnot_fxcfd__
      # Map to pd.NA if Type = X or an invalid value
      cases:
        - query: "`Type`.str.upper().isin(['P','C','F'])"
          attribute: Expiration
  upstreamTasks:
  - taskName: BatchProducer
    mapped: true
    key: result
  # Create a temp column for Timestamp because it is a keyword and causes issues in ConvertDatetime
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: MapTempTimestamp
  params:
    source_attribute: Timestamp
    target_attribute: __timestamp__
  upstreamTasks:
  - taskName: BatchProducer
    mapped: true
    key: result
- path: swarm_tasks.transform.datetime.convert_datetime:ConvertDatetime
  name: ConvertDatetime
  paramsList:
    - source_attribute: __timestamp__
      target_attribute: date
      convert_to: date
    - source_attribute: __expirydate_ifnot_fxcfd__
      target_attribute: __expiry_date__
      convert_to: date
      source_attribute_format: "%Y%m%d"
    - source_attribute: __timestamp__
      target_attribute: timestamps.tradingDateTime
      convert_to: datetime
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result
    - taskName: MapExpiryDate
      mapped: true
      key: expirydate
    - taskName: MapTempTimestamp
      mapped: true
      key: temptimestamp
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: MapAttribute
  paramsList:
  - source_attribute: ContraOrderID
    target_attribute: orderIdentifiers.internalOrderIdCode
  - source_attribute: Symbol
    target_attribute: __currency__
    start_index: -3
  - source_attribute: Symbol
    target_attribute: __underlying_symbol__
    start_index: 0
    end_index: 3
  - source_attribute: Symbol
    target_attribute: __notional_currency2__
    start_index: 0
    end_index: 3
  - source_attribute: OrderQuantity
    target_attribute: priceFormingData.initialQuantity
    cast_to: numeric.absolute
  - source_attribute: LegNumber
    target_attribute: __leg_number__
    cast_to: string.upper
  - source_attribute: Description
    target_attribute: __additional_info_name__
    prefix: "name:"
  - source_attribute: Route
    target_attribute: __additional_info_route__
    prefix: "route:"
  - source_attribute: OrderRefPrice
    target_attribute: __additional_info_ref_price__
    prefix: "order ref price:"
    # fields for populating party identifiers
  - source_attribute: Username
    target_attribute: __trader_id__
    prefix: "id:"
  - source_attribute: Account
    target_attribute: __client_identifier__
    prefix: "id:"
  - source_attribute: Destination
    target_attribute: __counterparty_identifier__
    prefix: "id:"
  upstreamTasks:
  - taskName: BatchProducer
    mapped: true
    key: result
- path: swarm_tasks.transform.map.map_value:MapValue
  name: MapValue
  paramsList:
    - source_attribute: LegSide
      target_attribute: transactionDetails.buySellIndicator
      case_insensitive: true
      value_map:
        b: BUYI
        buy: BUYI
        buy to cover: BUYI
        buytocover: BUYI
        cover: BUYI
        s: SELL
        sell: SELL
        sell short: SELL
        short: SELL
        ss: SELL
    - source_attribute: LegSide
      target_attribute: _order.buySell
      case_insensitive: true
      value_map:
        b: "1"
        buy: "1"
        buy to cover: "3"
        buytocover: "3"
        cover: "3"
        s: "2"
        sell: "2"
        sell short: "5"
        short: "5"
        ss: "5"
    - source_attribute: Type
      target_attribute: __asset_class__
      case_insensitive: true
      value_map:
        F: future
        P: option
        C: option
        X: fx cfd
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result
- path: swarm_tasks.transform.map.map_static:MapStatic
  name: MapStatic
  paramsList:
  - target_attribute: _order.__meta_model__
    target_value: Order
  - target_attribute: orderState.__meta_model__
    target_value: OrderState
  - target_attribute: _order.executionDetails.orderStatus
    target_value: NEWO
  - target_attribute: orderState.executionDetails.orderStatus
    target_value: FILL
  - target_attribute: orderState.sourceKey
    from_env_var: SWARM_FILE_URL
  - target_attribute: orderState.sourceIndex
    from_index: true
  - target_attribute: _order.sourceKey
    from_env_var: SWARM_FILE_URL
  - target_attribute: _order.sourceIndex
    from_index: true
  - target_attribute: dataSourceName
    target_value: Mercury Beacon
  - target_attribute: executionDetails.tradingCapacity
    target_value: AOTC
  - target_attribute: transactionDetails.venue
    target_value: XOFF
  - target_attribute: transactionDetails.quantityNotation
    target_value: UNIT
  - target_attribute: transactionDetails.priceNotation
    target_value: MONE
  - target_attribute: transactionDetails.recordType
    target_value: Market Side
  - target_attribute: executionDetails.orderType
    target_value: Market
  - target_attribute: __multileg__
    target_value: "|MULTILEG|"
  - target_attribute: __strike_price_type__
    target_value: MntryVal
  - target_attribute: __strike_price_ccy__
    target_value: USD
  upstreamTasks:
  - taskName: BatchProducer
    mapped: true
    key: result
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: MapConditional
  paramsList:
    - target_attribute: _order.id
      cases:
        - query: "index == index"
          attribute: OrderID
        - query: "`LegCount` > 1"
          attribute: __order_id__
    - target_attribute: __price_currency__
      cases:
        - query: "`Type`.str.upper().isin(['F','X'])"
          value: "USD"
        - query: "`Type`.str.upper().isin(['P','C'])"
          attribute: __notional_currency2__
    - target_attribute: __venue__
      cases:
        - query: "`Type`.str.upper().isin(['P','C','F'])"
          value: "XCME"
        - query: "~`Type`.str.upper().isin(['P','C','F'])"
          value: "XXXX"
    - target_attribute: __inputcurrency__
      cases:
        - query: "`Type`.str.upper().isin(['P','C','F'])"
          attribute: __currency__
        - query: "~`Type`.str.upper().isin(['P','C','F'])"
          value: "USD"
  upstreamTasks:
  - taskName: BatchProducer
    mapped: true
    key: result
  - taskName: ConcatAttributes
    mapped: true
    key: concat_attributes
  - taskName: ConvertDatetime
    mapped: true
    key: convert_date_time
  - taskName: MapAttribute
    mapped: true
    key: map_attribute
- path: swarm_tasks.generic.currency.convert_minor_to_major:ConvertMinorToMajor
  name: ConvertMinorToMajor
  paramsList:
    # Currencies
    - source_ccy_attribute: __price_currency__
      target_ccy_attribute: transactionDetails.priceCurrency
    # Prices
    - source_price_attribute: LegPrice
      source_ccy_attribute: transactionDetails.priceCurrency
      target_price_attribute: orderState.priceFormingData.price
      cast_to: abs
    - source_price_attribute: Strike
      source_ccy_attribute: transactionDetails.priceCurrency
      target_price_attribute: __strike_price__
      cast_to: abs
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result
    - taskName: MapConditional
      mapped: true
      key: map_conditional
- path: swarm_tasks.transform.concat.concat_attributes:ConcatAttributes
  name: ConcatAttributes
  paramsList:
    - source_attributes:
        - OrderID
        - __multileg__
        - __leg_number__
      target_attribute: __order_id__
    - source_attributes:
        - __additional_info_name__
        - __additional_info_route__
        - __additional_info_ref_price__
      target_attribute: executionDetails.outgoingOrderAddlInfo
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result
    - taskName: MapAttribute
      mapped: true
      key: map_attribute
    - taskName: MapStatic
      mapped: true
      key: map_static
- path: swarm_tasks.transform.map.map_static:MapStatic
  name: MapExecutingEntity
  params:
    target_attribute: Executing Entity ID
    target_value: __placeholder__
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result
- path: swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers:GenericOrderPartyIdentifiers
  name: PartyIdentifiers
  params:
    target_attribute: marketIdentifiers.parties
    executing_entity_identifier: __exec_entity_id__
    counterparty_identifier: __counterparty_identifier__
    buyer_identifier: __client_identifier__
    seller_identifier: __counterparty_identifier__
    trader_identifier: __trader_id__
    client_identifier: __client_identifier__
    execution_within_firm_identifier: __trader_id__
    buy_sell_side_attribute: transactionDetails.buySellIndicator
    use_buy_mask_for_buyer_seller: true
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result
    - taskName: MapAttribute
      mapped: true
      key: map_attribute
    - taskName: MapValue
      mapped: true
      key: map_value
    - taskName: MapExecutingEntity
      mapped: true
      key: map_exec_entity
- path: swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers:InstrumentIdentifiers
  name: InstrumentIdentifiers
  params:
    currency_attribute: __inputcurrency__
    expiry_date_attribute: __expiry_date__
    option_strike_price_attribute: __strike_price__
    option_type_attribute: Type
    underlying_symbol_attribute: __underlying_symbol__
    notional_currency_2_attribute: __notional_currency2__
    asset_class_attribute: __asset_class__
    venue_attribute: __venue__
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result
    - taskName: MapAttribute
      mapped: true
      key: map_attribute
    - taskName: MapValue
      mapped: true
      key: map_value
    - taskName: MapStatic
      mapped: true
      key: map_static
    - taskName: MapConditional
      mapped: true
      key: map_conditional
    - taskName: ConvertDatetime
      mapped: true
      key: convert_date_time
    - taskName: ConvertMinorToMajor
      mapped: true
      key: convert_minor_major
- path: swarm_tasks.transform.steeleye.link.merge_market_identifiers:MergeMarketIdentifiers
  name: MergeMarketIdentifiers
  params:
    identifiers_path: marketIdentifiers
    instrument_path: marketIdentifiers.instrument
    parties_path: marketIdentifiers.parties
  upstreamTasks:
    - taskName: InstrumentIdentifiers
      mapped: true
      key: result
    - taskName: PartyIdentifiers
      mapped: true
      key: party_identifiers
# primary frame concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PrimaryFrameConcatenator
  params:
    orient: horizontal
  upstreamTasks:
  - taskName: ConvertDatetime
    mapped: true
    key: convert_date_time
  - taskName: ConvertMinorToMajor
    mapped: true
    key: convert_minor_major
  - taskName: MapAttribute
    mapped: true
    key: map_attribute
  - taskName: MapStatic
    mapped: true
    key: map_static
  - taskName: ConcatAttributes
    mapped: true
    key: concat_attributes
  - taskName: MapValue
    mapped: true
    key: map_value
  - taskName: MapExpiryDate
    mapped: true
    key: map_expirydate
  - taskName: MapConditional
    mapped: true
    key: map_conditional
  - taskName: PartyIdentifiers
    mapped: true
    key: party_identifiers
  - taskName: InstrumentIdentifiers
    mapped: true
    key: instrument_identifiers
  - taskName: MergeMarketIdentifiers
    mapped: true
    key: merge_market_ids
- path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
  name: ParentId
  params:
    parent_model_attribute: _order.__meta_model__
    parent_attributes_prefix: _order.
    target_attribute: orderState.__meta_parent__
  upstreamTasks:
  - taskName: PrimaryFrameConcatenator
    mapped: true
    key: result
- path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
  name: LinkInstrument
  params:
    identifiers_path: marketIdentifiers.instrument
    currency_attribute: __inputcurrency__
    asset_class_attribute: __asset_class__
    venue_attribute: __venue__
  resources:
    ref_data_key: reference-data
    tenant_data_key: tenant-data
  upstreamTasks:
  - taskName: PrimaryFrameConcatenator
    mapped: true
    key: result
- path: swarm_tasks.transform.steeleye.link.parties:LinkParties
  name: LinkParties
  params:
    identifiers_path: marketIdentifiers.parties
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: PrimaryFrameConcatenator
    mapped: true
    key: result
- path: swarm_tasks.order.feed.mercury.beacon.instrument_fallback:InstrumentFallback
  name: InstrumentFallback
  upstreamTasks:
  - taskName: LinkInstrument
    mapped: true
    key: link_instrument
  - taskName: PrimaryFrameConcatenator
    mapped: true
    key: result
  - taskName: BatchProducer
    mapped: true
    key: batch_producer
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: AuxiliaryMapAttributes
  paramsList:
  - source_attribute: _order.id
    target_attribute: reportDetails.transactionRefNo
  - source_attribute: _order.id
    target_attribute: orderState.orderIdentifiers.transactionRefNo
  - source_attribute: _order.id
    target_attribute: orderIdentifiers.orderIdCode
  - source_attribute: _order.id
    target_attribute: orderState.id
  - source_attribute: _order.buySell
    target_attribute: orderState.buySell
  - source_attribute: transactionDetails.buySellIndicator
    target_attribute: executionDetails.buySellIndicator
  - source_attribute: timestamps.tradingDateTime
    target_attribute: transactionDetails.tradingDateTime
  - source_attribute: timestamps.tradingDateTime
    target_attribute: timestamps.orderReceived
  - source_attribute: timestamps.tradingDateTime
    target_attribute: _order.timestamps.orderStatusUpdated
  - source_attribute: timestamps.tradingDateTime
    target_attribute: orderState.timestamps.orderStatusUpdated
  - source_attribute: timestamps.tradingDateTime
    target_attribute: timestamps.orderSubmitted
  - source_attribute: orderState.priceFormingData.price
    target_attribute: orderState.transactionDetails.priceAverage
  - source_attribute: orderState.priceFormingData.price
    target_attribute: orderState.transactionDetails.price
  - source_attribute: priceFormingData.initialQuantity
    target_attribute: priceFormingData.tradedQuantity
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      mapped: true
      key: result
# Auxiliary frame conatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: AuxiliaryFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
    - marketIdentifiers.instrument
    - marketIdentifiers.parties
    - __currency__
    - __inputcurrency__
    - __multileg__
    - __leg_number__
    - __additional_info_name__
    - __additional_info_route__
    - __additional_info_ref_price__
    - __trader_id__
    - __client_identifier__
    - __counterparty_identifier__
    - __asset_class__
    -  __expirydate_ifnot_fxcfd__
    - __expiry_date__
    - __order_id__
    - __price_currency__
    - __notional_currency2__
    - __strike_price__
    - __underlying_symbol__
    - __venue__
    - __strike_price_type__
    - __strike_price_ccy__
    - __exec_entity_id__
  upstreamTasks:
  - taskName: PrimaryFrameConcatenator
    mapped: true
    key: primary_frame_concatenator
  - taskName: ParentId
    mapped: true
    key: parent_id
  - taskName: LinkParties
    mapped: true
    key: link_parties
  - taskName: InstrumentFallback
    mapped: true
    key: instrument_fallback
  - taskName: AuxiliaryMapAttributes
    mapped: true
    key: auxiliary_map_attributes
# Filter only OrderRecords into a frame
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderRecords
  params:
    except_prefix: orderState.
    strip_prefix: true
  upstreamTasks:
  - taskName: AuxiliaryFrameConcatenator
    mapped: true
    key: result
# Filter only OrderStateRecords into a frame
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderStateRecords
  params:
    except_prefix: _order.
    strip_prefix: true
  upstreamTasks:
  - taskName: AuxiliaryFrameConcatenator
    mapped: true
    key: result
# Strip prefix `_order.` from the column names of OrderRecords frame.
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrder
  params:
    action: strip
    prefix: _order.
  upstreamTasks:
  - taskName: OrderRecords
    mapped: true
    key: result
# Strip prefix `orderState.` from the column names of OrderState Records frame.
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrderState
  params:
    action: strip
    prefix: orderState.
  upstreamTasks:
  - taskName: OrderStateRecords
    mapped: true
    key: result
# Vertically concatenate Order and OrderState Record frames created above.
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: VerticalConcatenator
  params:
    orient: vertical
    reset_index: true
    drop_index: true
  upstreamTasks:
  - taskName: StripPrefixOrder
    mapped: true
    key: order_records
  - taskName: StripPrefixOrderState
    mapped: true
    key: order_state_records
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: RemoveInvalidOrderStates
  params:
    query: "`executionDetails.orderStatus`.notnull()"
  upstreamTasks:
  - taskName: VerticalConcatenator
    mapped: true
    key: result
- path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
  name: Meta
  params:
    model_attribute: __meta_model__
    parent_attribute: __meta_parent__
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: RemoveInvalidOrderStates
    mapped: true
    key: result
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: OrdersAndOrderStatesFinalConcatenator
  params:
    orient: horizontal
    drop_columns:
    - __meta_model__
    - __meta_parent__
  upstreamTasks:
  - taskName: Meta
    mapped: true
    key: result
  - taskName: RemoveInvalidOrderStates
    mapped: true
    key: remove_dup_newo
# Best-execution tasks
- path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
  name: BestExecution
  resources:
    es_client_key: reference-data
  upstreamTasks:
  - taskName: OrdersAndOrderStatesFinalConcatenator
    mapped: true
    key: result
# Best-Ex results concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: BestExecutionConcatenator
  params:
    orient: horizontal
  upstreamTasks:
  - taskName: BestExecution
    mapped: true
    key: best_ex_result
  - taskName: OrdersAndOrderStatesFinalConcatenator
    mapped: true
    key: orders_and_orderstates_final
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: ElasticBulkTransformer
  params:
    action_type: create
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: BestExecutionConcatenator
    mapped: true
    key: transform_result
  - taskName: BatchProducer
    mapped: true
    key: producer_result
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: PutIfAbsent
  params:
    payload_size: 10000000
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: ElasticBulkTransformer
    mapped: true
    key: result
# Instrument Mapper
- path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
  name: InstrumentMapper
  upstreamTasks:
    - taskName: BestExecutionConcatenator
      mapped: true
      key: source_frame
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
  name: QuarantineCondition
  upstreamTasks:
  - taskName: PutIfAbsent
    mapped: true
    key: bulk_writer_result
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: QuarantinedElasticBulkTransformer
  params:
    action_type: create
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: BestExecutionConcatenator
    mapped: true
    key: transform_result
  - taskName: BatchProducer
    mapped: true
    key: producer_result
  - taskName: PutIfAbsent
    mapped: true
    key: bulk_writer_result
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: QuarantinedPutIfAbsent
  params:
    payload_size: 10000000
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: QuarantinedElasticBulkTransformer
    mapped: true
    key: result
- path: swarm_tasks.control_flow.noop:Noop
  name: Noop
