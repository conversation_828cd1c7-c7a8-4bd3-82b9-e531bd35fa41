# Input: Csv file which contains S3 links to a batch of TT fix files.
# All the fix files are downloaded locally, and processed together in one flow run.

# Methodology - Parse FIX messages; build Pandas DataFrame with relevant data;
# Map data to fit Order and OrderState models; Follow order-universal-steeleye-trade-blotter pipeline
# Output - Write Order and OrderState trades to the associated tenant's Elastic index if new data,
# handle duplicate and quarantine data otherwise
# NOTE: If the "OrdStatus" tag of the .fix message IS NOT "1" nor "2" - flow creates Order record only
# If the "OrdStatus" tag of the .fix message IS NOT "0" - flow creates OrderState record only

# Confluence documentation: https://steeleye.atlassian.net/wiki/spaces/IN/pages/1439465640/Order+TT+FIX
id: order-feed-tt-fix
name: Order Feed TT FIX

infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: reference-data
  type: ELASTICSEARCH

audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data

parameters:
- name: file_url
  envVar: SWARM_FILE_URL
  description: url of the file to process

controlFlows:
- name: S3OrLocalFile
  conditionTaskName: ParametersFlowController
  trueTaskName: S3DownloadFile
  falseTaskName: LocalFile

- name: NeedsStaticExecutionWithinFirm
  conditionTaskName: ExecutionWithinFirmController
  merge: true
  cases:
    DefaultExecutionWithinFirm: DefaultExecutionWithinFirm # Default "universal" case + Andurand override
    MapStaticExecutionWithinFirm: MapStaticExecutionWithinFirm # Case specific for PCUK

- name: NeedsQuarantine
  conditionTaskName: QuarantineCondition
  trueTaskName: QuarantinedElasticBulkTransformer
  falseTaskName: Noop
  merge: false

tasks:
  # ParametersFlowController
- path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
  name: ParametersFlowController
  params:
    arg_name: file_url
    starts_with: s3://
    condition: AND
    ignore_case: true
  upstreamTasks:
  - taskName: file_url
    key: file_url

  # S3DownloadFile
- path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
  name: S3DownloadFile
  upstreamTasks:
  - taskName: file_url
    key: file_url

  # LocalFile
- path: swarm_tasks.io.read.local_file:LocalFile
  name: LocalFile
  upstreamTasks:
  - taskName: file_url
    key: file_url

  # Download fix files and put their content in a data frame column
- path: swarm_tasks.io.read.fix.fix_batch_csv_downloader:FixBatchCsvDownloader
  name: FixBatchCsvDownloader
  upstreamTasks:
    - taskName: S3OrLocalFile
      key: extractor_result

  # Read .fix file, parse, validate and convert to FixParserResult dataclass
  # Note: the key has to be fix_dataframe as that's the argument name in execute()
- path: swarm_tasks.io.read.fix.fix_parser:FixParser
  name: FixParser
  upstreamTasks:
    - taskName: FixBatchCsvDownloader
      key: fix_dataframe

  # Convert FixParserResult to Pandas DataFrame
  # Note: the key has to be fix_parsed_data as that's the argument name in execute()
- path: swarm_tasks.io.read.fix.fix_parser_result_to_frame:FixParserResultToFrame
  name: FixParserResultToFrame
  params:
    dataframe_columns:
      # ff_1057: AggressorIndicator for Bloomberg/TT,
      # ff_5419: BasketID for Bloomberg/TT, ff_18220: Counterparty ID for TT
      - Account
      - AllocID
      - AllocQty
      - AvgPx
      - CFICode
      - Commission
      - CommType
      - ContractMultiplier
      - CumQty
      - Currency
      - DeliverToSubID
      - DERIVED_ALT_SEC_ID_MULTILEG
      - DERIVED_SIDE
      - ExecID
      - ExecType
      - FillExecID
      - ff_552
      - ff_1057
      - ff_5419
      - ff_18220
      - ff_18221
      - ff_18227
      - ff_10553
      - ff_16118 # only populated for Eurex trades and used for `orderIdentifiers.tradingVenueTransactionIdCode`
      - ff_8016 # Same as ff_16118, see: https://steeleye.atlassian.net/browse/EU-5492
      - ff_16558 # Only used by Andurand: PM approved timestamp: https://steeleye.atlassian.net/browse/ON-3307
      - LastCapacity
      - LastLiquidityInd
      - LastMkt
      - LastPx
      - LastQty
      - LeavesQty
      - LegExDestination
      - LegStrikePrice
      - LegSecurityAltID
      - LegSecurityAltIDSource
      - LegSecurityExchange
      # MarketSegmentID = ff_1300: LastMkt for Bloomberg/TT
      - MarketSegmentID
      - MaturityDate
      - MsgSeqNum
      - MsgType
      - MultiLegReportingType
      - NetMoney
      - OrderCapacity
      - OrderID
      - OrderQty
      - OrdStatus
      - OrdType
      - PartyID  # ff_448
      - PartyRole  # ff_452
      - Price
      - PriceType
      - QtyType
      - SecondaryClOrdID
      - SecondaryExecID
      - SecondaryOrderID
      - SecurityAltID
      - SecurityAltIDSource
      - SecurityExchange
      - SecurityID
      - SecurityIDSource
      - SecuritySubType
      - SecurityType # ff_167
      - SenderCompID
      - SendingTime
      - SettlCurrAmt
      - SettlCurrency
      - SettlDate
      - Side
      - StrikePrice
      - Symbol
      - TradeDate
      - TradeReportID # ff_571
      - TradeReportTransType # ff_487
      - TradeReportType # ff_856
      - TransactTime
      - TrdRegTimestampType
      - TrdRegTimestamp
      - TrdType # ff_828
  upstreamTasks:
    - taskName: FixParser
      key: fix_parsed_data

# Skip Logic: Skips all messages matching below conditions:
# - All messages with fix tag 150_ExecType ='D' (restarted orders)
# - All messages with fix tag 35 (MsgType) == 'B'
# - All messages with fix tag 442_MultiLegReportingType == 3 AND 150_ExecType == "F"
# - All messages with 35_MsgType=AE and 828_TrdType=1052 and 856_TradeReportType=0
# - All messages with 35_MsgType=AE and 828_TrdType=1 and 856_TradeReportType!=0
# - All messages with 35_MsgType=AE and 828_TrdType=0 and 856_TradeReportType=1000
# - All messages with 35_MsgType=AE and 167_SecurityType=MLEG and 828_TrdType != 1
# - All messages with 35_MsgType=AE and 167_SecurityType=MLEG and 442_MultiLegReportingType == 3
# - All messages with 35_MsgType=AE and ff_552>1 (single value or first of list) and 828_TrdType != 1
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: PrimarySkipLogic
  params:
    query: "~((`ExecType` == 'D') | (`MsgType` == 'B') | ((`MultiLegReportingType`.astype('str')=='3') & (`ExecType` == 'F')) | ((`MsgType`=='AE') & (`TrdType`.astype('str')=='1052') & (`TradeReportType`.astype('str')=='0')) | ((`MsgType`=='AE') & (`TrdType`.astype('str')=='1') & (`TradeReportType`.astype('str')!='0')) | ((`MsgType`=='AE') & (`TrdType`.astype('str')=='0') & (`TradeReportType`.astype('str')=='1000')) | ((`MsgType`=='AE') & (`SecurityType`=='MLEG') & (`TrdType`.astype('str')!='1')) | ((`MsgType`=='AE') & (`SecurityType`=='MLEG') & (`MultiLegReportingType`.astype('str')=='3')) | ((`MsgType`=='AE') & (`ff_552`.str.get(0).fillna(0).astype('int')>1) & (`TrdType`.astype('str')!='1')))"
    skip_on_empty: true
  upstreamTasks:
  - taskName: FixParserResultToFrame
    key: result

# Used for skip override without interfering with main skip logic
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: IceExchangeSkipLogic
  params:
    query: "index == index"
    skip_on_empty: true
  upstreamTasks:
  - taskName: PrimarySkipLogic
    key: result

# Filter SingleStockRecords and Individual legs of Multi-leg records
# Note: MultiLegReportingType will be null when MsgType = AE
# Note: we don't want to do any splitting for VDI exchanges as that's already
# done as a part of VDI fetch instruments
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: IndividualLegOrVDIRecords
  params:
    query: "(`MultiLegReportingType`.isin(['1', '2']) | (`MultiLegReportingType`.isnull()) | `SecurityExchange`.str.fullmatch('CME', case=False, na=False)) & ~((`MsgType`=='AE') & (`SecurityType`=='MLEG') & (`TrdType`.astype('str')=='1'))"
  upstreamTasks:
    - taskName: IceExchangeSkipLogic
      key: result

# Filter Multi-leg Records(FF_442=3) or multi-leg block trades ((`MsgType`=='AE') & (`SecurityType`=='MLEG') & (`TrdType`.astype('str')=='1'))
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: NonVDIMultilegRecords
  params:
    query: "((`MultiLegReportingType` == '3') & ~(`SecurityExchange`.str.fullmatch('CME', case=False, na=False))) | ((`MultiLegReportingType`.isnull()) & (`MsgType`=='AE') & (`SecurityType`=='MLEG') & (`TrdType`.astype('str')=='1'))"
  upstreamTasks:
    - taskName: IceExchangeSkipLogic
      key: result

- path: swarm_tasks.generic.frame.map_conditional_attribute_from_list_items:MapConditionalAttributeFromListItems
  name: PopulateSecIDSingleStockAndIndividualLeg
  paramsList:
    - source_attribute_for_pattern_search: SecurityAltIDSource
      source_attribute_for_mapping: SecurityAltID
      regex_pattern: ^4$
      target_attribute: __isin_single_stock_and_individual_leg__
    - source_attribute_for_pattern_search: SecurityAltIDSource
      source_attribute_for_mapping: SecurityAltID
      regex_pattern: ^8$
      target_attribute: __alt_sec_id_single_stock_and_individual_leg__
  upstreamTasks:
    - taskName: IndividualLegOrVDIRecords
      key: result

- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: SingleStockAndIndividualLegRecordsWithISIN
  params:
    orient: horizontal
  upstreamTasks:
  - taskName: IndividualLegOrVDIRecords
    key: vdi_exchange_records
  - taskName: PopulateSecIDSingleStockAndIndividualLeg
    key: sec_id_for_single_stock_and_individual_leg_records

# Keep everything but: (MsgType=AE) & (SecurityType in [OPT, FUT]) & (TrdType=1) & (552_NoSides > 1) & MultiLegReportingType is NULL
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: NonBlockTradesSingleLeg
  params:
    query: "~(`MultiLegReportingType`.isnull() & ((`MsgType`=='AE') & (`SecurityType`.isin(['OPT','FUT'])) & (`TrdType`.astype('str')=='1') & (`ff_552`.str.get(0).fillna(0).astype('int')>1)))"
  upstreamTasks:
    - taskName: SingleStockAndIndividualLegRecordsWithISIN
      key: result

# Filter Single leg block trades: (MsgType=AE) & (SecurityType in [OPT, FUT]) & (TrdType=1) & (552_NoSides > 1) & MultiLegReportingType is NULL
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: BlockTradesSingleLeg
  params:
    query: "`MultiLegReportingType`.isnull() & ((`MsgType`=='AE') & (`SecurityType`.isin(['OPT','FUT'])) & (`TrdType`.astype('str')=='1') & (`ff_552`.str.get(0).fillna(0).astype('int')>1))"
  upstreamTasks:
    - taskName: SingleStockAndIndividualLegRecordsWithISIN
      key: result

- path: swarm_tasks.order.feed.tt.fix.tt_fix_parse_multi_leg:TTFixParseMultiLeg
  name: GenerateMultilegFromIndividualLegs
  upstreamTasks:
    - taskName: NonVDIMultilegRecords
      key: result

# Split up non block trade multi legs from block trade multi legs
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: NonBlockTradesMultiLegs
  params:
    query: "~((`MultiLegReportingType`.isnull()) & (`MsgType`=='AE') & (`TrdType`.astype('str')=='1'))"
  upstreamTasks:
    - taskName: GenerateMultilegFromIndividualLegs
      key: result

# Filter multi-leg block trades ((`MsgType`=='AE') & (`TrdType`.astype('str')=='1'))
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: BlockTradesMultiLegs
  params:
    query: "((`MultiLegReportingType`.isnull()) & (`MsgType`=='AE') & (`TrdType`.astype('str')=='1'))"
  upstreamTasks:
    - taskName: GenerateMultilegFromIndividualLegs
      key: result

# Vertical concatenate single and multi leg block trades
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: BlockTradesVerticalConcatenator
  params:
    orient: vertical
    reset_index: true
    drop_index: true
  upstreamTasks:
    - taskName: BlockTradesMultiLegs
      key: multi_leg_block_trades
    - taskName: BlockTradesSingleLeg
      key: single_block_trades

# Unfold block multi legs into buySell pairs
- path: swarm_tasks.order.feed.tt.fix.unfold_block_trades:UnfoldBlockTrades
  name: UnfoldBlockTrades
  upstreamTasks:
    - taskName: BlockTradesVerticalConcatenator
      key: result

# Vertical concatenate of SingleStockWithMultileg and Multi-legs generated from
# individual legs
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PrimaryVerticalConcatenator
  params:
    orient: vertical
    reset_index: true
    drop_index: true
  upstreamTasks:
  - taskName: NonBlockTradesSingleLeg
    key: single_stock_and_multi_leg_records_with_isin
  - taskName: NonBlockTradesMultiLegs
    key: multi_legs_generated_from_individual_legs_non_block_trades
  - taskName: UnfoldBlockTrades
    key: multi_legs_generated_from_individual_legs_block_trades

  # Preliminary transformations to create utility columns
  # Party Fields
- path: swarm_tasks.generic.frame.map_conditional_attribute_from_list_items:MapConditionalAttributeFromListItems
  name: PopulatePartyIdentifiersFromList
  paramsList:
  - source_attribute_for_pattern_search: PartyRole
    source_attribute_for_mapping: PartyID
    regex_pattern: ^11$
    target_attribute: __trader__
  - source_attribute_for_pattern_search: PartyRole
    source_attribute_for_mapping: PartyID
    regex_pattern: ^37$
    target_attribute: __trader_37__
  - source_attribute_for_pattern_search: PartyRole
    source_attribute_for_mapping: PartyID
    regex_pattern: ^122$
    target_attribute: __investment_decision_maker__
  - source_attribute_for_pattern_search: PartyRole
    source_attribute_for_mapping: PartyID
    regex_pattern: ^12$
    target_attribute: __execution_within_firm__
  upstreamTasks:
    - taskName: PrimaryVerticalConcatenator
      key: result

  # Convert currency
- path: swarm_tasks.generic.currency.convert_minor_to_major:ConvertMinorToMajor
  name: ConvertMinorToMajor
  paramsList:
    - source_ccy_attribute: Currency
      target_ccy_attribute: transactionDetails.priceCurrency
  upstreamTasks:
    - taskName: PrimaryVerticalConcatenator
      key: result
  # Convert Prices
- path: swarm_tasks.generic.currency.convert_minor_to_major:ConvertMinorToMajor
  name: ConvertPrice
  paramsList:
    - source_price_attribute: LastPx
      source_ccy_attribute: Currency
      target_price_attribute: __last_px__
#      cast_to: abs
    - source_price_attribute: Price
      source_ccy_attribute: Currency
      target_price_attribute: __price__
    - source_price_attribute: StrikePrice
      source_ccy_attribute: Currency
      target_price_attribute: __option_strike_price__
      cast_to: abs
    - source_price_attribute: AvgPx
      source_ccy_attribute: Currency
      target_price_attribute: _orderState.transactionDetails.priceAverage
      cast_to: abs
  upstreamTasks:
    - taskName: PrimaryVerticalConcatenator
      key: result
  # Populate TradeID and OrderID
- path: swarm_tasks.transform.concat.concat_attributes:ConcatAttributes
  name: PopulateTradeAndOrderID
  paramsList:
    - source_attributes:
        - ExecID
        - MsgSeqNum
        - OrderID
      target_attribute: __trans_ref_num_non_ae__
      delimiter: "|"
      max_length: 52
    - source_attributes:
        - ExecID
        - MsgSeqNum
        - TradeReportID
      target_attribute: __trans_ref_num_ae__
      delimiter: "|"
      max_length: 52
    - source_attributes:
        - __security_id__
        - __symbol__
        - SecuritySubType
        - ff_10553
      target_attribute: executionDetails.outgoingOrderAddlInfo
      delimiter: "|"
    - source_attributes:
        - Side
        - OrderID
      target_attribute: __id_single_stock__
      delimiter: "|"
      max_length: 50
    - source_attributes:
        - Side
        - TradeReportID
        - SecurityID
      target_attribute: __side_trade_report_security_id__
      delimiter: "|"
      max_length: 50
    - source_attributes:
        - Side
        - OrderID
        - __static_text_m__
        - __alt_sec_id_single_stock_and_individual_leg__
      target_attribute: __id_individual_leg_of_mleg__
      delimiter: "|"
      max_length: 50
    - source_attributes:
        - __final_derived_side__
        - OrderID
        - __static_text_m__
        - DERIVED_ALT_SEC_ID_MULTILEG
      target_attribute: __id_mleg__
      delimiter: "|"
      max_length: 50
    - source_attributes:
        - OrderID
        - __exchange_symbol_local__
      target_attribute: __order_symbol__
      delimiter: "|"
  upstreamTasks:
    - taskName: PrimaryVerticalConcatenator
      key: result
    - taskName: MapAttributes
      key: map_attributes
    - taskName: MapStatic
      key: map_static
    - taskName: BuySell
      key: buy_sell
    - taskName: MapConditionalSymbol
      key: exchange_symbol_local
  # Map Static
- path: swarm_tasks.transform.map.map_static:MapStatic
  name: MapStatic
  paramsList:
    - target_attribute: _order.priceFormingData.tradedQuantity
      target_value: 0
    - target_attribute: _order.transactionDetails.cumulativeQuantity
      target_value: 0
    - target_attribute: _order.transactionDetails.priceAverage
      target_value: 0
    - target_attribute: _order.__meta_model__
      target_value: Order
    - target_attribute: _orderState.__meta_model__
      target_value: OrderState
    - target_attribute: _orderState.sourceIndex
      from_index: true
      cast_to_str: true
    - target_attribute: _order.sourceIndex
      from_index: true
      cast_to_str: true
    - target_attribute: __cash__
      target_value: CASH
    - target_attribute: dataSourceName
      target_value: TT
    - target_attribute: __newo_in_file_col__
      target_value: false
    # temporary orderStatus which only purpose is to be used in the AssignMetaParent task
    # to populate the metaParent id of OrderState records
    - target_attribute:  _order.executionDetails.orderStatus
      target_value: NEWO
    - target_attribute: __static_text_m__
      target_value: M
    - target_attribute: transactionDetails.recordType
      target_value: "Market Side"
  upstreamTasks:
    - taskName: PrimaryVerticalConcatenator
      key: result
  # Assign Meta Parent
- path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
  name: AssignMetaParent
  params:
    parent_model_attribute: _order.__meta_model__
    parent_attributes_prefix: _order.
    target_attribute: _orderState.__meta_parent__
  upstreamTasks:
  - taskName: MapStatic
    key: result
  - taskName: PopulateTradeAndOrderID
    key: order_id
  - taskName: MapAttributes
    key: map_attributes
  - taskName: MapConditional
    key: map_conditional
  # ExecutionWithinFirm Controller
- path: swarm_tasks.generic.value_proxy:ValueProxy
  name: ExecutionWithinFirmController
  params:
    value: DefaultExecutionWithinFirm
  upstreamTasks:
    - taskName: PrimaryVerticalConcatenator
      key: result

  # Map Use Default Logic
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: DefaultExecutionWithinFirm
  params:
    target_attribute: EXECUTION_WITHIN_FIRM
    cases:
      - query: "`__execution_within_firm__`.notnull()"
        attribute: __execution_within_firm__
      - query: "((`__execution_within_firm__`.notnull()) & (`__execution_within_firm__`.str.upper() == '3'))"
        value: NORE
  upstreamTasks:
    - taskName: PopulatePartyIdentifiersFromList
      key: result

  # Map Static ExecutionWithinFirm
- path: swarm_tasks.transform.map.map_static:MapStatic
  name: MapStaticExecutionWithinFirm
  paramsList:
    - target_attribute: EXECUTION_WITHIN_FIRM
      target_value: clnt:nore
  upstreamTasks:
    - taskName: PrimaryVerticalConcatenator
      key: result
   # Map Attributes
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: MapAttributes
  paramsList:
  - source_attribute: SettlCurrAmt
    target_attribute: transactionDetails.settlementAmount
    fill_nan: 0
  - source_attribute: PriceType
    target_attribute: transactionDetails.priceNotation
    fill_nan: MONE
  - source_attribute: CumQty
    target_attribute: _orderState.transactionDetails.cumulativeQuantity
    fill_nan: 0
  - source_attribute: LastQty
    target_attribute: _orderState.priceFormingData.tradedQuantity
    fill_nan: 0
  - source_attribute: LeavesQty
    target_attribute: _order.priceFormingData.remainingQuantity
    fill_nan: 0
  - source_attribute: ContractMultiplier
    target_attribute: __derivative_price_multiplier__
  - source_attribute: ff_5419
    target_attribute: transactionDetails.basketId
  - source_attribute: NetMoney
    target_attribute: transactionDetails.netAmount
    fill_nan: 0
  - source_attribute: Commission
    target_attribute: transactionDetails.commissionAmount
    fill_nan: 0
  - source_attribute: transactionDetails.priceCurrency
    target_attribute: transactionDetails.commissionAmountCurrency
  - source_attribute: CommType
    target_attribute: transactionDetails.commissionAmountType
  - source_attribute: OrdStatus
    target_attribute: __raw_order_status__
  - source_attribute: MsgType
    target_attribute: __raw_msg_type__
  - source_attribute: TradeReportTransType
    target_attribute: __raw_trade_report_trans_type__
  - source_attribute: Symbol
    target_attribute: __symbol__
  - source_attribute: SecurityID
    target_attribute: __security_id__
  - source_attribute: __investment_decision_maker__
    target_attribute: INVESTMENTDECISIONMAKER
  - source_attribute: ff_18220
    target_attribute: COUNTERPARTYID
  - source_attribute: CFICode
    target_attribute: __option_type_if_exists__
    start_index: -1
  - source_attribute: CFICode
    target_attribute: __option_type_mleg__
    start_index: 1
    end_index: 2
  - source_attribute: transactionDetails.buySellIndicator
    target_attribute: _order.buySell
  # Add the sourceKey from the S3FileURL column created in FixParserResultToFrame
  - source_attribute: S3FileURL
    target_attribute: sourceKey
  - source_attribute: SecondaryClOrdID
    target_attribute: orderIdentifiers.internalOrderIdCode
  - source_attribute: SecurityExchange
    target_attribute: __exchange_venue__
  - source_attribute: LastMkt
    target_attribute: __last_mkt__
  - source_attribute: MarketSegmentID
    target_attribute: __market_segment_id__
  upstreamTasks:
    - taskName: PrimaryVerticalConcatenator
      key: result
    - taskName: ConvertMinorToMajor
      key: convert_minor_to_major
    - taskName: MapValue
      key: map_value
    - taskName: BuySell
      key: buy_sell
    - taskName: PopulatePartyIdentifiersFromList
      key: party_ids
# Map Conditional - OrderStatus - Preliminary processing
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: MapPreliminaryOrderStatusConditional
  paramsList:
    - target_attribute: __order_status__
      cases:
        - query: "(`ExecType`.str.upper() == 'F') & (`__raw_order_status__`.isin(['1', '2']))"
          attribute: __raw_order_status__
        - query: "~((`ExecType`.str.upper() == 'F') & (`__raw_order_status__`.isin(['1', '2'])))"
          attribute: ExecType
  upstreamTasks:
    - taskName: MapAttributes
      key: map_attributes
    - taskName: PrimaryVerticalConcatenator
      key: result
# Map Value
- path: swarm_tasks.transform.map.map_value:MapValue
  name: MapValue
  paramsList:
    - source_attribute: AllocID
      target_attribute: transactionDetails.positionEffect
      case_insensitive: true
      value_map:
        C: Close
        D: Default
        F: FIFO
        N: Closed but notify on open
        O: Open
        R: Rolled
    - source_attribute: OrderCapacity
      target_attribute: __trading_capacity_from_order_capacity__
      case_insensitive: true
      value_map:
        A: AOTC
        M: MTCH
        W: AOTC
        G: DEAL
        I: DEAL
        P: DEAL
        R: DEAL
    - source_attribute: OrdType
      target_attribute: executionDetails.orderType
      case_insensitive: true
      default_value: MARKET
      value_map: "{1: 'Market', 'M': 'Market', 'MKT': 'Market', 'MARKET': 'Market', 2: 'Limit', 'LMT': 'Limit', 'BUY LIMIT': 'Limit', 'SELL LIMIT': 'Limit', 'LIMIT': 'Limit', 3: 'Stop / Stop Loss', 'STOP / STOP LOSS': 'Stop / Stop Loss', 'STOP LOSS': 'Stop / Stop Loss', 'BUY STOP': 'Stop / Stop Loss', 'SELL STOP': 'Stop / Stop Loss', 4: 'Stop Limit', 'STOP LIMIT': 'Stop Limit', 'SL': 'Stop Limit', 'STOP': 'Stop Limit', 'STP': 'Stop Limit', 5: 'Market On Close', 'MARKET ON CLOSE': 'Market On Close', 'MOC': 'Market On Close', 6: 'With or Without', 'WITH OR WITHOUT': 'With or Without', 7: 'Limit or Better', 'LIMIT OR BETTER': 'Limit or Better', 'LOB': 'Limit or Better', 8: 'Limit With or Without', 'LIMIT WITH OR WITHOUT': 'Limit With or Without', 9: 'On Basis', 'ON BASIS': 'On Basis', 'OB': 'On Basis', 'A': 'On Close', 'ON CLOSE': 'On Close', 'B': 'Limit On Close', 'LIMIT ON CLOSE': 'Limit On Close', 'LOC': 'Limit On Close', 'C': 'Forex Market', 'FOREX MARKET': 'Forex Market', 'FM ': 'Forex Market', 'FX MKT': 'Forex Market', 'D': 'Previously Quoted', 'PREVIOUSLY QUOTED': 'Previously Quoted', 'PQ': 'Previously Quoted', 'E': 'Previously Indicated', 'PREVIOUSLY INDICATED': 'Previously Indicated', 'F': 'Forex Limit', 'FOREX LIMIT': 'Forex Limit', 'FL': 'Forex Limit', 'G': 'Forex Swap', 'FOREX SWAP': 'Forex Swap', 'FS': 'Forex Swap', 'H': 'Forex Previously', 'FOREX PREVIOUSLY': 'Forex Previously', 'I': 'Funari', 'J': 'Market if Touched', 'MARKET IF TOUCHED': 'Market if Touched', 'MIT': 'Market if Touched', 'K': 'Market With Left Over as Limit', 'MARKET WITH LEFT OVER AS LIMIT': 'Market With Left Over as Limit', 'L': 'Previous Fun Valuation Point', 'PREVIOUS FUN VALUATION POINT': 'Previous Fun Valuation Point', 'NEXT FUND VALUATION POINT': 'Next Fund Valuation Point', 'P': 'Pegged', 'PEGGED': 'Pegged', 'PGD': 'Pegged', 'Q': 'Counter-order selection', 'COUNTER-ORDER SELECTION': 'Counter-order selection'}"
    - source_attribute: SecurityType
      target_attribute: __asset_class__
      case_insensitive: true
      value_map:
        FUT: "future"
        OPT: "option"
        SPOT: "fx spot"
        TBOND : "bond"
        CUR: "fx forward"
    - source_attribute: TrdType
      target_attribute: orderClass
      case_insensitive: true
      value_map: "{0: 'Regular Trade', 1: 'Block Trade', 2: 'Exchange For Physical', 3: 'Transfer', 11: 'Exchange For Risk', 12: 'Exchange For Swap', 14: 'Exchange Of Options For Options', 51: 'Average Price (Eurex)', 54: 'Flexible Contract Trade (Eurex)', 1000: 'Volatility', 1001: 'EFP Financial', 1002: 'EFP Index Futures', 1003: 'Strategy Block Trade', 1004: 'Block Standard CF', 1005: 'Block Combination CF', 1006: 'EFS EFP CF', 1007: 'Block Internal CF', 1008: 'Portfolio CF', 1009: 'Correction CF', 1010: 'Block Combination Buyer CF', 1011: 'Block Combination Seller CF', 1012: 'EFS EFP Combination CF', 1013: 'EFS EFP Combination Buyer CF', 1014: 'EFS EFP Combination Seller CF', 1015: 'OTC Standard CIO', 1016: 'OTC Combination CIO', 1017: 'OTC Combination Buyer CIO', 1018: 'OTC Combination Seller CIO', 1019: 'Standard Trade CD', 1020: 'Standard Outside Spread CD', 1021: 'Combination CD', 1022: 'Old CD', 1023: 'Internal CD', 1024: 'Portfolio CD', 1025: 'Correction CD', 1026: 'Exchange Granted FD', 1027: 'Standard Outside FD', 1028: 'Off Hours FD', 1029: 'Block FD', 1030: 'Exch Granted Exceed Max Lot FD', 1031: 'Exch Granted Eml Off Hours FD', 1032: 'Exch Granted Late FD', 1033: 'Flex Contract Conversion FD', 1034: 'Ice Efrp', 1035: 'Iceblk', 1036: 'Basis', 1037: 'Volatility Contingent', 1038: 'Stock Contingent', 1039: 'CCX EFP', 1040: 'Other Clearing Value', 1041: 'N2EX', 1042: 'EEX', 1043: 'EFS EFP Contra', 1044: 'Efm', 1045: 'Ng EFP EFS', 1046: 'Contra', 1047: 'Cpblk', 1048: 'Bilateral Off Exch', 1049: 'OTC Privately Negotiated Trades', 1050: 'OTC Large Notional Off Facility Swap', 1051: 'Block Swap Trade', 1052: 'Large in Scale (Eurex)', 1053: 'Against Actual (Eurex)', 1054: 'Large in Scale Package (Eurex)', 1055: 'Guaranteed Cross (Eurex)', 1056: 'Request for Cross (Eurex)', 1057: 'EEP CD (NDAQ_EU)', 1058: 'Buyer and Seller No Clearing CD (NDAQ_EU)', 1059: 'Buyer No Clearing CD (NDAQ_EU)', 1060: 'Seller No Clearing CD (NDAQ_EU)', 1061: 'EEP No Fee CD (NDAQ_EU)', 1062: 'Match Exch Manually CD (NDAQ_EU)', 1063: 'Match Exch Combination CD (NDAQ_EU)', 1064: 'Future DS Future Combo CD (NDAQ_EU)', 1065: 'Block Nonfinancial CD(NDAQ_EU)', 1066: 'Exchange for Swap Options CD (NDAQ_EU)', 1067: 'Block Nonfinanical CP CF (NDAQ_EU)', 1068: 'Exchange for Swap Options CF ((NDAQ_EU)', 1069: 'Asset Allocation', 1070: 'Cross Contra Trade', 1071: 'Type Committed (MX/LSE/IDEM/CurveGlobal)', 1072: 'Type Internal (HKEX)', 1073: 'Type Inter-Bank (HKEX)', 1074: 'J-Net One-Sided (OSE)', 1075: 'J-Net Cross (OSE)', 1076: 'EFP Bond', 1077: 'EFP SPI XJO', 1078: 'Cash Related Trade', 1079: 'Non-disclosed OTC Trade', 1080: 'Disclosed OTC Trade', 1081: 'SI Trade', 9999: 'Unknown'}"
  upstreamTasks:
    - taskName: PrimaryVerticalConcatenator
      key: result
# Map ff-16118 to 3 different columns based on the values in the column (this column might contain simple values
# or lists (len 1, 2, 3). If the len is 4, only the first 3 values are written to the 3 output columns
- path: swarm_tasks.transform.map.map_from_list_or_string:MapFromListOrString
  name: MapFromListOrString
  params:
    source_attribute: ff_16118
    target_attribute: __ff_16118_when_not_null__
  upstreamTasks:
    - taskName: PrimaryVerticalConcatenator
      key: result
  # Map Conditional Client
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: ClientMapConditional
  params:
    target_attribute: CLIENTID
    cases:
      - query: "`ff_18227`.notnull()"
        attribute: ff_18227
      - query: "`ff_18227`.isnull()"
        attribute: ff_18221
  upstreamTasks:
    - taskName: PrimaryVerticalConcatenator
      key: result

  # Map Conditional
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: MapConditional
  paramsList:
    - target_attribute: transactionDetails.settlementAmountCurrency
      cases:
        - query: "`transactionDetails.settlementAmount` != 0"
          attribute: transactionDetails.priceCurrency
    - target_attribute: transactionDetails.quantityNotation
      cases:
        - query: "index == index"
          value: UNIT
        - query: "`QtyType` == '0' | `SecurityIDSource` == '6'"
          value: MONE
    - target_attribute: __trading_date_time__
      cases:
        - query: "index == index"
          attribute: TransactTime
        - query: "(`TransactTime`.isnull() | `TransactTime` == '19700101-00:00:00.000000') & `TradeDate`.notnull()"
          attribute: TradeDate
        - query: "(`TransactTime`.isnull() | `TransactTime` == '19700101-00:00:00.000000') & `TradeDate`.isnull()"
          attribute: SendingTime
    - target_attribute: _orderState.priceFormingData.remainingQuantity
      cases:
        - query: "index == index"
          attribute: _order.priceFormingData.remainingQuantity
        - query: "`OrdStatus`.notnull() & `OrdStatus` == '2'"
          value: 0
    - target_attribute: __limit_price__
      cases:
        - query: "(`MultiLegReportingType` == '1') & (`executionDetails.orderType`.str.contains(r'.*[Ll]imit.*'))"
          attribute: Price
    - target_attribute: executionDetails.passiveAggressiveIndicator
      cases:
        - query: "(`LastLiquidityInd`.notnull() & `LastLiquidityInd` == '1') | (`ff_1057`.notnull() & `ff_1057` == 'N')"
          value: PASV
        - query: "(`LastLiquidityInd`.notnull() & `LastLiquidityInd` == '2') | (`ff_1057`.notnull() & `ff_1057` == 'Y')"
          value: AGRE
        - query: "`OrdType`.notnull() & `OrdType`.isin(('1', '3', '5'))"
          value: AGRE
    - target_attribute: __option_type__
      cases:
        - query: "`SecurityType` != 'OPT'"
          as_empty: true
        - query: "`SecurityType` == 'OPT'"
          attribute: __option_type_if_exists__
        - query: "`SecurityType` == 'OPT' & `MultiLegReportingType` == '3'"
          attribute: __option_type_mleg__
    - target_attribute: _order.id
      cases:
        - query: "`SecurityExchange`.str.fullmatch('CME', case=False, na=False) & `__exchange_symbol_local__`.notnull()"
          attribute: __order_symbol__
        - query: "~(`SecurityExchange`.str.fullmatch('CME', case=False, na=False) & `__exchange_symbol_local__`.notnull()) &`MultiLegReportingType` == '1'"
          attribute: __id_single_stock__
        - query: "~(`SecurityExchange`.str.fullmatch('CME', case=False, na=False) & `__exchange_symbol_local__`.notnull()) &`MultiLegReportingType` == '2'"
          attribute: __id_individual_leg_of_mleg__
        - query: "~(`SecurityExchange`.str.fullmatch('CME', case=False, na=False) & `__exchange_symbol_local__`.notnull()) &`MultiLegReportingType` == '3'"
          attribute: __id_mleg__
        # For MsgType=AE (always single-leg, or block trades), we don't have an OrderId
        - query: "~(`SecurityExchange`.str.fullmatch('CME', case=False, na=False) & `__exchange_symbol_local__`.notnull()) & `MsgType`=='AE'"
          attribute: __side_trade_report_security_id__
    - target_attribute: __isin__
      cases:
        - query: "`__isin_single_stock_and_individual_leg__`.notnull() & `MultiLegReportingType` != '3'"
          attribute: __isin_single_stock_and_individual_leg__
        - query: "`__isin_single_stock_and_individual_leg__`.isnull() & ((`MultiLegReportingType` == '3') | (`TrdType`.astype('str') == '1')) &  ~(`__exchange_venue__`.str.fullmatch('CME', case=False, na=False))"
          attribute: DERIVED_ISIN_MULTILEG
    - target_attribute: orderIdentifiers.tradingVenueTransactionIdCode
      cases:
        - query: "index == index"
          attribute: __ff_16118_when_not_null__
        - query: "`ff_16118`.isnull() & `ff_8016`.notnull()"
          attribute: ff_8016
        - query: "`ff_16118`.isnull() & `ff_8016`.isnull() & `SecondaryOrderID`.notnull()"
          attribute: SecondaryOrderID
        - query: "`ff_16118`.isnull() & `ff_8016`.isnull() & `SecondaryOrderID`.isnull() & `SecurityExchange`.str.match('EUREX', case=False, na=False)"
          attribute: FillExecID
    - target_attribute: priceFormingData.initialQuantity
      cases:
        - query: "`MsgType`!='AE'"
          attribute: OrderQty
        - query: "`MsgType`=='AE' & (`LastQty`.notnull())"
          attribute: LastQty
        - query: "`MsgType`=='AE' & (`LastQty`.isnull())"
          attribute: AllocQty
    - target_attribute: orderIdentifiers.aggregatedOrderId
      cases:
        - query: "`SecondaryExecID`.str.len() < 50"
          attribute: SecondaryExecID
    - target_attribute: _orderState.reportDetails.transactionRefNo
      cases:
        - query: "`MsgType`!='AE'"
          attribute: __trans_ref_num_non_ae__
        - query: "`MsgType`=='AE'"
          attribute: __trans_ref_num_ae__
    - target_attribute: TRADERID
      cases:
        - query: "`DeliverToSubID`.notnull()"
          attribute: DeliverToSubID
        - query: "(`__trader__`.notnull()) & (`DeliverToSubID`.isnull())"
          attribute: __trader__
        - query: "(`__trader__`.isnull()) & (`DeliverToSubID`.isnull())"
          attribute: __trader_37__
    - target_attribute: __underlying_symbol__
      cases:
        - query: "`LastMkt`.str.fullmatch('XOSE', case=False, na=False)"
          attribute: __symbol_xose__
        - query: "~`LastMkt`.str.fullmatch('XOSE', case=False, na=False)"
          attribute: __symbol__
    - target_attribute: multiLegReportingType
      cases:
        - query: "index == index"
          value: "Outright"
        - query: "`MultiLegReportingType`.str.fullmatch('2', case=False, na=False)"
          value: "Single Leg of Multi-leg"
  upstreamTasks:
    - taskName: PrimaryVerticalConcatenator
      key: result
    - taskName: MapAttributes
      key: map_attributes
    - taskName: MapValue
      key: map_value
    - taskName: ConvertPrice
      key: convert_price
    - taskName: ConvertMinorToMajor
      key: convert_minor_to_major
    - taskName: PopulateTradeAndOrderID
      key: populate_trade_and_order_id
    - taskName: MapFromListOrString
      key: tt_map_ff_16118
    - taskName: PopulatePartyIdentifiersFromList
      key: populate_party_ids_from_list
    - taskName: MapConditionalSymbol
      key: exchange_symbol_local
# Get Tenant LEI
- path: swarm_tasks.steeleye.generic.get_tenant_lei:GetTenantLEI
  name: GetTenantLEI
  params:
   target_lei_column: __tenant_lei__
   target_column_prefix: "lei:"
  upstreamTasks:
   - taskName: PrimaryVerticalConcatenator
     key: result
# Map the Executing Entity. This task is overridden
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: MapExecutingEntity
  params:
    source_attribute: __tenant_lei__
    target_attribute: EXECUTINGENTITYID
  upstreamTasks:
    - taskName: PrimaryVerticalConcatenator
      key: result
    - taskName: GetTenantLEI
      key: get_tenant_lei
  # This task creates the "ISIN" identifier column based on the TT trade sink handler Icarus logic
  # Please read https://btobits.com/fixopaedia/fixdic44/tag_456_SecurityAltIDSource_.html for more details
  # NOTE: Both the SecurityAltID and SecurityAltIDSource tags can be repeated on the same .fix message
  # This task aims to find which SecurityAltID contains the ISIN number based on the associated SecurityAltIDSource.
  # Output is a single column of ISINs.
  # Auxiliary Map Conditional
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: AuxiliaryMapConditional
  paramsList:
    - target_attribute: __trading_capacity_from_last_capacity__
      cases:
        - query: "index == index"
          value: AOTC
        - query: "`OrderCapacity`.isnull() & `LastCapacity`.notnull() & `LastCapacity` == '2'"
          value: MTCH
        - query: "`OrderCapacity`.isnull() & `LastCapacity`.notnull() & `LastCapacity`.isin(('3', '4', '5'))"
          value: DEAL
    - target_attribute: __order_submitted__
      cases:
        - query: "index == index"
          attribute: __trading_date_time__
        - query: "`TrdRegTimestamp`.notnull() & `TrdRegTimestampType`.isin(('4', '10'))"
          attribute: TrdRegTimestamp
    - target_attribute: executionDetails.passiveOnlyIndicator
      cases:
        - query: "`executionDetails.passiveAggressiveIndicator`.notnull() & `executionDetails.passiveAggressiveIndicator` == 'AGRE'"
          value: false
        - query: "`executionDetails.passiveAggressiveIndicator`.notnull() & `executionDetails.passiveAggressiveIndicator` == 'PASV'"
          value: true
  upstreamTasks:
    - taskName: PrimaryVerticalConcatenator
      key: result
    - taskName: MapValue
      key: map_value
    - taskName: MapConditional
      key: map_conditional
  # Auxiliary Map Conditional to populate transactionDetails.tradingCapacity
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: AuxiliaryFinalMapConditional
  params:
    target_attribute: transactionDetails.tradingCapacity
    cases:
      - query: "index == index"
        attribute: __trading_capacity_from_order_capacity__
      - query: "`__trading_capacity_from_order_capacity__`.isnull() & `__trading_capacity_from_last_capacity__`.notnull()"
        attribute: __trading_capacity_from_last_capacity__
      - query: "`__trading_capacity_from_order_capacity__`.isnull() & `__trading_capacity_from_last_capacity__`.isnull()"
        value: AOTC
  upstreamTasks:
    - taskName: MapValue
      key: result
    - taskName: AuxiliaryMapConditional
      key: auxiliary_map_conditional
  # The first two condition states that even if MultiLegReportingType is 3, if it's a VDI
  # venue, we want to map the source Side to BuySell, since the VDI fetch instruments takes care
  # of buysell and required flipping of sides.
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: BuySell
  paramsList:
    - target_attribute: transactionDetails.buySellIndicator
      cases:
        - query: "(`MultiLegReportingType` != '3' | (`SecurityExchange`.str.fullmatch('CME', case=False, na=False)) ) & `Side` == '1'"
          value: BUYI
        - query: "(`MultiLegReportingType` != '3' | (`SecurityExchange`.str.fullmatch('CME', case=False, na=False)) ) & `Side` == '2'"
          value: SELL
        - query: "`Side` == '1' & `MultiLegReportingType` == '3' & `DERIVED_SIDE` == '1'"
          value: BUYI
        - query: "`Side` == '1' & `MultiLegReportingType` == '3' & `DERIVED_SIDE` == '2'"
          value: SELL
        - query: "`Side` == '2' & `MultiLegReportingType` == '3' & `DERIVED_SIDE` == '1'"
          value: SELL
        - query: "`Side` == '2' & `MultiLegReportingType` == '3' & `DERIVED_SIDE` == '2'"
          value: BUYI
    - target_attribute: __final_derived_side__
      cases:
        - query: "`MultiLegReportingType` != '3'"
          attribute: Side
        - query: "`Side` == '1' & `MultiLegReportingType` == '3'"
          attribute: DERIVED_SIDE
        - query: "`Side` == '2' & `MultiLegReportingType` == '3' & `DERIVED_SIDE` == '1'"
          value: 2
        - query: "`Side` == '2' & `MultiLegReportingType` == '3' & `DERIVED_SIDE` == '2'"
          value: 1
  upstreamTasks:
    - taskName: PrimaryVerticalConcatenator
      key: result

  # Get ExchangeSymbolLocal
- path: swarm_tasks.generic.frame.map_conditional_attribute_from_list_items:MapConditionalAttributeFromListItems
  name: MapConditionalSymbol
  paramsList:
    - source_attribute_for_pattern_search: SecurityAltIDSource
      source_attribute_for_mapping: SecurityAltID
      regex_pattern: "^8$"
      target_attribute: __exchange_symbol_local__
    - source_attribute_for_pattern_search: SecurityAltIDSource
      source_attribute_for_mapping: SecurityAltID
      regex_pattern: "^4$"
      target_attribute: __symbol_xose__
  upstreamTasks:
    - taskName: PrimaryVerticalConcatenator
      key: result

  # Primary Frame Concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PrimaryFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __trading_capacity_from_order_capacity__
      - __trading_capacity_from_last_capacity__
      - __option_type_if_exists__
      - __ff_16118_when_not_null__
      - __trans_ref_num_non_ae__
      - __trans_ref_num_ae__
      - __trader_37__
      - __symbol_xose__
      - __tenant_lei__
  upstreamTasks:
  - taskName: ConvertMinorToMajor
    key: convert_minor_to_major
  - taskName: ConvertPrice
    key: convert_price
  - taskName: PopulateTradeAndOrderID
    key: populate_trade_id
  - taskName: MapStatic
    key: map_static
  - taskName: NeedsStaticExecutionWithinFirm
    key: static_execution_within_firm
  - taskName: MapConditional
    key: map_conditional
  - taskName: ClientMapConditional
    key: client_map_conditional
  - taskName: MapAttributes
    key: map_attributes
  - taskName: MapValue
    key: map_value
  - taskName: AuxiliaryMapConditional
    key: auxiliary_map_conditional
  - taskName: AuxiliaryFinalMapConditional
    key: auxiliary_final_map_conditional
  - taskName: AssignMetaParent
    key: meta_parent
  - taskName: BuySell
    key: buy_Sell
  - taskName: MapConditionalSymbol
    key: exchange_symbol_local
  - taskName: MapExecutingEntity
    key: executing_entity

# Convert dates
- path: swarm_tasks.transform.datetime.convert_datetime:ConvertDatetime
  name: ConvertDatetime
  paramsList:
    - source_attribute: __trading_date_time__
      target_attribute: _orderState.transactionDetails.tradingDateTime
      convert_to: datetime
    # Only used by Andurand: PM approved timestamp: https://steeleye.atlassian.net/browse/ON-3307
    - source_attribute: ff_16558
      target_attribute: __order_received_from_16558__
      convert_to: datetime
    - source_attribute: __order_submitted__
      target_attribute: timestamps.orderSubmitted
      convert_to: datetime
    - source_attribute: __order_submitted__
      target_attribute: date
      convert_to: date
    - source_attribute: SettlDate
      target_attribute: transactionDetails.settlementDate
      convert_to: date
    - source_attribute: MaturityDate
      target_attribute: __expiry_date__
      convert_to: date
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      key: primary_frame_concatenator
    - taskName: PrimaryVerticalConcatenator
      key: result

# Map Conditional - OrderStatus - Final processing
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: MapOrderStatusConditional
  paramsList:
    - target_attribute: _orderState.executionDetails.orderStatus
      cases:
        - query: "`__order_status__` == '1'"
          value: PARF
        - query: "`__order_status__` == '2'"
          value: FILL
        - query: "`__order_status__` == '3'"
          value: DNFD
        - query: "`__order_status__` == '4'"
          value: CAME
        - query: "`__order_status__` == '5'"
          value: REME
        - query: "`__order_status__` == 'D'"
          value: REME
        - query: "`__order_status__` == '6'"
          value: PNDC
        - query: "`__order_status__`.isin(('7', '9'))"
          value: REMA
        - query: "`__order_status__` == '8'"
          value: REMO
        - query: "`__order_status__` == 'C'"
          value: EXPI
        - query: "`__order_status__` == 'L'"
          value: TRIG
        # Values for MsgType=AE, overwrites any values from the queries (only for AE)
        - query: "`__raw_msg_type__`=='AE' & (`__raw_trade_report_trans_type__`.astype('str').isin(('0', '102')))"
          value: FILL
        - query: "`__raw_msg_type__`=='AE' & (`__raw_trade_report_trans_type__`.astype('str')=='1')"
          value: CAME
        - query: "`__raw_msg_type__`=='AE' & (`__raw_trade_report_trans_type__`.astype('str')=='2')"
          value: REME
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      key: result
    - taskName: MapPreliminaryOrderStatusConditional
      key: preliminary_order_status
  # Map Attributes populated upstream
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: AuxMapAttributes
  paramsList:
  - source_attribute: transactionDetails.tradingCapacity
    target_attribute: executionDetails.tradingCapacity
  - source_attribute: transactionDetails.buySellIndicator
    target_attribute: executionDetails.buySellIndicator
  - source_attribute: transactionDetails.buySellIndicator
    target_attribute: _orderState.buySell
  - source_attribute: _orderState.transactionDetails.tradingDateTime
    target_attribute: _orderState.timestamps.tradingDateTime
  - source_attribute: _order.id
    target_attribute: orderIdentifiers.orderIdCode
  - source_attribute: _order.id
    target_attribute: _orderState.id
  - source_attribute: _order.priceFormingData.tradedQuantity
    target_attribute: _order.transactionDetails.quantity
  - source_attribute: _orderState.priceFormingData.tradedQuantity
    target_attribute: _orderState.transactionDetails.quantity
  - source_attribute: _orderState.reportDetails.transactionRefNo
    target_attribute: _orderState.orderIdentifiers.transactionRefNo
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      key: result
    - taskName: ConvertDatetime
      key: datetime_result
    - taskName: MapOrderStatusConditional
      key: order_status
  # Timestamps map conditional
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: TimestampsMapConditional
  paramsList:
    - target_attribute: _orderState.timestamps.orderStatusUpdated
      cases:
        - query: "(`__raw_order_status__`.notnull() & `__raw_order_status__`.isin(('1', '2'))) | ((`__raw_msg_type__`=='AE') & (`__raw_trade_report_trans_type__`.isin(('0', '102'))))"
          attribute: _orderState.transactionDetails.tradingDateTime
        - query: "((`__raw_order_status__`.notnull()) & (~`__raw_order_status__`.isin(('1', '2')))) | ((`__raw_msg_type__`=='AE') & (`__raw_trade_report_trans_type__`.isin(('1', '2'))))"
          attribute: timestamps.orderSubmitted
    - target_attribute: _order.timestamps.orderStatusUpdated
      cases:
        - query: "`timestamps.orderSubmitted`.notnull()"
          attribute: timestamps.orderSubmitted
        - query: "`timestamps.orderSubmitted`.isnull() & `_orderState.transactionDetails.tradingDateTime`.notnull()"
          attribute: _orderState.transactionDetails.tradingDateTime
    - target_attribute: timestamps.orderReceived
      cases:
        - query: "`__order_received_from_16558__`.notnull()"
          attribute: __order_received_from_16558__
        - query: "`__order_received_from_16558__`.isnull()"
          attribute: timestamps.orderSubmitted
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      key: result
    - taskName: ConvertDatetime
      key: datetime_result
    - taskName: AuxMapAttributes
      key: aux_map_attributes

  # Auxiliary Frame Concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: AuxiliaryFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __trading_date_time__
      - __order_submitted__
      - __raw_msg_type__
      - __raw_trade_report_trans_type__
      - __order_received_from_16558__
  upstreamTasks:
  - taskName: PrimaryFrameConcatenator
    key: result
  - taskName: AuxMapAttributes
    key: aux_map_attributes
  - taskName: ConvertDatetime
    key: datetime_result
  - taskName: TimestampsMapConditional
    key: timestamps_map_conditional
  - taskName: MapOrderStatusConditional
    key: order_status
  # VDI Exchanges
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: VDIExchanges
  params:
    query: "`__exchange_venue__`.str.fullmatch('CME', case=False, na=False) & `__exchange_symbol_local__`.notnull()"
  upstreamTasks:
    - taskName: AuxiliaryFrameConcatenator
      key: result
  # Fetch Instruments for VDI Exchanges
- path: swarm_tasks.order.generic.vdi_link_instrument.fetch_vdi_and_split_multileg_trades:FetchVDIAndSplitMultiLegTrades
  name: FetchVDIAndSplitMultiLegTrades
  params:
    security_id_col: __exchange_symbol_local__
    trade_date: date
    instrument: instrumentDetails.instrument
    exchange_venue_col: __exchange_venue__
  upstreamTasks:
    - taskName: VDIExchanges
      key: result
  # Map __instrument_classification__ from InstrumentDetails.instrument.instrumentClassification
- path: swarm_tasks.transform.map.map_from_nested:MapFromNested
  name: MapVDIUltimateVenue
  paramsList:
  - source_attribute: instrumentDetails.instrument
    nested_path: venue.tradingVenue
    target_attribute: transactionDetails.ultimateVenue
  upstreamTasks:
    - taskName: FetchVDIAndSplitMultiLegTrades
      key: result

  # VDI instrument identifiers concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: VDIInstrumentsConcatenator
  params:
    orient: horizontal
  upstreamTasks:
  - taskName: FetchVDIAndSplitMultiLegTrades
    key: result
  - taskName: MapVDIUltimateVenue
    key: vdi_ultimate_venues

  # Filter out non-VDI exchanges - these will use Master Icarus
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: NonVDIExchanges
  params:
    query: "~`__exchange_venue__`.str.fullmatch('CME', case=False, na=False) | `__exchange_symbol_local__`.isnull()"
  upstreamTasks:
    - taskName: AuxiliaryFrameConcatenator
      key: result

  # Populate transactionDetails.ultimateVenue from Security Exchange
- path: swarm_tasks.order.feed.tt.fix.security_exchange:SecurityExchange
  name: SecurityExchange # used for InstrumentIdentifier
  params:
    security_exchange: __exchange_venue__
    last_mkt: __last_mkt__
    target_attribute: transactionDetails.ultimateVenue
  upstreamTasks:
    - taskName: NonVDIExchanges
      key: result

  # Fetch Instruments for non VDI Exchanges
  # Instrument Identifiers
- path: swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers:InstrumentIdentifiers
  name: InstrumentIdentifiers
  params:
    asset_class_attribute: __asset_class__
    currency_attribute: transactionDetails.priceCurrency
    isin_attribute: __isin__
    expiry_date_attribute: __expiry_date__
    option_strike_price_attribute: __option_strike_price__
    option_type_attribute: __option_type__
    swap_near_leg_date_attribute: date
    underlying_symbol_attribute: __underlying_symbol__
    venue_attribute: transactionDetails.ultimateVenue
    underlying_index_name_attribute: Underlying Index Name
    underlying_index_series_attribute: Underlying Index Series
    underlying_index_term_attribute: Underlying Index Term
    underlying_index_term_value_attribute: Underlying Index Term
    underlying_index_version_attribute: Underlying Index Version
    underlying_isin_attribute: Underlying Instrument ISIN/s
  upstreamTasks:
    - taskName: NonVDIExchanges
      key: result
    - taskName: SecurityExchange
      key: ultimate_venue
  # Link Instruments
- path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
  name: LinkInstrument
  params:
    identifiers_path: marketIdentifiers.instrument
    instrument_model_preference_rank:
      - VenueDirectInstrument
      - FcaFirdsInstrument
      - VenueInstrument
      - FirdsInstrument
      - AnnaDsbInstrument
      - SedolInstrument
      - CfdInstrument
      - SteelEyeInstrument
      - Instrument
    asset_class_attribute: __asset_class__
    currency_attribute: transactionDetails.priceCurrency
    venue_attribute: transactionDetails.ultimateVenue
  resources:
    ref_data_key: reference-data
    tenant_data_key: tenant-data
  upstreamTasks:
  - taskName: InstrumentIdentifiers
    key: result
  - taskName: NonVDIExchanges
    key: aux_frame_concatenator_result
  - taskName: SecurityExchange
    key: ultimate_venue

  # Override Instrument Strike Price
- path: swarm_tasks.transform.map.map_to_nested:MapToNested
  name: InstrumentOverridesStrikePrice
  params:
    source_attribute: __option_strike_price__
    nested_path: derivative.strikePrice
    target_attribute: instrumentDetails.instrument
  upstreamTasks:
    - taskName: LinkInstrument
      key: link_instrument
    - taskName: NonVDIExchanges
      key: result
  # Override Instrument Expiry Date
- path: swarm_tasks.transform.map.map_to_nested:MapToNested
  name: InstrumentOverridesExpiryDate
  params:
    source_attribute: __expiry_date__
    nested_path: derivative.expiryDate
    target_attribute: instrumentDetails.instrument
  upstreamTasks:
    - taskName: InstrumentOverridesStrikePrice
      key: override_strike_price
    - taskName: NonVDIExchanges
      key: result
  # Override Instrument Price Multiplier
- path: swarm_tasks.transform.map.map_to_nested:MapToNested
  name: InstrumentOverridesPriceMultiplier
  params:
    source_attribute: __derivative_price_multiplier__
    nested_path: derivative.priceMultiplier
    target_attribute: instrumentDetails.instrument
  upstreamTasks:
    - taskName: InstrumentOverridesExpiryDate
      key: override_expiry_date
    - taskName: NonVDIExchanges
      key: result
  # Override Instrument Delivery Type
- path: swarm_tasks.transform.map.map_to_nested:MapToNested
  name: InstrumentOverridesDeliveryType
  params:
    source_attribute: __cash__
    nested_path: derivative.deliveryType
    target_attribute: instrumentDetails.instrument
  upstreamTasks:
    - taskName: InstrumentOverridesPriceMultiplier
      key: override_price_multiplier
    - taskName: NonVDIExchanges
      key: result

  # Non-VDI instrument identifiers concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: NonVDIInstrumentsConcatenator
  params:
    orient: horizontal
  upstreamTasks:
  - taskName: NonVDIExchanges
    key: result
  - taskName: InstrumentOverridesDeliveryType
    key: instruments
  - taskName: SecurityExchange
    key: ultimate_venue
  - taskName: InstrumentIdentifiers
    key: instrument_identifiers_non_vdi
#
  # Vertical concatenator with instruments
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: AuxiliaryFrameWithInstruments
  params:
    orient: vertical
    reset_index: true
    drop_index: true
    drop_columns:
      - __exchange_symbol_local__
      - __exchange_venue__
      - __last_mkt__
      - __order_symbol__
  upstreamTasks:
  - taskName: NonVDIInstrumentsConcatenator
    key: result
  - taskName: VDIInstrumentsConcatenator
    key: vdi_instruments

  # Convert all FILLS/PARF into a single record if fill_by_fill_flag is FALSE
- path: swarm_tasks.order.generic.fill_by_fill_check:FillByFillCheck
  name: FillByFillCheck
  params:
    fill_by_fill_flag: true
  upstreamTasks:
    - taskName: AuxiliaryFrameWithInstruments
      key: result

  # Map __instrument_classification__ from InstrumentDetails.instrument.instrumentClassification
- path: swarm_tasks.transform.map.map_from_nested:MapFromNested
  name: MapNestedFromInstrument
  paramsList:
  - source_attribute: instrumentDetails.instrument
    nested_path: instrumentClassification
    target_attribute: __instrument_classification__
  - source_attribute: instrumentDetails.instrument
    nested_path: "derivative.priceDisplayFactor"
    target_fillna_value: 1
    treat_dot_delimited_path_as_simple_attribute: True
    target_attribute: __price_display_factor__
  upstreamTasks:
    - taskName: FillByFillCheck
      key: result
  # Map tradersAlgosWaiversIndicators.commodityDerivativeIndicator from __instrument_classification__
- path: swarm_tasks.transform.map.map_value:MapValue
  name: MapCommodityDerivativeIndicator
  paramsList:
  - source_attribute: __instrument_classification__
    target_attribute: tradersAlgosWaiversIndicators.commodityDerivativeIndicator
    case_insensitive: true
    regex_replace_map:
      - regex: "^JT[A-Z]{2}C[A-Z]{1}$"
        replace_value: F
      - regex: "^JT[A-Z]{2}F[A-Z]{1}$"
        replace_value: F
      - regex: "^FC[A-Z]{4}$"
        replace_value: F
      - regex: "^HT[A-Z]{4}$"
        replace_value: F
      - regex: "^O[A-Z]{2}T[A-Z]{2}$"
        replace_value: F
      - regex: "^ST[A-Z]{1}T[A-Z]{2}$"
        replace_value: F
      - regex: "^ST[A-Z]{1}C[A-Z]{2}$"
        replace_value: F
    value_map:
      F: false
  upstreamTasks:
  - taskName: MapNestedFromInstrument
    key: result
# Multiply __last_px__ and __price_display_factor__ to get the correct price
- path: swarm_tasks.transform.calculations.basic_operation:BasicOperation
  name: ApplyPriceDisplayFactor
  paramsList:
  - source_attributes:
    - __last_px__
    - __price_display_factor__
    operator: MULTIPLY
    target_attribute: __last_px_multiplied__
  - source_attributes:
    - __price__
    - __price_display_factor__
    operator: MULTIPLY
    target_attribute: __price_multiplied__
  - source_attributes:
    - __limit_price__
    - __price_display_factor__
    operator: MULTIPLY
    target_attribute: executionDetails.limitPrice
  upstreamTasks:
    - taskName: MapNestedFromInstrument
      key: map_nested
    - taskName: FillByFillCheck
      key: result
  # Use Price if LastPx is Null
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: GetLastPxOrPrice
  paramsList:
    - target_attribute: _orderState.transactionDetails.price
      cases:
        - query: "`__last_px_multiplied__`.notnull()"
          attribute: __last_px_multiplied__
        - query: "`__last_px_multiplied__`.isnull()"
          attribute: __price_multiplied__
  upstreamTasks:
    - taskName: ApplyPriceDisplayFactor
      key: result
# Map Attributes
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: MapAttributesPrice
  paramsList:
  - source_attribute:  _orderState.transactionDetails.price
    target_attribute: _orderState.priceFormingData.price
  upstreamTasks:
    - taskName: GetLastPxOrPrice
      key: result

  # Party Identifiers
- path: swarm_tasks.transform.steeleye.orders.data_source.universal.steeleye_trade_blotter.identifiers.party_identifiers:PartyIdentifiers
  name: PartyIdentifiers
  params:
    override_discretionary: false
    override_non_lei_prefix: id
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: FillByFillCheck
      key: result
  # Link Parties
- path: swarm_tasks.transform.steeleye.link.parties:LinkParties
  name: LinkParties
  params:
    identifiers_path: marketIdentifiers.parties
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: PartyIdentifiers
    key: result

  # Merge Market Identifiers
- path: swarm_tasks.transform.steeleye.link.merge_market_identifiers:MergeMarketIdentifiers
  name: MergeMarketIdentifiers
  params:
    identifiers_path: marketIdentifiers
    parties_path: marketIdentifiers.parties
  upstreamTasks:
    - taskName: PartyIdentifiers
      key: result
    - taskName: FillByFillCheck
      key: instrument_identifiers_non_vdi
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: MapVenue
  paramsList:
    - target_attribute: transactionDetails.venue
      cases:
        - query: "`__market_segment_id__`.notnull()"
          attribute: __market_segment_id__
        - query: "`__market_segment_id__`.isnull()"
          attribute: transactionDetails.ultimateVenue
        - query: "((`__market_segment_id__`.isnull()) & (`transactionDetails.ultimateVenue`.isnull()))"
          value: XOFF
  upstreamTasks:
    - taskName: FillByFillCheck
      key: result
  # Party and Instrument Frame Concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: MarketFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
      - INVESTMENTDECISIONMAKER
      - __asset_class__
      - __symbol__
      - __underlying_symbol__
      - __security_id__
      - EXECUTINGENTITYID
      - TRADERID
      - COUNTERPARTYID
      - __isin__
      - CLIENTID
      - __option_strike_price__
      - __option_type__
      - __expiry_date__
      - __derivative_price_multiplier__
      - __cash__
      - __instrument_classification__
      - marketIdentifiers.instrument
      - marketIdentifiers.parties
      - EXECUTION_WITHIN_FIRM
      - __option_type_mleg__
      - DERIVED_ALT_SEC_ID_MULTILEG
      - __last_px__
      - __limit_price__
      - __price__
      - __price_multiplied__
      - __last_px_multiplied__
      - __market_segment_id__
  upstreamTasks:
  - taskName: FillByFillCheck
    key: result
  - taskName: MergeMarketIdentifiers
    key: market_identifiers
  - taskName: LinkParties
    key: link_parties
  - taskName: MapCommodityDerivativeIndicator
    key: map_comm_drv_indicator
  - taskName: PartyIdentifiers
    key: party_identifiers
  - taskName: ApplyPriceDisplayFactor
    key: price_display_factor
  - taskName: MapAttributesPrice
    key: map_attributes_price
  - taskName: MapVenue
    key: map_venue
  # Split "Order" records
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderRecords
  params:
    except_prefix: _orderState.
    strip_prefix: true
  upstreamTasks:
  - taskName: MarketFrameConcatenator
    key: result
  # Split "OrderState" records
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderStateRecords
  params:
    except_prefix: _order.
    strip_prefix: true
  upstreamTasks:
  - taskName: MarketFrameConcatenator
    key: result
  # Strip "_order." prefix from "Order" records
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrder
  params:
    action: strip
    prefix: _order.
  upstreamTasks:
  - taskName: OrderRecords
    key: result
  # Strip "_orderState." prefix from "OrderState" records
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrderState
  params:
    action: strip
    prefix: _orderState.
  upstreamTasks:
  - taskName: OrderStateRecords
    key: result
  # Vertical concatenator of "Order" and "OrderState" records
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: VerticalConcatenator
  params:
    orient: vertical
    reset_index: true
    drop_index: true
  upstreamTasks:
  - taskName: StripPrefixOrder
    key: order_records
  - taskName: StripPrefixOrderState
    key: order_state_records
  # Pre Meta Frame Concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PreMetaFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
      - __raw_order_status__
      - __id_single_stock__
      - __id_individual_leg_of_mleg__
      - __id_mleg__
      - __static_text_m__
      - __isin_single_stock_and_individual_leg__
      - __side_trade_report_security_id__
      - DERIVED_ISIN_MULTILEG
      - __alt_sec_id_single_stock_and_individual_leg__
      - __final_derived_side__
  upstreamTasks:
  - taskName: VerticalConcatenator
    key: result
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: RemoveInvalidOrderStates
  params:
    query: "`executionDetails.orderStatus`.notnull() & ~(`__meta_model__` == 'Order' & `executionDetails.orderStatus` != 'NEWO') & ~(`__meta_model__` == 'OrderState' & `executionDetails.orderStatus` == 'NEWO')"
  upstreamTasks:
    - taskName: PreMetaFrameConcatenator
      key: result
- path: swarm_tasks.order.generic.remove_duplicate_newo:RemoveDuplicateNEWO
  name: RemoveDupNEWO
  params:
    newo_in_file_col: __newo_in_file_col__
    drop_newo_in_file_col: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
    - taskName: RemoveInvalidOrderStates
      key: result
# Compute Best-execution
- path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
  name: BestExecution
  resources:
    es_client_key: reference-data
  upstreamTasks:
  - taskName: RemoveDupNEWO
    key: result
# Concatenate Best-Execution Result
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: BestExecutionConcatenator
  params:
    orient: horizontal
  upstreamTasks:
  - taskName: BestExecution
    key: result
  - taskName: RemoveDupNEWO
    key: remove_dup_newo
  # Assign Meta
- path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
  name: Meta
  params:
    model_attribute: __meta_model__
    parent_attribute: __meta_parent__
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: BestExecutionConcatenator
    key: result
  # Post Meta Concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PostMetaConcatenator
  params:
    orient: horizontal
    drop_columns:
    - __meta_model__
    - __meta_parent__
  upstreamTasks:
  - taskName: Meta
    key: result
  - taskName: BestExecutionConcatenator
    key: best_ex_concatenator
  # Elastic Bulk Transformer
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: ElasticBulkTransformer
  params:
    action_type: create
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: PostMetaConcatenator
    key: transform_result
  - taskName: FixParserResultToFrame
    key: producer_result
  # Elastic Bulk Writer
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: PutIfAbsent
  params:
    payload_size: 10000000
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: ElasticBulkTransformer
    key: result
# Instrument Mapper
- path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
  name: InstrumentMapper
  upstreamTasks:
    - taskName: PostMetaConcatenator
      key: source_frame
    - taskName: PutIfAbsent
      key: bulk_writer_result
  # Quarantine Condition
- path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
  name: QuarantineCondition
  upstreamTasks:
  - taskName: PutIfAbsent
    key: bulk_writer_result
  # Quarantine Elastic Bulk Transformer
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: QuarantinedElasticBulkTransformer
  params:
    action_type: create
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: BestExecutionConcatenator
    key: transform_result
  - taskName: FixParserResultToFrame
    key: producer_result
  - taskName: PutIfAbsent
    key: bulk_writer_result
  # Quarantine Elastic Bulk Writer
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: QuarantinedPutIfAbsent
  params:
    payload_size: 10000000
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: QuarantinedElasticBulkTransformer
    key: result
- path: swarm_tasks.control_flow.noop:Noop
  name: Noop
