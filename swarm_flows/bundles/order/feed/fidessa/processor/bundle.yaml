id: order-feed-fidessa-processor
name: Fidessa Market Orders
infra:
- name: tenant-data
  type: ELASTICSEARCH
- name: reference-data
  type: ELASTICSEARCH
audit:
  type: SINK_FILE_AUDIT
  resourceId: tenant-data
parameters:
- name: file_url
  envVar: SWARM_FILE_URL
  description: url of the file to process
controlFlows:
- name: S3OrLocalFile
  conditionTaskName: ParametersFlowController
  trueTaskName: S3DownloadFile
  falseTaskName: LocalFile
  merge: true
- name: NeedsQuarantine
  conditionTaskName: QuarantineCondition
  trueTaskName: QuarantinedElasticBulkTransformer
  falseTaskName: Noop
  mapped: true
  merge: false
tasks:
- path: swarm_tasks.boolean.parameters_flow_controller:ParametersFlowController
  name: ParametersFlowController
  params:
    arg_name: file_url
    starts_with: s3://
    condition: AND
    ignore_case: true
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.aws.s3_download_file:S3DownloadFile
  name: S3DownloadFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.local_file:LocalFile
  name: LocalFile
  upstreamTasks:
  - taskName: file_url
    mapped: false
    key: file_url
- path: swarm_tasks.io.read.read_batch_csv:ReadBatchCSV
  name: ReadBatchCSV
  params:
  upstreamTasks:
    - taskName: S3OrLocalFile
      key: extractor_result
- path: swarm_tasks.order.feed.fidessa.processor.pre_process_fidessa_data:PreProcessFidessaData
  name: PreProcessFidessaData
  params:
  upstreamTasks:
  - taskName: ReadBatchCSV
    mapped: false
    key: producer_result
- path: swarm_tasks.io.read.csv_file_splitter:CsvFileSplitter
  name: CsvFileSplitter
  params:
    chunksize: 25000
    detect_encoding: true
  upstreamTasks:
    - taskName: PreProcessFidessaData
      mapped: false
      key: extractor_result
- path: swarm_tasks.io.read.batch_producer:BatchProducer
  name: BatchProducer
  params:
    source_schema:
      "Alternate References": string
      "BOOK_VIEW_CODE": string
      "BROKER_LEI": string
      "BUY_SELL": string
      "CFI_CODE": string
      "COUNTERPARTY_CODE": string
      "DEALING_CAPACITY": string
      "DEALT_CURRENCY_ID": string
      "DERIVED_AGGREGATE_ORDER_ID": string
      "DERIVED_BOOK_VIEW_CODE": string
      "DERIVED_BUY_SELL": string
      "DERIVED_COUNTERPARTY_CODE": string
      "DERIVED_ENTERED_BY": string
      "DERIVED_EXECUTION_DECISION": string
      "DERIVED_HIERARCHY": string
      "DERIVED_INVESTMENT_DECISION": string
      "DERIVED_ISIN_CODE": string
      "DERIVED_LIMIT_PRICE": float
      "DERIVED_MARKET_ID": string
      "DERIVED_ORDER_ID": string
      "DERIVED_ORDER_RECEIVED_DT": string
      "DERIVED_ORDER_SUBMITTED_DT": string
      "DERIVED_PARENT_ORDER_ID": string
      "DERIVED_STOP_PRICE": string
      "DERIVED_VENUE": string
      "DESTINATION_EXCHANGE_ID": string
      "DISPLAY_QUANTITY": float
      "ENTERED_BY": string
      "Event Timestamp": string
      "Event Type": string
      "EXCHANGE_ORDER_ID": string
      "EXCHANGE_TRADE_CODE": string
      "EXECUTION_DECISION": string
      "EXECUTION_DECISION_SHORT_CODE": string
      "EXECUTION_VENUE": string
      "EXPIRY_TYPE": string
      "Gross_Fill_Price": float
      "GROSS_PRICE": float
      "INSTRUMENT_DESCRIPTION": string
      "INSTRUMENT_EXPIRY_DATE": string
      "INSTRUMENT_FIM_CODE": string
      "INSTRUMENT_TRADED_CCY": string
      "Internal ID": string
      "Internal sequence": int
      "INVESTMENT_DECISION": string
      "INVESTMENT_DECISION_SHORT_CODE": string
      "LIMIT_PRICE": float
      "MARKET_ID": string
      "Object Type": string
      "OPTION_TYPE": string
      "ORDER_PRICE_TYPE": string
      "Primary ID": string
      "QUANTITY": float
      "REASON_TEXT": string
      "REGULATORY_ALGO_ID": string
      "Related Objects": string
      "STOP_PRICE": float
      "STRIKE_PRICE": float
      "TRADING_ENTITY_ID": string
      "TRADING_QUANTITY": float
      "TRADING_VENUE_TRANSACTION_ID": string
  upstreamTasks:
  - taskName: CsvFileSplitter
    mapped: true
    flatten: true
    key: file_splitter_result
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: ExpiryDate
  paramsList:
    - target_attribute: __expiry_dt__
      cases:
        # There are some dates in the input file with value 000000000. Expiry date should be set to null in these cases
        - query: "`INSTRUMENT_EXPIRY_DATE`.notnull() & `INSTRUMENT_EXPIRY_DATE`.str.len() >= 8 & ~`INSTRUMENT_EXPIRY_DATE`.str.match('0000',case=False,na=False)"
          attribute: INSTRUMENT_EXPIRY_DATE
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result
  # primary transformations
- path: swarm_tasks.transform.datetime.convert_datetime:ConvertDatetime
  name: ConvertDatetime
  paramsList:
    - source_attribute: __expiry_dt__
      target_attribute: __fb_expiry_dt__
      convert_to: date
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result
    - taskName: ExpiryDate
      mapped: true
      key: expiry_dt
- path: swarm_tasks.generic.currency.convert_minor_to_major:ConvertMinorToMajor
  name: ConvertMinorToMajor
  paramsList:
    # Currencies
    - source_ccy_attribute: DEALT_CURRENCY_ID
      target_ccy_attribute: transactionDetails.priceCurrency
    # Prices
    - source_price_attribute: GROSS_PRICE
      source_ccy_attribute: DEALT_CURRENCY_ID
      target_price_attribute: orderState.transactionDetails.price
      cast_to: abs
    - source_price_attribute: Gross_Fill_Price
      source_ccy_attribute: Instrument_Traded_Ccy
      target_price_attribute: __gross_fill_price__
      cast_to: abs
    - source_price_attribute: DERIVED_LIMIT_PRICE
      source_ccy_attribute: DEALT_CURRENCY_ID
      target_price_attribute: __limit_price__
      cast_to: abs
    - source_price_attribute: DERIVED_STOP_PRICE
      source_ccy_attribute: DEALT_CURRENCY_ID
      target_price_attribute: __stop_price__
      cast_to: abs
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result
# Get Tenant LEI
- path: swarm_tasks.steeleye.generic.get_tenant_lei:GetTenantLEI
  name: GetTenantLEI
  params:
    target_lei_column: __tenant_lei__
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: MapAttribute
  paramsList:
  - source_attribute: Internal ID
    target_attribute: orderIdentifiers.internalOrderIdCode
  - source_attribute: DERIVED_AGGREGATE_ORDER_ID
    target_attribute: orderIdentifiers.aggregatedOrderId
  - source_attribute: DERIVED_ORDER_ID
    target_attribute: _order.id
  - source_attribute: Internal sequence
    target_attribute: orderIdentifiers.sequenceNumber
  - source_attribute: DERIVED_PARENT_ORDER_ID
    target_attribute: orderIdentifiers.parentOrderId
  - source_attribute: DERIVED_HIERARCHY
    target_attribute: hierarchy
  - source_attribute: REGULATORY_ALGO_ID
    target_attribute: executionDetails.routingStrategy
  - source_attribute: __limit_price__
    target_attribute: executionDetails.limitPrice
  - source_attribute: __stop_price__
    target_attribute: executionDetails.stopPrice
  - source_attribute: DERIVED_INITIAL_QTY
    target_attribute: priceFormingData.initialQuantity
  - source_attribute: DISPLAY_QUANTITY
    target_attribute: priceFormingData.displayedQuantity
  - source_attribute: DERIVED_VENUE
    target_attribute: transactionDetails.ultimateVenue
  - source_attribute: DERIVED_VENUE
    target_attribute: transactionDetails.venue
  - source_attribute: DERIVED_BOOK_VIEW_CODE
    target_attribute: orderIdentifiers.orderRoutingCode
  - source_attribute: DERIVED_BOOK_VIEW_CODE
    target_attribute: transactionDetails.basketId
  - source_attribute: INSTRUMENT_FIM_CODE
    target_attribute: transactionDetails.complexTradeComponentId
    # fields for populating executionDetails.outgoingOrderAddlInfo
  - source_attribute: INSTRUMENT_FIM_CODE
    target_attribute: __fim_code__
    prefix: "FIM Code: "
    cast_to: string.upper
  - source_attribute: INSTRUMENT_SEDOL_CODE
    target_attribute: __sedol_code__
    prefix: "Sedol: "
    cast_to: string.upper
  - source_attribute: DERIVED_MARKET_ID
    target_attribute: __market_id_for_addl_info__
    prefix: "Market: "
    cast_to: string.upper
  - source_attribute: REASON_TEXT
    target_attribute: __reason__
    prefix: "Reason: "
    cast_to: string.upper
  - source_attribute: Order_Price_Type_Qualifier
    target_attribute: __price_qualifier__
    prefix: "Price Qualifier: "
    cast_to: string.upper
  - source_attribute: Event Type
    target_attribute: __event_type__
    prefix: "Event Type: "
    cast_to: string.upper
  - source_attribute: Alternate References
    target_attribute: __alternate_ref__
    prefix: "Alt Ref: "
    cast_to: string.upper
  - source_attribute: EXECUTION_DECISION_SHORT_CODE
    target_attribute: __execution_decision__
    prefix: "Execution Decision: "
    cast_to: string.upper
  - source_attribute: INVESTMENT_DECISION_SHORT_CODE
    target_attribute: __investment_decision__
    prefix: "Investment Decision: "
    cast_to: string.upper
  # fields for party identifiers
  - source_attribute: TRADING_ENTITY_ID
    target_attribute: __trading_entity_id__
    prefix: "id:"
  - source_attribute: DERIVED_ENTERED_BY
    target_attribute: __entered_by__
    prefix: "id:"
  - source_attribute: DERIVED_INVESTMENT_DECISION
    target_attribute: __investment_decision_id__
    prefix: "id:"
  - source_attribute: DERIVED_EXECUTION_DECISION
    target_attribute: __execution_decision_id__
    prefix: "id:"
  - source_attribute: __tenant_lei__
    target_attribute: __tenant_lei_with_prefix__
    prefix: "lei:"
  - source_attribute: DERIVED_MARKET_ID
    target_attribute: __market_id__
    prefix: "id:"
  - source_attribute: DERIVED_COUNTERPARTY_CODE
    target_attribute: __counterparty_code__
    prefix: "id:"
  upstreamTasks:
  - taskName: BatchProducer
    mapped: true
    key: result
  - taskName: ConvertMinorToMajor
    mapped: true
    key: convert_to_major
  - taskName: GetTenantLEI
    mapped: true
    key: get_tenant_lei
- path: swarm_tasks.transform.map.map_value:MapValue
  name: MapValue
  paramsList:
    - source_attribute: Event Type
      target_attribute: orderState.executionDetails.orderStatus
      case_insensitive: true
      value_map:
        ExchangeOrderReplaceAccepted: REME
        ExchangeOrderCancelAccepted: CAME
        ExchangeOrderFillEntered: PARF
        ExchangeOrderFillCancelled: CAME
    - source_attribute: EXPIRY_TYPE
      target_attribute: __validity_period__
      case_insensitive: true
      value_map:
        GTD: GTDV
        GFD: DAVY
        IOC: IOCV
        FOK: FOKV
        GTC: GTCV
        GTT: GTTV
        GTS: GTSV
        GTX: GTXV
        GAT: GATV
        GAD: GADV
        GAS: GASV
    - source_attribute: DEALING_CAPACITY
      target_attribute: executionDetails.tradingCapacity
      case_insensitive: true
      value_map:
        P: DEAL
        R: DEAL
        A: AOTC
    - source_attribute: DERIVED_BUY_SELL
      target_attribute: transactionDetails.buySellIndicator
      case_insensitive: true
      value_map:
        b: BUYI
        buy: BUYI
        buy to cover: BUYI
        buytocover: BUYI
        cover: BUYI
        s: SELL
        sell: SELL
        sell short: SELL
        short: SELL
        ss: SELL
    - source_attribute: DERIVED_BUY_SELL
      target_attribute: _order.buySell
      case_insensitive: true
      value_map:
        b: "1"
        buy: "1"
        buy to cover: "3"
        buytocover: "3"
        cover: "3"
        s: "2"
        sell: "2"
        sell short: "5"
        short: "5"
        ss: "5"
    - source_attribute: OPTION_TYPE
      target_attribute: __fb_option_type__
      case_insensitive: true
      value_map:
        P: PUTO
        C: CALL
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result
- path: swarm_tasks.transform.concat.concat_attributes:ConcatAttributes
  name: ConcatAttributes
  paramsList:
    - source_attributes:
        - __fim_code__
        - __sedol_code__
        - __market_id_for_addl_info__
        - __reason__
        - __price_qualifier__
        - __event_type__
        - __alternate_ref__
        - __execution_decision__
        - __investment_decision__
      target_attribute: executionDetails.outgoingOrderAddlInfo
      delimiter: ';  '
    - source_attributes:
        - Primary ID
        - Internal Sequence
        - date
      target_attribute: __derived_transaction_ref_no__
      delimiter: '|'
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result
    - taskName: MapAttribute
      mapped: true
      key: map_attribute
    - taskName: ConvertDatetime
      mapped: true
      key: convert_date_time
    - taskName: ValidityTimePeriod
      mapped: true
      key: validity_time_period
- path: swarm_tasks.transform.map.map_static:MapStatic
  name: MapStatic
  paramsList:
  - target_attribute: _order.__meta_model__
    target_value: Order
  - target_attribute: orderState.__meta_model__
    target_value: OrderState
  - target_attribute: _order.executionDetails.orderStatus
    target_value: NEWO
  - target_attribute: orderState.sourceKey
    from_env_var: SWARM_FILE_URL
  - target_attribute: orderState.sourceIndex
    from_index: true
  - target_attribute: _order.sourceKey
    from_env_var: SWARM_FILE_URL
  - target_attribute: _order.sourceIndex
    from_index: true
  - target_attribute: dataSourceName
    target_value: Fidessa Market Orders
  - target_attribute: transactionDetails.recordType
    target_value: Market Side
  - target_attribute: transactionDetails.priceNotation
    target_value: MONE
  - target_attribute: transactionDetails.quantityNotation
    target_value: UNIT
  - target_attribute: __fb_is_created_through_fallback__
    target_value: True
  - target_attribute: __fb_price_multiplier__
    target_value: 1
  - target_attribute: __fb_instrumentIdCodeType__
    target_value: ID
  - target_attribute: __fb_price_notation__
    target_value: MONE
  - target_attribute: __fb_quantity_notation__
    target_value: UNIT
  - target_attribute: __fb_ext_strikePriceType__
    target_value: MntryVal
  upstreamTasks:
  - taskName: BatchProducer
    mapped: true
    key: result
- path: swarm_tasks.transform.datetime.join_date_and_time:JoinDateAndTimeFormat
  name: ValidityTimePeriod
  paramsList:
    - source_date_attribute: EXPIRY_DATETIME
      skip_time_attribute: true
      target_attribute: __timestamps_validity_period__
      source_formats: ['%Y%m%d %H:%M:%S.%f %zs', '%Y%m%d %H:%M:%S.%f %z']
      target_format: '%Y-%m-%dT%H:%M:%S.%fZ'
    - source_date_attribute: DERIVED_ORDER_RECEIVED_DT
      skip_time_attribute: true
      target_attribute: timestamps.orderReceived
      source_formats: ['%Y%m%d %H:%M:%S.%f %zs', '%Y%m%d %H:%M:%S.%f %z']
      target_format: '%Y-%m-%dT%H:%M:%S.%fZ'
    - source_date_attribute: DERIVED_ORDER_SUBMITTED_DT
      skip_time_attribute: true
      target_attribute: timestamps.orderSubmitted
      source_formats: ['%Y%m%d %H:%M:%S.%f %zs', '%Y%m%d %H:%M:%S.%f %z']
      target_format: '%Y-%m-%dT%H:%M:%S.%fZ'
    - source_date_attribute: Event Timestamp
      skip_time_attribute: true
      target_attribute: timestamps.orderStatusUpdated
      source_formats: ['%Y%m%d %H:%M:%S.%f %zs', '%Y%m%d %H:%M:%S.%f %z']
      target_format: '%Y-%m-%dT%H:%M:%S.%fZ'
    - source_date_attribute: DERIVED_ORDER_RECEIVED_DT
      skip_time_attribute: true
      source_formats: ['%Y%m%d %H:%M:%S.%f %zs', '%Y%m%d %H:%M:%S.%f %z']
      target_attribute: date
      target_format: "%Y-%m-%d"
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result
    - taskName: MapStatic
      mapped: true
      key: map_static
- path: swarm_tasks.transform.map.map_conditional:MapConditional
  name: MapConditional
  paramsList:
    - target_attribute: transactionDetails.tradingDateTime
      cases:
        - query: "`orderState.executionDetails.orderStatus` == 'PARF'"
          attribute: timestamps.orderStatusUpdated
    - target_attribute: reportDetails.transactionRefNo
      cases:
        - query: "index == index"
          attribute: __derived_transaction_ref_no__
        - query: "`orderState.executionDetails.orderStatus` == 'PARF' & `EXCHANGE_TRADE_CODE`.notnull()"
          attribute: EXCHANGE_TRADE_CODE
        - query: "`orderState.executionDetails.orderStatus` == 'PARF' & `EXCHANGE_TRADE_CODE`.isnull()"
          attribute: TRADING_VENUE_TRANSACTION_ID
    - target_attribute: executionDetails.orderType
      cases:
        - query: "index == index"
          value: MARKET
        - query: "`DERIVED_LIMIT_PRICE`.notnull()"
          value: LIMIT
        - query: "`DERIVED_STOP_PRICE`.notnull()"
          value: STOP
    - target_attribute: orderIdentifiers.tradingVenueTransactionIdCode
      cases:
        - query: "`orderState.executionDetails.orderStatus` == 'PARF'"
          attribute: TRADING_VENUE_TRANSACTION_ID
    - target_attribute: transactionDetails.quantity
      cases:
        - query: "`orderState.executionDetails.orderStatus` == 'PARF'"
          attribute: QUANTITY
    - target_attribute: transactionDetails.priceAverage
      cases:
        - query: "`DERIVED_HIERARCHY`.str.upper() == 'STANDALONE'"
          attribute: __gross_fill_price__
    - target_attribute: __asset_class__
      cases:
        - query: "`CFI_CODE`.str.upper().str.startswith('D', na=False)"
          value: bond
        - query: "`CFI_CODE`.str.upper().str.startswith('F', na=False)"
          value: future
        - query: "`CFI_CODE`.str.upper().str.startswith('O', na=False)"
          value: option
        - query: "`CFI_CODE`.str.upper().str.startswith('H', na=False)"
          value: option
    - target_attribute: __newo_in_file_col__
      cases:
        - query: "index == index"
          value: false
        - query: "`Event Type`.str.upper() == 'EXCHANGEORDERENTRYREQUESTED'"
          value: true
    - target_attribute: __fb_Strike_price__
      cases:
        - query: "`STRIKE_PRICE`.notnull() & `STRIKE_PRICE` != 0"
          attribute: STRIKE_PRICE
    - target_attribute: __fb_Strike_price_ccy__
      cases:
        - query: "`STRIKE_PRICE`.notnull() & `STRIKE_PRICE` != 0"
          attribute: INSTRUMENT_TRADED_CCY
    - target_attribute: __executing_entity_id__
      cases:
        - query: "`TRADING_ENTITY_ID`.notnull()"
          attribute: __trading_entity_id__
        - query: "`TRADING_ENTITY_ID`.isnull()"
          attribute: __tenant_lei_with_prefix__
    - target_attribute: __executing_entity_id_fallback__
      cases:
        - query: "`TRADING_ENTITY_ID`.notnull()"
          attribute: TRADING_ENTITY_ID
        - query: "`TRADING_ENTITY_ID`.isnull()"
          attribute: __tenant_lei__
    - target_attribute: isIceberg
      cases:
        - query: "`ORDER_PRICE_TYPE`.str.fullmatch('ICE', case=False, na=False)"
          value: true
        - query: "~`ORDER_PRICE_TYPE`.str.fullmatch('ICE', case=False, na=False)"
          value: false
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result
    - taskName: MapValue
      mapped: true
      key: map_value
    - taskName: MapAttribute
      mapped: true
      key: map_attribute
    - taskName: ConvertDatetime
      mapped: true
      key: convert_date_time
    - taskName: ConcatAttributes
      mapped: true
      key: concat_attributes
    - taskName: ConvertMinorToMajor
      mapped: true
      key: convert_minor_to_major
    - taskName: GetTenantLEI
      mapped: true
      key: get_tenant_lei
    - taskName: ValidityTimePeriod
      mapped: true
      key: validity_time_period
- path: swarm_tasks.transform.steeleye.orders.identifiers.generic_order_party_identifiers:GenericOrderPartyIdentifiers
  name: PartyIdentifiers
  params:
    target_attribute: marketIdentifiers.parties
    executing_entity_identifier: __executing_entity_id__
    client_identifier: __counterparty_code__
    counterparty_identifier: __market_id__
    buyer_identifier: __executing_entity_id__
    seller_identifier: __market_id__
    use_buy_mask_for_buyer_seller: true
    trader_identifier: __entered_by__
    execution_within_firm_identifier: __execution_decision_id__
    buy_sell_side_attribute: transactionDetails.buySellIndicator
    investment_decision_within_firm_identifier: __investment_decision_id__
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result
    - taskName: MapValue
      mapped: true
      key: map_value
    - taskName: MapConditional
      mapped: true
      key: map_conditional
    - taskName: MapAttribute
      mapped: true
      key: map_attribute
- path: swarm_tasks.transform.steeleye.tr.identifiers.instrument.identifiers:InstrumentIdentifiers
  name: InstrumentIdentifiers
  params:
    asset_class_attribute: __asset_class__
    isin_attribute: DERIVED_ISIN_CODE
    option_strike_price_attribute: STRIKE_PRICE
    option_type_attribute: OPTION_TYPE
    underlying_symbol_attribute: EPIC_Code
    venue_attribute: DERIVED_VENUE
    currency_attribute: INSTRUMENT_TRADED_CCY
    retain_task_inputs: true
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result
    - taskName: MapConditional
      mapped: true
      key: map_conditional
- path: swarm_tasks.transform.steeleye.link.merge_market_identifiers:MergeMarketIdentifiers
  name: MergeMarketIdentifiers
  params:
    identifiers_path: marketIdentifiers
    instrument_path: marketIdentifiers.instrument
    parties_path: marketIdentifiers.parties
  upstreamTasks:
    - taskName: InstrumentIdentifiers
      mapped: true
      key: result
    - taskName: PartyIdentifiers
      mapped: true
      key: party_identifiers
  # primary frame concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: PrimaryFrameConcatenator
  params:
    orient: horizontal
  upstreamTasks:
  - taskName: ConvertDatetime
    mapped: true
    key: convert_date_time
  - taskName: ConvertMinorToMajor
    mapped: true
    key: convert_minor_major
  - taskName: MapAttribute
    mapped: true
    key: map_attribute
  - taskName: MapStatic
    mapped: true
    key: map_static
  - taskName: MapValue
    mapped: true
    key: map_value
  - taskName: MapConditional
    mapped: true
    key: buy_sell
  - taskName: PartyIdentifiers
    mapped: true
    key: party_identifiers
  - taskName: InstrumentIdentifiers
    mapped: true
    key: instrument_identifiers
  - taskName: MergeMarketIdentifiers
    mapped: true
    key: merge_market_ids
  - taskName: ConcatAttributes
    mapped: true
    key: concat_attributes
  - taskName: ValidityTimePeriod
    mapped: true
    key: validity_time_period
- path: swarm_tasks.transform.steeleye.meta.assign_meta_parent:AssignMetaParent
  name: ParentId
  params:
    parent_model_attribute: _order.__meta_model__
    parent_attributes_prefix: _order.
    target_attribute: orderState.__meta_parent__
  upstreamTasks:
  - taskName: PrimaryFrameConcatenator
    mapped: true
    key: result
- path: swarm_tasks.transform.steeleye.link.instrument:LinkInstrument
  name: LinkInstrument
  params:
    identifiers_path: marketIdentifiers.instrument
    venue_attribute: transactionDetails.ultimateVenue
    currency_attribute: transactionDetails.priceCurrency
  resources:
    ref_data_key: reference-data
    tenant_data_key: tenant-data
  upstreamTasks:
  - taskName: PrimaryFrameConcatenator
    mapped: true
    key: result

- path: swarm_tasks.transform.map.map_to_nested:MapToNested
  name: OverrideInstrumentName
  params:
    source_attribute: INSTRUMENT_DESCRIPTION
    nested_path: instrumentFullName
    target_attribute: instrumentDetails.instrument
  upstreamTasks:
    - taskName: LinkInstrument
      mapped: true
      key: result
    - taskName: BatchProducer
      mapped: true
      key: batch_producer

- path: swarm_tasks.transform.steeleye.link.parties:LinkParties
  name: LinkParties
  params:
    identifiers_path: marketIdentifiers.parties
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: PrimaryFrameConcatenator
    mapped: true
    key: result
- path: swarm_tasks.generic.instrument_fallback.instrument_fallback:InstrumentFallback
  name: InstrumentFallback
  params:
    instrument_fields_map:
      - source_field: __fb_expiry_dt__
        target_field: derivative.expiryDate
      - source_field: Exercise_Type
        target_field: derivative.optionExerciseStyle
      - source_field: __fb_option_type__
        target_field: derivative.optionType
      - source_field: __fb_price_multiplier__
        target_field: derivative.priceMultiplier
      - source_field: __fb_Strike_price__
        target_field: derivative.strikePrice
      - source_field: __fb_Strike_price_ccy__
        target_field: derivative.strikePriceCurrency
      - source_field: __fb_instrumentIdCodeType__
        target_field: ext.instrumentIdCodeType
      - source_field: __fb_price_notation__
        target_field: ext.priceNotation
      - source_field: __fb_quantity_notation__
        target_field: ext.quantityNotation
      - source_field: __fb_ext_strikePriceType__
        target_field: ext.strikePriceType
      - source_field: ISIN_Code
        target_field: instrumentIdCode
      - source_field: INSTRUMENT_DESCRIPTION
        target_field: instrumentFullName
      - source_field: CFI_CODE
        target_field: instrumentClassification
      - source_field: __fb_is_created_through_fallback__
        target_field: isCreatedThroughFallback
    cfi_and_bestex_from_instrument_classification: true
    str_to_bool_dict:
      "true": True
      "y": True
      "yes": True
      "t": True
      "on": True
      "false": False
      "n": False
      "no": False
      "f": False
      "off": False
  upstreamTasks:
    - taskName: OverrideInstrumentName
      mapped: true
      key: override_instrument_name
    - taskName: PrimaryFrameConcatenator
      mapped: true
      key: primary_frame_concatenator
    - taskName: BatchProducer
      mapped: true
      key: result
- path: swarm_tasks.order.generic.parties_fallback:PartiesFallback
  name: PartiesFallback
  resources:
    es_client_key: tenant-data
  params:
    buyer_attribute: __executing_entity_id_fallback__
    seller_attribute: DERIVED_MARKET_ID
    use_buy_mask_for_buyer_seller: true
    buy_sell_side_attribute: transactionDetails.buySellIndicator
    trader_attribute: DERIVED_ENTERED_BY
    client_attribute: DERIVED_COUNTERPARTY_CODE
    execution_within_firm_attribute: DERIVED_EXECUTION_DECISION
    investment_decision_maker_attribute: DERIVED_INVESTMENT_DECISION
    executing_entity_attribute: __executing_entity_id_fallback__
    counterparty_attribute: DERIVED_MARKET_ID
  upstreamTasks:
    - taskName: BatchProducer
      mapped: true
      key: result
    - taskName: PrimaryFrameConcatenator
      mapped: true
      key: primary_frame_concatenator
    - taskName: LinkParties
      mapped: true
      key: parties
- path: swarm_tasks.transform.map.map_attribute:MapAttribute
  name: AuxiliaryMapAttributes
  paramsList:
  - source_attribute: __validity_period__
    target_attribute: executionDetails.validityPeriod
    cast_to: string.list
    list_delimiter: ;
  - source_attribute: __timestamps_validity_period__
    target_attribute: timestamps.validityPeriod
    cast_to: string.list
    list_delimiter: ;
  - source_attribute: reportDetails.transactionRefNo
    target_attribute: orderState.orderIdentifiers.transactionRefNo
  - source_attribute: _order.id
    target_attribute: orderState.id
  - source_attribute: _order.id
    target_attribute: orderIdentifiers.orderIdCode
  - source_attribute: _order.buySell
    target_attribute: orderState.buySell
  - source_attribute: transactionDetails.tradingDateTime
    target_attribute: timestamps.tradingDateTime
  - source_attribute: transactionDetails.buySellIndicator
    target_attribute: executionDetails.buySellIndicator
  - source_attribute: orderState.transactionDetails.price
    target_attribute: orderState.priceFormingData.price
  - source_attribute: transactionDetails.quantity
    target_attribute: priceFormingData.tradedQuantity
  upstreamTasks:
    - taskName: PrimaryFrameConcatenator
      mapped: true
      key: result
# Auxiliary frame concatenator
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: AuxiliaryFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
    - __asset_class__
    - marketIdentifiers.instrument
    - marketIdentifiers.parties
    - __fb_expiry_dt__
    - __gross_fill_price__
    - __fim_code__
    - __sedol_code__
    - __market_id_for_addl_info__
    - __reason__
    - __price_qualifier__
    - __event_type__
    - __book_view_vode__
    - __alternate_ref__
    - __execution_decision__
    - __investment_decision__
    - __validity_period__
    - __fb_option_type__
    - __derived_transaction_ref_no__
    - __fb_is_created_through_fallback__
    - __fb_price_multiplier__
    - __fb_instrumentIdCodeType__
    - __fb_price_notation__
    - __fb_quantity_notation__
    - __fb_ext_strikePriceType__
    - __asset_class__
    - __expiry_dt__
    - __trading_entity_id__
    - __entered_by__
    - __execution_decision_id__
    - __investment_decision_id__
    - __counterparty_code__
    - __market_id__
    - __timestamps_validity_period__
    - __fb_Strike_price__
    - __fb_Strike_price_ccy__
    - __executing_entity_id_fallback__
    - __executing_entity_id__
    - __limit_price__
    - __stop_price__
    - __tenant_lei__
    - __tenant_lei_with_prefix__
    - __counterparty_id_without_prefix__
    - __counterparty_id__
    - asset_class_attribute # Instrument Identifiers columns
    - bbg_figi_id_attribute
    - currency_attribute
    - eurex_id_attribute
    - exchange_symbol_attribute
    - expiry_date_attribute
    - interest_rate_start_date_attribute
    - isin_attribute
    - notional_currency_1_attribute
    - notional_currency_2_attribute
    - option_strike_price_attribute
    - option_type_attribute
    - swap_near_leg_date_attribute
    - underlying_index_name_attribute
    - underlying_index_name_leg_2_attribute
    - underlying_index_series_attribute
    - underlying_index_term_attribute
    - underlying_index_term_value_attribute
    - underlying_index_version_attribute
    - underlying_isin_attribute
    - underlying_symbol_attribute
    - underlying_symbol_expiry_code_attribute
    - underlying_index_term_leg_2_attribute
    - underlying_index_term_value_leg_2_attribute
    - venue_attribute
    - venue_financial_instrument_short_name_attribute
    - instrument_classification_attribute
  upstreamTasks:
  - taskName: AuxiliaryMapAttributes
    mapped: true
    key: auxiliary_map_attributes
  - taskName: PrimaryFrameConcatenator
    mapped: true
    key: primary_frame_concatenator
  - taskName: ParentId
    mapped: true
    key: parent_id
  - taskName: PartiesFallback
    mapped: true
    key: parties_fallback
  - taskName: InstrumentFallback
    mapped: true
    key: instrument_fallback
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderRecords
  params:
    except_prefix: orderState.
    strip_prefix: true
  upstreamTasks:
  - taskName: AuxiliaryFrameConcatenator
    mapped: true
    key: result
- path: swarm_tasks.transform.frame.frame_splitter:FrameSplitter
  name: OrderStateRecords
  params:
    except_prefix: _order.
    strip_prefix: true
  upstreamTasks:
  - taskName: AuxiliaryFrameConcatenator
    mapped: true
    key: result
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrder
  params:
    action: strip
    prefix: _order.
  upstreamTasks:
  - taskName: OrderRecords
    mapped: true
    key: result
- path: swarm_tasks.transform.frame.frame_column_manipulator:FrameColumnManipulator
  name: StripPrefixOrderState
  params:
    action: strip
    prefix: orderState.
  upstreamTasks:
  - taskName: OrderStateRecords
    mapped: true
    key: result
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: VerticalConcatenator
  params:
    orient: vertical
    reset_index: true
    drop_index: true
  upstreamTasks:
  - taskName: StripPrefixOrder
    mapped: true
    key: order_records
  - taskName: StripPrefixOrderState
    mapped: true
    key: order_state_records
- path: swarm_tasks.transform.frame.get_rows_by_condition:GetRowsByCondition
  name: RemoveInvalidOrderStates
  params:
    query: "`executionDetails.orderStatus`.notnull()"
  upstreamTasks:
  - taskName: VerticalConcatenator
    mapped: true
    key: result
- path: swarm_tasks.order.generic.remove_duplicate_newo:RemoveDuplicateNEWO
  name: RemoveDupNEWO
  params:
    newo_in_file_col: __newo_in_file_col__
    drop_newo_in_file_col: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: RemoveInvalidOrderStates
    mapped: true
    key: result
# Compute Best-execution
- path: swarm_tasks.transform.steeleye.orders.best_execution.best_execution:BestExecution
  name: BestExecution
  resources:
    es_client_key: reference-data
  upstreamTasks:
  - taskName: RemoveDupNEWO
    mapped: true
    key: result
# Concatenate Best-Execution Result
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: BestExecutionConcatenator
  params:
    orient: horizontal
  upstreamTasks:
  - taskName: BestExecution
    mapped: true
    key: best_execution
  - taskName: RemoveDupNEWO
    mapped: true
    key: remove_dup_newo
- path: swarm_tasks.transform.steeleye.meta.assign_meta:AssignMeta
  name: Meta
  params:
    model_attribute: __meta_model__
    parent_attribute: __meta_parent__
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: BestExecutionConcatenator
    mapped: true
    key: result
- path: swarm_tasks.transform.frame.frame_concatenator:FrameConcatenator
  name: FinalFrameConcatenator
  params:
    orient: horizontal
    drop_columns:
    - __meta_model__
    - __meta_parent__
  upstreamTasks:
  - taskName: Meta
    mapped: true
    key: result
  - taskName: BestExecutionConcatenator
    mapped: true
    key: best_ex_concatenator
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: ElasticBulkTransformer
  params:
    action_type: create
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: FinalFrameConcatenator
    mapped: true
    key: transform_result
  - taskName: BatchProducer
    mapped: true
    key: producer_result
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: PutIfAbsent
  params:
    payload_size: 10000000
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: ElasticBulkTransformer
    mapped: true
    key: result
# Instrument Mapper
- path: swarm_tasks.steeleye.orders.instrument_mapper.instrument_mapper:InstrumentMapper
  name: InstrumentMapper
  upstreamTasks:
    - taskName: FinalFrameConcatenator
      mapped: true
      key: source_frame
    - taskName: PutIfAbsent
      mapped: true
      key: bulk_writer_result
- path: swarm_tasks.io.write.quarantine_condition:QuarantineCondition
  name: QuarantineCondition
  upstreamTasks:
  - taskName: PutIfAbsent
    mapped: true
    key: bulk_writer_result
- path: swarm_tasks.io.write.elastic_bulk_transformer:ElasticBulkTransformer
  name: QuarantinedElasticBulkTransformer
  params:
    action_type: create
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: FinalFrameConcatenator
    mapped: true
    key: transform_result
  - taskName: BatchProducer
    mapped: true
    key: producer_result
  - taskName: PutIfAbsent
    mapped: true
    key: bulk_writer_result
- path: swarm_tasks.io.write.elastic_bulk_writer:ElasticBulkWriter
  name: QuarantinedPutIfAbsent
  params:
    payload_size: 10000000
    quarantined: true
  resources:
    es_client_key: tenant-data
  upstreamTasks:
  - taskName: QuarantinedElasticBulkTransformer
    mapped: true
    key: result
- path: swarm_tasks.control_flow.noop:Noop
  name: Noop
