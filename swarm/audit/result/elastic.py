import logging
import os
from dataclasses import asdict
from dataclasses import dataclass
from pathlib import Path
from typing import List
from typing import Optional
from typing import Union

import humanize
import pandas as pd
import yaml
from prefect.engine.results import LocalResult
from prefect.utilities.collections import DotDict

from swarm.schema.task.static import ResultCategory
from swarm.task.io.write.elastic.result import ElasticBulkWriterResult
from swarm.task.io.write.elastic.static import BulkWriterColumns
from swarm.task.io.write.elastic.static import WriteStatus


logger = logging.getLogger(__name__)


@dataclass
class WriteCounts:
    created: Optional[int] = 0
    deleted: Optional[int] = 0
    updated: Optional[int] = 0
    errored: Optional[int] = 0
    duplicate: Optional[int] = 0
    quarantined: Optional[int] = 0
    version_conflict: Optional[int] = 0
    total: Optional[int] = 0


@dataclass
class ModelStats:
    model_name: str
    counts: WriteCounts


@dataclass
class ElasticResult:
    errors: Optional[pd.DataFrame] = pd.DataFrame()
    duplicates: Optional[pd.DataFrame] = pd.DataFrame()
    version_conflicts: Optional[pd.DataFrame] = pd.DataFrame()
    model_stats: Optional[List[ModelStats]] = None
    total_counts: Optional[WriteCounts] = None
    payload: Optional[int] = 0
    empty: bool = False


class ElasticResultAggregator:
    @staticmethod
    def execute(context: DotDict) -> ElasticResult:
        # elastic results dir
        elastic_results_dir = Path(context.get("results_dir")).joinpath(
            ResultCategory.ELASTIC.value
        )

        # consolidate elastic results
        consolidated_result = ElasticResultAggregator.consolidate_results(
            output_path=elastic_results_dir
        )

        # short-circuit if no results
        if not consolidated_result or consolidated_result.frame.empty:
            return ElasticResult(empty=True)

        # summarise into elastic result
        elastic_result = ElasticResultAggregator.summarize(
            consolidated_result=consolidated_result
        )

        # consolidate write error stats for logging
        writer_error_stats = list()
        if not elastic_result.errors.empty:
            for error_type, frame in elastic_result.errors.groupby(
                by=[BulkWriterColumns.ERROR_TYPE]
            ):
                error_reasons = frame[BulkWriterColumns.ERROR_REASON].unique().tolist()
                error_class_stat = dict(errorClass=error_type, values=error_reasons)
                writer_error_stats.append(error_class_stat)

        # compose elastic result logging message
        message = dict(
            payload=humanize.naturalsize(elastic_result.payload),
            counts=asdict(elastic_result.total_counts),
            errors=writer_error_stats,
        )
        logger.info(f"elastic results:\n{yaml.dump(message)}")

        return elastic_result

    @staticmethod
    def summarize(
        consolidated_result: ElasticBulkWriterResult,
    ) -> ElasticResult:
        # extract relevant data for errors
        error_columns = [
            BulkWriterColumns.RAW_INDEX,
            BulkWriterColumns.MODEL,
            BulkWriterColumns.ID,
            BulkWriterColumns.ERROR_TYPE,
            BulkWriterColumns.ERROR_REASON,
            BulkWriterColumns.ERROR_CAUSED_BY_TYPE,
            BulkWriterColumns.ERROR_CAUSED_BY_REASON,
        ]
        errors = consolidated_result.frame.loc[
            (consolidated_result.frame.status == WriteStatus.ERRORED)
        ][error_columns]

        # extract relevant data for version conflicts
        conflict_columns = [
            BulkWriterColumns.RAW_INDEX,
            BulkWriterColumns.MODEL,
            BulkWriterColumns.ID,
            BulkWriterColumns.ERROR_TYPE,
            BulkWriterColumns.ERROR_REASON,
        ]
        version_conflicts = consolidated_result.frame.loc[
            (consolidated_result.frame.status == WriteStatus.VERSION_CONFLICT),
            conflict_columns,
        ]

        # extract relevant data for duplicates
        dupe_columns = [
            BulkWriterColumns.RAW_INDEX,
            BulkWriterColumns.MODEL,
            BulkWriterColumns.ID,
        ]
        dupes = consolidated_result.frame.loc[
            (consolidated_result.frame.status == WriteStatus.DUPLICATE), dupe_columns
        ]

        # summary model stats
        total_value_counts = consolidated_result.frame.status.value_counts().to_dict()
        ElasticResultAggregator.sum_counts(total_value_counts)
        total_counts = WriteCounts(**total_value_counts)

        model_stats = list()
        for model, df in consolidated_result.frame.groupby(
            by=[BulkWriterColumns.MODEL]
        ):
            model_counts = df.status.value_counts().to_dict()
            ElasticResultAggregator.sum_counts(model_counts)
            counts = WriteCounts(**model_counts)
            stats = ModelStats(model_name=model, counts=counts)
            model_stats.append(stats)

        elastic_result = ElasticResult(
            errors=errors,
            version_conflicts=version_conflicts,
            duplicates=dupes,
            total_counts=total_counts,
            model_stats=model_stats,
            payload=consolidated_result.total_bytes,
        )

        return elastic_result

    @staticmethod
    def sum_counts(write_counts: dict) -> None:
        total = 0
        total += write_counts.get("created", 0)
        total += write_counts.get("updated", 0)
        total += write_counts.get("deleted", 0)
        total += write_counts.get("errored", 0)
        total += write_counts.get("duplicate", 0)
        total += write_counts.get("quarantined", 0)
        total += write_counts.get("version_conflict", 0)
        write_counts["total"] = total

    @staticmethod
    def consolidate_results(output_path: str) -> Union[ElasticBulkWriterResult, None]:
        results = list()
        for root, directory_names, file_names in os.walk(output_path):
            local_result = LocalResult(dir=root)
            for f in file_names:
                result = local_result.read(location=f)
                results.append(result.value)

        if not results:
            logger.info(f"no elastic results found in path: {output_path}")
            return None

        total_bytes = sum([result.total_bytes for result in results])
        quarantined = results[0].quarantined if results else None
        frame = pd.concat([result.frame for result in results])
        return ElasticBulkWriterResult(
            frame=frame, quarantined=quarantined, total_bytes=total_bytes
        )
