import logging
import os
from dataclasses import dataclass
from pathlib import Path
from typing import List
from typing import Op<PERSON>
from typing import Union

import pandas as pd
from prefect.utilities.collections import DotDict


logger = logging.getLogger(__name__)


@dataclass
class ErrorSummary:
    task: str
    message: str
    error: Optional[str] = None
    traceback: Optional[str] = None


@dataclass
class TaskAuditResult:
    errors: Optional[pd.DataFrame] = None
    summary: Optional[List[ErrorSummary]] = None
    empty: bool = False


class TaskAuditAggregator:
    @staticmethod
    def execute(context: DotDict) -> TaskAuditResult:
        # consolidate audit files
        audit_frame = TaskAuditAggregator.consolidate_audits(
            output_path=context.get("audits_dir")
        )

        # short-circuit if no audits
        if not isinstance(audit_frame, pd.DataFrame):
            return TaskAuditResult(empty=True)

        # agg audit error types
        error_types = TaskAuditAggregator.aggregate_audit_errors(
            audit_frame=audit_frame
        )

        audit_result = TaskAuditResult(errors=audit_frame, summary=error_types)

        return audit_result

    @staticmethod
    def consolidate_audits(output_path: str) -> Union[pd.DataFrame, None]:
        output_files = []
        for root, directory_names, file_names in os.walk(output_path):
            for f in file_names:
                output_file = Path(root).joinpath(f)
                output_files.append(output_file)

        if not output_files:
            logger.info(f"no task audits found in path: {output_path}")
            return None

        results = list()
        for idx, output_file in enumerate(output_files):
            logger.info(f"processing audit file {output_file}")
            df = pd.read_csv(filepath_or_buffer=output_file)
            results.append(df)

        audit_frame = pd.concat(results)

        return audit_frame

    @staticmethod
    def aggregate_audit_errors(audit_frame: pd.DataFrame) -> List[ErrorSummary]:
        summaries = list()
        if len(audit_frame.index) > 0:
            for task, task_frame in audit_frame.groupby(by=["task_name"]):
                for message, msg_frame in task_frame.groupby(by=["message"]):
                    if "ctx.error" in msg_frame.columns:
                        for error, error_frame in msg_frame.groupby(by=["ctx.error"]):
                            traceback = msg_frame.loc[:, "ctx.traceback"].dropna()
                            if traceback.index.empty:
                                traceback = None
                            else:
                                traceback = traceback.iloc[0]
                            error_summary = ErrorSummary(
                                task=task,
                                message=message,
                                error=error,
                                traceback=traceback,
                            )
                            summaries.append(error_summary)
                    else:
                        error_summary = ErrorSummary(
                            task=task, message=message, error="", traceback=""
                        )
                        summaries.append(error_summary)
        return summaries
