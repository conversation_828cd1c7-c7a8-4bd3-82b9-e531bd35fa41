import logging
import os
from dataclasses import dataclass
from pathlib import Path
from typing import List
from typing import Optional
from typing import Union

import humanize
import yaml
from prefect.engine.results import LocalResult
from prefect.utilities.collections import DotDict

from swarm.schema.task.static import ResultCategory
from swarm.task.io.write.s3.result import S3Action
from swarm.task.io.write.s3.result import S3File
from swarm.task.io.write.s3.result import S3TargetResult

logger = logging.getLogger(__name__)


@dataclass
class S3Result:
    uploaded: Optional[List[S3File]] = None
    downloaded: Optional[List[S3File]] = None
    total_bytes: Optional[int] = 0
    empty: bool = False


class S3ResultAggregator:
    @staticmethod
    def execute(context: DotDict) -> S3Result:
        # s3 results dir
        s3_results_dir = Path(context.get("results_dir")).joinpath(
            ResultCategory.S3.value
        )

        # consolidate s3 results
        s3_result = S3ResultAggregator.consolidate_results(output_path=s3_results_dir)

        # short-circuit if no results
        if not s3_result:
            return S3Result(empty=True)

        # compose s3 result logging message
        message = dict(
            payload=humanize.naturalsize(s3_result.total_bytes),
            uploaded=len(s3_result.uploaded),
        )
        logger.info(f"s3 results:\n{yaml.dump(message)}")

        return s3_result

    @staticmethod
    def consolidate_results(output_path: str) -> Union[S3Result, None]:
        results: List[S3TargetResult] = list()
        for root, directory_names, file_names in os.walk(output_path):
            local_result = LocalResult(dir=root)
            for f in file_names:
                logger.info(f"reading results file present on path: {output_path}")
                result = local_result.read(location=f)
                # reading the s3 result files from output_path that contain List of S3TargetResult objects
                if result.value and result.value.targets:
                    logger.info(f"Removing all empty S3TargetResult from targets")
                    result.value.targets = [
                        target for target in result.value.targets if target
                    ]
                    logger.info(
                        f"checking targets list is empty or not {len(result.value.targets)} for results"
                    )
                    if len(result.value.targets) > 0:
                        results.append(result.value)

        if not results:
            logger.info(f"no s3 results found in path: {output_path}")
            return None

        total_bytes = 0
        uploaded = list()
        downloaded = list()
        for result in results:
            total_bytes += sum([target.bytes for target in result.targets])
            uploaded.extend([t for t in result.targets if t.action == S3Action.UPLOAD])
            downloaded.extend(
                [t for t in result.targets if t.action == S3Action.DOWNLOAD]
            )

        return S3Result(
            uploaded=uploaded, downloaded=downloaded, total_bytes=total_bytes
        )
