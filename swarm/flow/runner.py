import json
import logging
import os
import traceback
import warnings
from typing import Op<PERSON>
from typing import Tuple

import addict
import click
import pandas as pd
import pkg_resources
import prefect
import yaml
from prefect.executors import LocalDaskExecutor
from se_elastic_schema.models import TenantConfiguration
from se_schema_meta import EXPIRY
from se_schema_meta import ID
from se_schema_meta import MODEL

from swarm.audit.email.email_report import EmailReport
from swarm.audit.flow.flow_auditor import <PERSON>tR<PERSON>ult
from swarm.audit.flow.flow_auditor import FlowAuditor
from swarm.audit.sink.sink_file_auditor import SinkFileAuditor
from swarm.audit.slack.slack_report import SlackReport
from swarm.conf import Settings
from swarm.flow.static import FlowEnvVar
from swarm.flow.workflow import Workflow
from swarm.utilities.slack import get_swarm_alerts_slack_channel
from swarm.utilities.slack import send_raw_message
from swarm.utilities.slack import TOKEN
from swarm.utilities.time_util import get_date_time_utcnow
from swarm.utilities.time_util import get_utc_time_diff

# Set the general logging level and format for all loggers enabled in this process
logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] %(levelname)-5.5s %(name)s | %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

# Filter out warnings emitted by 3rd party packages.
# Controlled by DEBUG env var in case it is to be
# enabled for certain use-cases.
if os.getenv("DEBUG", "False").lower() == "false":
    warnings.filterwarnings(action="ignore")

# Override the default Prefect logger to use the same logging level and format across
# all Prefect Tasks and our own libs i.e. se-core-tasks
prefect.utilities.logging.prefect_logger = logger

# This default worker is limited to 3 only for Horizontally scaling flows
DEFAULT_PREFECT_WORKERS = 3
MAX_PREFECT_TASKS = os.environ.get("MAX_PREFECT_TASKS", None)


class FlowRunner:
    def __init__(
        self,
        flow_id: Optional[str] = None,
        local_flow: Optional[dict] = None,
        file_url: Optional[str] = None,
        audit_id: Optional[str] = None,
        email_notification: Optional[bool] = False,
        use_dask_executor: Optional[bool] = True,
        registry: Optional[object] = None,
        *args,
        **kwargs,
    ):
        if not (flow_id or local_flow):
            raise ValueError("require either flow_id or flow")

        self.max_prefect_tasks = None
        self.flow_id = flow_id
        self.local_flow = local_flow
        self.file_url = file_url
        self.audit_id = audit_id
        self.stack = None
        self.use_dask_executor = use_dask_executor
        self.email_notification = email_notification

        self.registry = registry is None and Settings.registry or registry

        self.workflow, self.params, self.client_id = self.compose_flow()
        self.version = self.workflow.config.bundle.image.split(":")[-1]
        self.sink_auditor = None

    def execute(self):
        self.on_processing()
        executor = (
            LocalDaskExecutor(
                scheduler="threads",
                num_workers=self.max_prefect_tasks,
            )
            if self.use_dask_executor
            else None
        )
        if self.use_dask_executor:
            logger.info(
                f"Running flow with max {self.max_prefect_tasks} concurrent prefect tasks"
            )

        state = self.workflow.run(parameters=self.params, executor=executor)

        audit_result = FlowAuditor.execute(state=state, context=self.workflow.context)

        if state.is_failed():
            self.on_failed(audit_result=audit_result)
            raise Exception(
                "Flow failed.\n" + json.dumps(audit_result.get_failed_tasks())
            )
        else:
            self.on_processed(audit_result=audit_result)

    def on_processing(self):
        if self.audit_id:
            resource_config = Settings.connections.get_config("tenant-data")
            self.sink_auditor = SinkFileAuditor(
                audit_id=self.audit_id, config=resource_config, tenant=self.client_id
            )

            self.sink_auditor.on_processing()

    def on_processed(self, audit_result: AuditResult):
        if self.audit_id:
            try:
                self.sink_auditor.on_processed(result=audit_result)
            except Exception:
                logger.exception("sink auditor failed")

        # avoid polling-like flows that spam
        # todo revisit for alternative
        if not audit_result.empty:
            try:
                slack_report = SlackReport(
                    audit_result=audit_result,
                    workflow=self.workflow,
                    parameters=self.params,
                )
                slack_report.publish()
            except Exception:
                logger.exception("slack report failed")

    def on_failed(self, audit_result: AuditResult):
        if self.audit_id:
            try:
                self.sink_auditor.on_errored(result=audit_result)
            except Exception:
                logger.exception("sink auditor failed")

        if self.email_notification:
            try:
                email_report = EmailReport(
                    audit_result=audit_result,
                    workflow=self.workflow,
                    parameters=self.params,
                    recipients=["<EMAIL>"],
                )
                email_report.send()
            except Exception:
                logger.exception("email report failed")

        try:
            slack_report = SlackReport(
                audit_result=audit_result,
                workflow=self.workflow,
                parameters=self.params,
            )
            slack_report.publish()
        except Exception:
            logger.exception("slack report failed")

    def compose_flow(self) -> Tuple[Workflow, dict, str]:
        # extract client id from realm
        client_id, _ = self.flow_id.split(".", 1)
        # fetch client given client id
        client = self.registry.get_client(client_id=client_id)

        # determine stack based on client realm
        realm, _ = self.flow_id.split(":", 1)
        self.stack = client["stacks"][realm]

        # fetch flow given flow id or local flow if passed
        flow = (
            self.local_flow
            if self.local_flow
            else self.registry.get_flow(flow_id=self.flow_id, stack=self.stack)
        )
        self.max_prefect_tasks = self._get_max_prefect_tasks(
            flow=flow, default_worker_for_horizontal_scaling=DEFAULT_PREFECT_WORKERS
        )
        self._validate_horizontal_task_params(flow=flow)

        # assign platform flow flag
        platform_flow = flow["bundle"].get("platform", False) and flow[
            "realm"
        ].startswith("platform")

        if platform_flow:
            logger.info(f"operating {flow['bundle']['name']} as platform flow")

        # align params
        params = dict()
        for param in flow["bundle"].get("parameters", list()):
            params[param["name"]] = os.environ.get(param["envVar"])

        logger.info(f"compose flow for {client['id']} on {self.stack} stack")

        # fetch tenant config for non platform bundles
        tenant_configuration = (
            self._fetch_tenant_config(realm=realm) if not platform_flow else None
        )

        # construct workflow
        workflow = Workflow(
            flow_config=flow,
            client=client,
            tenant_configuration=tenant_configuration,
        )

        # return workflow and params to be used during execution
        return workflow, params, client_id

    def _fetch_tenant_config(
        self, realm: str
    ) -> Optional[addict.Dict]:  # pragma no cover

        tenant, _ = realm.split(".", 1)

        es_client = Settings.connections.get("tenant-data")

        self.registry.get_stack_infrastructure(stack=Settings.STACK, env=Settings.env)
        body = {
            "query": {
                "bool": {
                    "filter": [
                        {"term": {MODEL: "TenantConfiguration"}},
                        {"term": {ID: realm}},
                    ],
                    "must_not": {"exists": {"field": EXPIRY}},
                }
            }
        }

        response = es_client.search(
            query=body, index=TenantConfiguration.get_elastic_index_alias(tenant=tenant)
        )

        hit = response["hits"]["hits"]

        if not hit:
            raise ValueError(f"Tenant Configuration for {realm} not found.")

        elif len(hit) > 1:
            raise ValueError(f"{len(hit)} Tenant Configurations found for {realm}")

        tenant_configuration = addict.Dict(hit[0]["_source"])

        return tenant_configuration

    @staticmethod
    def _get_max_prefect_tasks(
        flow: dict, default_worker_for_horizontal_scaling: int
    ) -> Optional[int]:
        """
        This method determines when the local flow being run is a horizontally
        scaling flow or not. This is done by checking for the presence of the
        HorizontalBatchController task in both the flows tasks, and the
        controlFlows.
        If the flow seems to be a horizontally scaling flow, the max_prefect_tasks
        is returned as the default
        :param flow: the flow being run
        :param default_worker_for_horizontal_scaling: the default max concurrent prefect tasks
        :return:
        """
        if MAX_PREFECT_TASKS:
            return int(MAX_PREFECT_TASKS)
        logger.info("No env variable found for MAX_PREFECT_TASKS")
        tasks_df = pd.DataFrame(flow.get("bundle", {}).get("tasks"))
        control_flows_df = pd.DataFrame(flow.get("bundle", {}).get("controlFlows"))
        if control_flows_df.empty or tasks_df.empty:
            return
        horizontal_scaling_task = (
            tasks_df["path"].astype("string").str.endswith(":HorizontalBatchController")
        )
        if not horizontal_scaling_task.any():
            return

        horizontal_scaling_task_name = tasks_df[horizontal_scaling_task][
            "name"
        ].to_list()
        if (
            control_flows_df["conditionTaskName"]
            .isin(horizontal_scaling_task_name)
            .any()
        ):
            return default_worker_for_horizontal_scaling

    @staticmethod
    def _validate_horizontal_task_params(flow: dict):
        """
        Validates whether the correct params are used in tasks required for horizontal scaling.
        This helps us avoid recursive upload scenario.
        (This only applies for flows using the horizontal scaling and not any other flows)

        If we fail this validation, the flow is not executed and a ValueError is raised.

        :param flow: the flow being run
        """
        tasks_df = pd.DataFrame(flow.get("bundle", {}).get("tasks"))
        control_flows_df = pd.DataFrame(flow.get("bundle", {}).get("controlFlows"))
        if control_flows_df.empty or tasks_df.empty:
            return
        horizontal_scaling_task_mask = tasks_df["path"].astype("string").str.endswith(
            ":HorizontalBatchController"
        ) | tasks_df["path"].astype("string").str.endswith(":MergeAndChunkCsvFiles")

        # Raise out a value error if HorizontalBatchController's max_chunk_size
        # is less than MergeAndChunkCsvFile's max_chunk_size.
        # We skip this validation if both of the tasks are not present.
        if (
            len(tasks_df[horizontal_scaling_task_mask]) == 2
            and tasks_df[horizontal_scaling_task_mask]["params"]
            .str.get("max_chunk_size")
            .diff()
            .fillna(0)
            .sum()
            > 0
        ):
            raise ValueError(
                "HorizontalBatchController max_chunk_size less than MergeAndChunkCsvFiles max_chunk_size. "
                "The flow will be stuck in an upload loop."
            )


@click.command()
@click.option("--flow_id", envvar=FlowEnvVar.SWARM_FLOW_ID)
@click.option("--file_url", envvar=FlowEnvVar.SWARM_FILE_URL)
@click.option(
    "--email_notification", envvar=FlowEnvVar.SWARM_EMAIL_NOTIFICATION, type=bool
)
@click.option("--flow_args", envvar=FlowEnvVar.SWARM_FLOW_ARGS)
@click.option("--audit_id", envvar=FlowEnvVar.SWARM_AUDIT_ID)
@click.option("--dask_executor", envvar=FlowEnvVar.SWARM_DASK_EXECUTOR, type=bool)
def main(
    flow_id: str = None,
    file_url: Optional[str] = None,
    flow_args: Optional[str] = None,
    audit_id: Optional[str] = None,
    dask_executor: Optional[bool] = True,
    email_notification: Optional[bool] = False,
):
    # todo not defaulting to true properly
    dask_executor = True if dask_executor is None else dask_executor
    email_notification = False if email_notification is None else email_notification

    # capture env vars of interest and log
    env_var_input = dict(
        flow_id=flow_id,
        file_url=file_url,
        audit_id=audit_id,
        email_notification=email_notification,
        use_dask_executor=dask_executor,
    )
    logger.info(f"run flow using:\n{yaml.dump(env_var_input)}")
    logger.info(f"SWARM_ORIGIN {os.environ.get(FlowEnvVar.SWARM_ORIGIN)}")

    if flow_args and len(flow_args) > 0:
        logger.info(f"additional flow args:\n" f"{flow_args}")

    logger.info(
        f"Environment Variables available: \n"
        f"{sorted([k for k,v in os.environ.items() if v])}"
    )
    logger.info(
        f"Python packages: \n "
        f"{dict(sorted({pkg.project_name:pkg.version for pkg in pkg_resources.working_set}.items()))}"
    )

    # require swarm flow id env var
    if not flow_id:
        raise ValueError(f"missing required env var {FlowEnvVar.SWARM_FLOW_ID}")

    # get flow start time, to measure flow duration
    flow_start_time = get_date_time_utcnow()

    try:
        # construct flow runner
        flow_runner = FlowRunner(**env_var_input)
        # execute flow runner
        flow_runner.execute()
    except Exception as e:
        logger.error(e)
        env_var_input["time_taken"] = get_utc_time_diff(flow_start_time)
        message = slack_notify_message(env_var_input, traceback.format_exc())
        send_raw_message(message)
        raise

    logger.info("complete")


def slack_notify_message(env_var_input: dict, exception_str: str):
    blocks = [
        {
            "type": "header",
            "text": {"type": "plain_text", "text": "Details", "emoji": True},
        },
        {"type": "divider"},
        {
            "type": "section",
            "fields": [
                {
                    "type": "mrkdwn",
                    "text": f"*flow_id*\n{env_var_input.get('flow_id', '')}",
                },
                {
                    "type": "mrkdwn",
                    "text": f"*time_taken*\n{env_var_input.get('time_taken', '')}",
                },
            ],
        },
        {
            "type": "section",
            "fields": [
                {
                    "type": "mrkdwn",
                    "text": f"*file_url*\n{env_var_input.get('file_url', '')}",
                },
                {
                    "type": "mrkdwn",
                    "text": f"*environment*\n{Settings.env}",
                },
            ],
        },
        {"type": "divider"},
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"*aws_env_vars*\n```{json.dumps(FlowAuditor.aws_env_vars())}```",
            },
        },
        {"type": "divider"},
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"*flow_details*\n```{json.dumps(env_var_input)}```",
            },
        },
        {"type": "divider"},
        {
            "type": "section",
            "text": {"type": "mrkdwn", "text": f"*traceback*\n```{exception_str}```"},
        },
    ]
    message = {
        "token": TOKEN,
        "channel": get_swarm_alerts_slack_channel(Settings),
        "text": f"Flow Runner Failed",
        "username": f"Flow Runner Failed",
        "blocks": blocks,
    }
    return message


if __name__ == "__main__":
    main()
