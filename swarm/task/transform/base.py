from abc import abstractmethod
from traceback import format_exc
from typing import Op<PERSON>
from typing import Union

import pandas as pd
from prefect.engine import signals

from swarm.schema.task.auditor.static import TaskMessage
from swarm.task.base import BaseTask
from swarm.task.io.read import FrameProducerResult
from swarm.task.transform.result import TransformResult
from swarm.task.utilities import pre_run


class TransformBaseTask(BaseTask):
    @pre_run
    def run(
        self,
        result: Optional[Union[FrameProducerResult, TransformResult]] = None,
        **kwargs,
    ) -> TransformResult:

        # todo rationalise result types (e.g., align path to frame)
        frame = (
            result.frame if isinstance(result, FrameProducerResult) else result.target
        )

        # create a new DataFrame instance (different memory pointer)
        # this is necessary to avoid Pandas multi-threading issues of
        # concurrent tasks accessing the same dataframe object
        # which might lead to unintended behavior and silent errors such as wrongly mapping data
        # Example of root issue:
        # https://steeleye.atlassian.net/wiki/spaces/EN/pages/1407844451
        # Ticket describing the solution:
        # https://steeleye.atlassian.net/browse/DE-575
        frame = frame.copy()  # TODO remove when not using LocalDaskExecutor

        if kwargs:
            # Concatenate horizontally additional frames passed as upstream tasks
            exclude_kwargs = []
            additional_frames = [frame]  # the result df at the beginning
            for key_, val_ in kwargs.items():
                if isinstance(val_, (FrameProducerResult, TransformResult)):
                    additional_frames.append(
                        val_.frame
                        if isinstance(val_, FrameProducerResult)
                        else val_.target
                    )
                    exclude_kwargs.append(key_)

            kwargs = {i: kwargs[i] for i in kwargs if i not in exclude_kwargs}
            if len(additional_frames) > 1:
                frame = pd.concat(additional_frames, axis=1)

        targets = []

        execution_args = [self.params] if self.params else self.params_list

        for params in execution_args:
            self.logger.info(f"Executing task for params: {params}")
            try:
                execute_result = self.execute(
                    source_frame=frame,
                    params=params,
                    resources=self.resources,
                    **kwargs,
                )
                targets.append(execute_result)

            except (signals.SKIP, signals.RETRY, signals.ENDRUN):
                raise

            except Exception as e:
                ctx = {"error": str(e)}
                if not isinstance(e, KeyError):
                    ctx["traceback"] = format_exc()

                self.logger.error(
                    f"Error on the Transform Task: {__class__.__name__}. Traceback:\n {format_exc()}"
                )

                self.auditor.add(
                    message=TaskMessage.EXECUTION_FAILED.format(
                        error=e.__class__.__name__
                    ),
                    ctx=ctx,
                )

        target = pd.concat(targets, axis=1) if targets else pd.DataFrame()

        return TransformResult(target, result.batch_index)

    @abstractmethod
    def execute(
        self, source_frame: pd.DataFrame = None, params=None, resources=None, **kwargs
    ) -> pd.DataFrame:
        raise NotImplementedError
