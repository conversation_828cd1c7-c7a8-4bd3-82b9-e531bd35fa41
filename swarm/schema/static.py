class Environment:
    DEV = "dev"
    UAT = "uat"
    PROD = "prod"


class Index:
    ABACI = ".abaci"
    SWARM = ".swarm"


class WriteAction:
    CREATE = "create"
    DELETE = "delete"
    UPDATE = "update"
    INDEX = "index"


class DocStatus:
    CREATED = "CREATED"
    DELETED = "DELETED"
    DUPLICATE = "DUPLICATE"
    ERRORED = "ERRORED"
    EXPIRED = "EXPIRED"
    VERSION_CONFLICT = "VERSION_CONFLICT"
    UPDATED = "UPDATED"


class SwarmColumns:
    SWARM_RAW_INDEX = "__swarm_raw_index__"


STATUS_MAP = {
    WriteAction.DELETE: DocStatus.DELETED,
    WriteAction.UPDATE: DocStatus.UPDATED,
    WriteAction.CREATE: DocStatus.CREATED,
}
