from datetime import timedelta
from typing import Callable
from typing import Iterable
from typing import List
from typing import Optional
from typing import Set

import prefect.triggers
from prefect.engine.result import Result
from prefect.engine.state import State
from pydantic import validator

from swarm.schema.base import AbstractComponent


TRIGGER_MAP = dict(
    all_successful=prefect.triggers.all_successful,
    all_failed=prefect.triggers.all_failed,
    any_successful=prefect.triggers.any_successful,
    any_failed=prefect.triggers.any_failed,
    manual_only=prefect.triggers.manual_only,
    always_run=prefect.triggers.always_run,
)


class PrefectParams(AbstractComponent):
    cache_for: timedelta = None
    cache_key: str = None
    cache_validator: Callable = None
    checkpoint: bool = None
    log_stdout: bool = False
    max_retries: int = None
    nout: Optional[int] = None
    on_failure: Callable = None
    result: Optional[Result] = None
    retry_delay: timedelta = None
    skip_on_upstream_skip: bool = True
    slug: str = None
    state_handlers: List[Callable] = None
    tags: Iterable[str] = None
    timeout: int = None
    trigger: Callable[[Set[State]], bool] = None

    @validator("trigger", pre=True)
    def assign_trigger(cls, v):
        if not v:
            return v

        fn = TRIGGER_MAP.get(v)
        if not fn:
            raise ValueError(f"no trigger registered for value {v}")

        return fn
