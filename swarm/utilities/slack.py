import json
import logging
import os

import requests

from swarm.conf import SettingsCls

logger = logging.getLogger(__name__)

TOKEN = os.environ.get(
    "SLACK_TOKEN", "********************************************************"
)

AVAILABLE_ENVIRONMENTS = ("prod", "uat", "dev", "benchmark", "sit")
DEFAULT_SLACK_CHANNEL_ENV = "uat"


def send_raw_message(message: dict):
    url = "https://slack.com/api/chat.postMessage"

    if "blocks" in message:
        message["blocks"] = json.dumps(message["blocks"])

    r = requests.post(url, data=message)
    j = r.json()
    if j["ok"]:
        return True

    logger.error(f"Slack report post failed:\n{j}")


def get_swarm_alerts_slack_channel(settings: SettingsCls) -> str:
    """
    Get swarm alerts channel based on the settings environment

    If for any reason the current environment does not exist,
    all notifications will be sent for the DEFAULT_SLACK_CHANNEL_ENV
    channel, which will work as a fallback channel

    :param settings: from which the environment should be extracted
    :return: swarm alert channel name
    """
    env = settings.env

    if env not in AVAILABLE_ENVIRONMENTS:
        env = DEFAULT_SLACK_CHANNEL_ENV

    _channel = "#swarm-alerts" if env == "prod" else f"#swarm-alerts-{env}"

    return os.environ.get("SWARM_ALERTS_CHANNEL", _channel)
