import logging
from typing import List
from typing import Optional
from typing import Union

from se_elasticsearch.repository import get_repository_by_cluster_version
from se_elasticsearch.repository import ResourceConfig

from swarm.client.sftp import SftpClient
from swarm.schema.flow.bundle.components import Resource


logger = logging.getLogger(__name__)


def get_es_repo(config: Optional[Union[ResourceConfig, List[ResourceConfig]]]):
    return get_repository_by_cluster_version(config)


CLIENT_MAP = {"ELASTICSEARCH": get_es_repo, "SFTP": SftpClient}


def instantiate_client(resource: Resource):

    client_func = CLIENT_MAP.get(resource.type)

    if client_func is None:
        raise NotImplementedError(f"Client not implemented for {resource.type}")

    client = client_func(config=resource.config)

    return client


def instantiate_platform_clients(resource: Resource):

    client_func = CLIENT_MAP.get(resource.type)

    if client_func is None:
        raise NotImplementedError(f"Client not implemented for {resource.type}")

    clients = []
    for config in resource.config:
        try:
            # if ElasticSearch client instantiation fails we still want the list
            # with the remaining valid clients

            clients.append(client_func(config=config))
        except ConnectionError:
            logger.error(
                f"instantiate_platform_clients failed for {config}", exc_info=True
            )
            continue

    return clients
